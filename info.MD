/yuayuuu
<EMAIL>

========================================
composer require ophimcms/theme-vieon-2025
php artisan vendor:publish --tag=theme-vieon-2025
php artisan cache:clear
php artisan view:clear
php artisan config:clear
php artisan route:clear
=========================================

php artisan migrate --path=packages/ophimcms/theme-vieon-2025/database/migrations

=======================================

php artisan vendor:publish --provider="Backpack\CRUD\BackpackServiceProvider" --tag="views"

=======================================
php artisan route:list --name=loading

php artisan theme-vieon-2025:seed-loading-themes

php artisan vendor:publish --tag=vieon-2025-assets --force

php artisan vendor:publish --tag=vieon-2025-lang --force

Email: <EMAIL>
Mật khẩu:!cFASv^mGn4%
