<?xml version="1.0" encoding="UTF-8"?><xsl:stylesheet version="2.0"
	xmlns:html="http://www.w3.org/TR/REC-html40"
	xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
	xmlns:sitemap="http://www.sitemaps.org/schemas/sitemap/0.9"
	xmlns:kml="http://www.opengis.net/kml/2.2"
	xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
	xmlns:atom="http://www.w3.org/2005/Atom">
	<xsl:output method="html" version="1.0" encoding="UTF-8" indent="yes"/>
	<xsl:template match="/">
		<html
			xmlns="http://www.w3.org/1999/xhtml">
			<head>
				<xsl:choose>
					<xsl:when test="kml:kml">
						<title>Locations Sitemap</title>
					</xsl:when>
					<xsl:otherwise>
						<title>XML Sitemap</title>
					</xsl:otherwise>
				</xsl:choose>
				<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
				<style type="text/css">
					body {
						font-size: 14px;
						font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;
						margin: 0;
						color: #545353;
					}
					a {
						color: #05809e;
						text-decoration: none;
					}
					h1 {
						font-size: 24px;
						font-family: Verdana,Geneva,sans-serif;
						font-weight: normal;
						margin: 0;
					}

					#description {
						background-color: #4275f4;
						padding: 20px 40px;
						color: #fff;
						padding: 30px 30px 20px;
					}
					#description h1,
					#description p,
					#description a {
						color: #fff;
						margin: 0;
						font-size: 1.1em;
					}
					#description h1 {
						font-size: 2em;
						margin-bottom: 1em;
					}
					#description p {
						margin-top: 5px;
					}
					#description a {
						border-bottom: 1px dotted;
					}

					#content {
						padding: 20px 30px;
						background: #fff;
						max-width: 75%;
						margin: 0 auto;
					}
					table {
						border: none;
						border-collapse: collapse;
						font-size: .9em;
						width: 100%;
					}
					th {
						background-color: #4275f4;
						color: #fff;
						text-align: left;
						padding: 15px 10px;
						font-size: 14px;
						cursor: pointer;
					}
					td {
						padding: 10px;
						border-bottom: 1px solid #ddd;
					}
					tbody tr:nth-child(even) {
						background-color: #f7f7f7;
					}
					table td a {
						display: block;
					}
					table td a img {
						max-height: 30px;
						margin: 6px 3px;
					}
				</style>
			</head>
			<body>
				<xsl:choose>
					<xsl:when test="kml:kml">
						<div id="description">
							<h1>KML File</h1>
							<p>
                                This KML File is generated by
								<a href="https://motchill.ac" target="_blank">Motchill  | Phim Hay | Xem Phim Online | Phim HD Vietsub | Xem Phim Nhanh | Phim Mới</a>. It is used to provide location information to Google.
							</p>
							<p>
								Learn more about
								<a href="https://developers.google.com/kml/documentation/" target="_blank">KML File</a>.
							</p>
						</div>
						<div id="content">
							<p class="expl">
								This KML file contains
								<xsl:value-of select="count(kml:kml/kml:Document/kml:Folder/kml:Placemark)"/> Locations.

							</p>
							<p class="expl">
								<a href="https://motchill.ac/sitemap.xml">&#8592; Sitemap Index</a>
							</p>
							<table id="sitemap" cellpadding="3">
								<thead>
									<tr>
										<th width="25%">Name</th>
										<th width="40%">Address</th>
										<th width="15%">Phone number</th>
										<th width="10%">Latitude</th>
										<th width="10%">Longitude</th>
									</tr>
								</thead>
								<tbody>
									<xsl:variable name="lower" select="'abcdefghijklmnopqrstuvwxyz'"/>
									<xsl:variable name="upper" select="'ABCDEFGHIJKLMNOPQRSTUVWXYZ'"/>
									<xsl:for-each select="kml:kml/kml:Document/kml:Folder/kml:Placemark">
										<tr>
											<td>
												<xsl:variable name="itemURL">
													<xsl:value-of select="atom:link/@href"/>
												</xsl:variable>
												<a href="{$itemURL}">
													<xsl:value-of select="kml:name"/>
												</a>
											</td>
											<td>
												<xsl:value-of select="kml:address"/>
											</td>
											<td>
												<xsl:value-of select="kml:phoneNumber"/>
											</td>
											<td>
												<xsl:value-of select="kml:LookAt/kml:latitude"/>
											</td>
											<td>
												<xsl:value-of select="kml:LookAt/kml:longitude"/>
											</td>
										</tr>
									</xsl:for-each>
								</tbody>
							</table>
						</div>
					</xsl:when>
					<xsl:otherwise>
						<div id="description">
							<h1>XML Sitemap</h1>
							<p>
                                This XML Sitemap is generated by
								<a href="https://motchill.ac" target="_blank">Motchill  | Phim Hay | Xem Phim Online | Phim HD Vietsub | Xem Phim Nhanh | Phim Mới</a>. It is what search engines like Google use to crawl and re-crawl posts/pages/products/images/archives on your website.
							</p>
							<p>
								Learn more about
								<a href="http://sitemaps.org" target="_blank">XML Sitemaps</a>.
							</p>
						</div>
						<div id="content">
							<xsl:if test="count(sitemap:sitemapindex/sitemap:sitemap) &gt; 0">
								<p>
									This XML Sitemap Index file contains
									<strong>
										<xsl:value-of select="count(sitemap:sitemapindex/sitemap:sitemap)"/>
									</strong> sitemaps.
								</p>
								<table id="sitemap" cellpadding="3">
									<thead>
										<tr>
											<th width="75%">Sitemap</th>
											<th width="25%">Last Modified</th>
										</tr>
									</thead>
									<tbody>
										<xsl:for-each select="sitemap:sitemapindex/sitemap:sitemap">
											<xsl:variable name="sitemapURL">
												<xsl:value-of select="sitemap:loc"/>
											</xsl:variable>
											<tr>
												<td>
													<a href="{$sitemapURL}">
														<xsl:value-of select="sitemap:loc"/>
													</a>
												</td>
												<td>
													<xsl:value-of select="concat(substring(sitemap:lastmod,0,11),concat(' ', substring(sitemap:lastmod,12,5)),concat(' ', substring(sitemap:lastmod,20,6)))"/>
												</td>
											</tr>
										</xsl:for-each>
									</tbody>
								</table>
							</xsl:if>
							<xsl:if test="count(sitemap:sitemapindex/sitemap:sitemap) &lt; 1">
								<p>
									This XML Sitemap contains
									<strong>
										<xsl:value-of select="count(sitemap:urlset/sitemap:url)"/>
									</strong> URLs.
								</p>
								<p class="expl">
									<a href="https://motchill.ac/sitemap.xml">&#8592; Sitemap Index</a>
								</p>
								<table id="sitemap" cellpadding="3">
									<thead>
										<tr>
											<th width="75%">URL</th>
											<th width="5%">Images</th>
											<th title="Last Modification Time" width="20%">Last Mod.</th>
										</tr>
									</thead>
									<tbody>
										<xsl:variable name="lower" select="'abcdefghijklmnopqrstuvwxyz'"/>
										<xsl:variable name="upper" select="'ABCDEFGHIJKLMNOPQRSTUVWXYZ'"/>
										<xsl:for-each select="sitemap:urlset/sitemap:url">
											<tr>
												<td>
													<xsl:variable name="itemURL">
														<xsl:value-of select="sitemap:loc"/>
													</xsl:variable>
													<a href="{$itemURL}">
														<xsl:value-of select="sitemap:loc"/>
													</a>
												</td>
												<td>
													<xsl:value-of select="count(image:image)"/>
												</td>
												<td>
													<xsl:value-of select="concat(substring(sitemap:lastmod,0,11),concat(' ', substring(sitemap:lastmod,12,5)),concat(' ', substring(sitemap:lastmod,20,6)))"/>
												</td>
											</tr>
										</xsl:for-each>
									</tbody>
								</table>
							</xsl:if>
						</div>
					</xsl:otherwise>
				</xsl:choose>
			</body>
		</html>
	</xsl:template>
</xsl:stylesheet>
