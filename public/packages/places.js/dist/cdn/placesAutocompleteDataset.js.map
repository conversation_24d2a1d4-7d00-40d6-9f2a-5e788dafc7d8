{"version": 3, "sources": ["webpack://[name]/webpack/universalModuleDefinition", "webpack://[name]/webpack/bootstrap", "webpack://[name]/./src/configure/index.js", "webpack://[name]/./src/places.css", "webpack://[name]/./node_modules/insert-css/index.js", "webpack://[name]/./src/createAutocompleteSource.js", "webpack://[name]/./src/createAutocompleteDataset.js", "webpack://[name]/./src/navigatorLanguage.js", "webpack://[name]/./src/version.js", "webpack://[name]/./src/icons/address.svg", "webpack://[name]/./autocompleteDataset.js", "webpack://[name]/./babel-css.js", "webpack://[name]/./src/formatInputValue.js", "webpack://[name]/./src/icons/city.svg", "webpack://[name]/./src/icons/country.svg", "webpack://[name]/./src/icons/bus.svg", "webpack://[name]/./src/icons/train.svg", "webpack://[name]/./src/icons/townhall.svg", "webpack://[name]/./src/icons/plane.svg", "webpack://[name]/./src/formatDropdownValue.js", "webpack://[name]/./src/icons/algolia.svg", "webpack://[name]/./src/icons/osm.svg", "webpack://[name]/./src/defaultTemplates.js", "webpack://[name]/./src/findCountryCode.js", "webpack://[name]/./src/findType.js", "webpack://[name]/./src/formatHit.js"], "names": ["extractParams", "hitsPerPage", "postcodeSearch", "aroundLatLng", "aroundRadius", "aroundLatLngViaIP", "insideBoundingBox", "insidePolygon", "getRankingInfo", "countries", "language", "type", "extracted", "navigator", "split", "Array", "isArray", "map", "country", "toLowerCase", "undefined", "restrictSearchableAttributes", "extractControls", "useDeviceLocation", "computeQueryParams", "params", "formatInputValue", "onHits", "onError", "e", "onRateLimitReached", "onInvalidCredentials", "controls", "configure", "configuration", "createAutocompleteSource", "algoliasearch", "clientOptions", "<PERSON><PERSON><PERSON><PERSON>", "appId", "placesClient", "initPlaces", "as", "addAlgoliaAgent", "version", "userCoords", "tracker", "geolocation", "watchPosition", "coords", "latitude", "longitude", "searcher", "query", "cb", "searchParams", "search", "then", "content", "hits", "hit", "hitIndex", "formatHit", "rawAnswer", "statusCode", "message", "partial", "updated", "clearWatch", "createAutocompleteDataset", "options", "templates", "defaultTemplates", "source", "value", "displayKey", "name", "cache", "userLanguage", "replace", "String", "prototype", "toUpperCase", "require", "css", "insertCss", "prepend", "module", "exports", "administrative", "city", "out", "trim", "icons", "address", "addressIcon", "cityIcon", "countryIcon", "busStop", "busIcon", "trainStation", "trainIcon", "townhall", "townhallIcon", "airport", "planeIcon", "formatDropdownValue", "highlight", "filter", "token", "join", "footer", "algoliaLogo", "osmLogo", "suggestion", "findCountryCode", "tags", "tagIndex", "length", "tag", "find", "match", "findType", "types", "t", "indexOf", "getBestHighlightedForm", "<PERSON><PERSON><PERSON><PERSON>", "defaultValue", "bestAttributes", "i", "matchLevel", "push", "index", "words", "matched<PERSON>ords", "sort", "a", "b", "getBestPostcode", "postcodes", "highlightedPostcodes", "postcode", "highlightedPostcode", "locale_names", "suburb", "county", "_highlightResult", "countryCode", "_tags", "latlng", "lat", "_geoloc", "lng", "console", "error"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;QCVA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;;;;;;;;AClFA,IAAMA,aAAa,GAAG,SAAhBA,aAAgB,OAYhB;AAAA,MAXJC,WAWI,QAXJA,WAWI;AAAA,MAVJC,cAUI,QAVJA,cAUI;AAAA,MATJC,YASI,QATJA,YASI;AAAA,MARJC,YAQI,QARJA,YAQI;AAAA,MAPJC,iBAOI,QAPJA,iBAOI;AAAA,MANJC,iBAMI,QANJA,iBAMI;AAAA,MALJC,aAKI,QALJA,aAKI;AAAA,MAJJC,cAII,QAJJA,cAII;AAAA,MAHJC,SAGI,QAHJA,SAGI;AAAA,MAFJC,QAEI,QAFJA,QAEI;AAAA,MADJC,IACI,QADJA,IACI;AACJ,MAAMC,SAAS,GAAG;AAChBH,aAAS,EAATA,SADgB;AAEhBR,eAAW,EAAEA,WAAW,IAAI,CAFZ;AAGhBS,YAAQ,EAAEA,QAAQ,IAAIG,SAAS,CAACH,QAAV,CAAmBI,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAHN;AAIhBH,QAAI,EAAJA;AAJgB,GAAlB;;AAOA,MAAII,KAAK,CAACC,OAAN,CAAcP,SAAd,CAAJ,EAA8B;AAC5BG,aAAS,CAACH,SAAV,GAAsBG,SAAS,CAACH,SAAV,CAAoBQ,GAApB,CAAwB,UAACC,OAAD;AAAA,aAC5CA,OAAO,CAACC,WAAR,EAD4C;AAAA,KAAxB,CAAtB;AAGD;;AAED,MAAI,OAAOP,SAAS,CAACF,QAAjB,KAA8B,QAAlC,EAA4C;AAC1CE,aAAS,CAACF,QAAV,GAAqBE,SAAS,CAACF,QAAV,CAAmBS,WAAnB,EAArB;AACD;;AAED,MAAIhB,YAAJ,EAAkB;AAChBS,aAAS,CAACT,YAAV,GAAyBA,YAAzB;AACD,GAFD,MAEO,IAAIE,iBAAiB,KAAKe,SAA1B,EAAqC;AAC1CR,aAAS,CAACP,iBAAV,GAA8BA,iBAA9B;AACD;;AAED,MAAIH,cAAJ,EAAoB;AAClBU,aAAS,CAACS,4BAAV,GAAyC,UAAzC;AACD;;AAED,yCACKT,SADL;AAEER,gBAAY,EAAZA,YAFF;AAGEE,qBAAiB,EAAjBA,iBAHF;AAIEC,iBAAa,EAAbA,aAJF;AAKEC,kBAAc,EAAdA;AALF;AAOD,CA/CD;;AAiDA,IAAMc,eAAe,GAAG,SAAlBA,eAAkB;AAAA,oCACtBC,iBADsB;AAAA,MACtBA,iBADsB,sCACF,KADE;AAAA,oCAEtBC,kBAFsB;AAAA,MAEtBA,kBAFsB,sCAED,UAACC,MAAD;AAAA,WAAYA,MAAZ;AAAA,GAFC;AAAA,MAGtBC,gBAHsB,SAGtBA,gBAHsB;AAAA,2BAItBC,MAJsB;AAAA,MAItBA,MAJsB,6BAIb,YAAM,CAAE,CAJK;AAAA,4BAKtBC,OALsB;AAAA,MAKtBA,OALsB,8BAKZ,UAACC,CAAD,EAAO;AACf,UAAMA,CAAN;AACD,GAPqB;AAAA,MAQtBC,kBARsB,SAQtBA,kBARsB;AAAA,MAStBC,oBATsB,SAStBA,oBATsB;AAAA,SAUjB;AACLR,qBAAiB,EAAjBA,iBADK;AAELC,sBAAkB,EAAlBA,kBAFK;AAGLE,oBAAgB,EAAhBA,gBAHK;AAILC,UAAM,EAANA,MAJK;AAKLC,WAAO,EAAPA,OALK;AAMLE,sBAAkB,EAAlBA,kBANK;AAOLC,wBAAoB,EAApBA;AAPK,GAViB;AAAA,CAAxB;;AAoBA,IAAIN,MAAM,GAAG,EAAb;AACA,IAAIO,QAAQ,GAAG,EAAf;;AAEA,IAAMC,SAAS,GAAG,SAAZA,SAAY,CAACC,aAAD,EAAmB;AACnCT,QAAM,GAAGzB,aAAa,iCAAMyB,MAAN,GAAiBS,aAAjB,EAAtB;AACAF,UAAQ,GAAGV,eAAe,iCAAMU,QAAN,GAAmBE,aAAnB,EAA1B;AAEA,SAAO;AAAET,UAAM,EAANA,MAAF;AAAUO,YAAQ,EAARA;AAAV,GAAP;AACD,CALD;;AAOeC,kEAAf,E;;;;;;;;AC/Ee,2EAAkB,gBAAgB,GAAG,yBAAyB,gBAAgB,wBAAwB,uBAAuB,sBAAsB,iBAAiB,2BAA2B,uBAAuB,kBAAkB,kBAAkB,qBAAqB,6BAA6B,2BAA2B,GAAG,0CAA0C,6BAA6B,GAAG,0BAA0B,kBAAkB,GAAG,4GAA4G,kBAAkB,GAAG,uBAAuB,gBAAgB,wBAAwB,8EAA8E,uBAAuB,oBAAoB,qBAAqB,GAAG,oBAAoB,oBAAoB,iBAAiB,sBAAsB,uBAAuB,qBAAqB,GAAG,uBAAuB,sBAAsB,uBAAuB,GAAG,iBAAiB,uBAAuB,sBAAsB,mBAAmB,GAAG,yBAAyB,uBAAuB,gBAAgB,iBAAiB,2BAA2B,GAAG,6BAA6B,qBAAqB,kDAAkD,kDAAkD,kBAAkB,GAAG,oBAAoB,cAAc,4BAA4B,uBAAuB,WAAW,cAAc,gBAAgB,kBAAkB,GAAG,gCAAgC,oBAAoB,GAAG,wBAAwB,kBAAkB,uBAAuB,aAAa,aAAa,wCAAwC,wCAAwC,GAAG,gBAAgB,wBAAwB,GAAG,wCAAwC,gDAAgD,gDAAgD,kBAAkB,GAAG,gBAAgB,gBAAgB,sBAAsB,6BAA6B,oBAAoB,sBAAsB,GAAG,kBAAkB,mBAAmB,0BAA0B,GAAG,sBAAsB,2BAA2B,GAAG,sBAAsB,eAAe,GAAG,GAAG,E;;;;;;;ACAvrE,oBAAoB;AACpB,uBAAuB,gBAAgB;;AAEvC;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA,uCAAuC,iCAAiC;;AAExE;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzDA;AACA;AACA;AAEe,SAASE,wBAAT,OAyBZ;AAAA,MAxBDC,aAwBC,QAxBDA,aAwBC;AAAA,MAvBDC,aAuBC,QAvBDA,aAuBC;AAAA,MAtBDC,MAsBC,QAtBDA,MAsBC;AAAA,MArBDC,KAqBC,QArBDA,KAqBC;AAAA,MApBDtC,WAoBC,QApBDA,WAoBC;AAAA,MAnBDC,cAmBC,QAnBDA,cAmBC;AAAA,MAlBDC,YAkBC,QAlBDA,YAkBC;AAAA,MAjBDC,YAiBC,QAjBDA,YAiBC;AAAA,MAhBDC,iBAgBC,QAhBDA,iBAgBC;AAAA,MAfDC,iBAeC,QAfDA,iBAeC;AAAA,MAdDC,aAcC,QAdDA,aAcC;AAAA,MAbDC,cAaC,QAbDA,cAaC;AAAA,MAZDC,SAYC,QAZDA,SAYC;AAAA,MAXDiB,gBAWC,QAXDA,gBAWC;AAAA,mCAVDF,kBAUC;AAAA,MAVDA,kBAUC,sCAVoB,UAACC,MAAD;AAAA,WAAYA,MAAZ;AAAA,GAUpB;AAAA,mCATDF,iBASC;AAAA,MATDA,iBASC,sCATmB,KASnB;AAAA,2BARDb,QAQC;AAAA,MARDA,QAQC,8BARUG,SAAS,CAACH,QAAV,CAAmBI,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAQV;AAAA,yBAPDa,MAOC;AAAA,MAPDA,MAOC,4BAPQ,YAAM,CAAE,CAOhB;AAAA,0BANDC,OAMC;AAAA,MANDA,OAMC,6BANS,UAACC,CAAD,EAAO;AACf,UAAMA,CAAN;AACD,GAIA;AAAA,MAHDC,kBAGC,QAHDA,kBAGC;AAAA,MAFDC,oBAEC,QAFDA,oBAEC;AAAA,MADDpB,IACC,QADDA,IACC;AACD,MAAM6B,YAAY,GAAGJ,aAAa,CAACK,UAAd,CAAyBF,KAAzB,EAAgCD,MAAhC,EAAwCD,aAAxC,CAArB;AACAG,cAAY,CAACE,EAAb,CAAgBC,eAAhB,0BAAkDC,kBAAlD;AAEA,MAAMV,aAAa,GAAGD,oCAAS,CAAC;AAC9BhC,eAAW,EAAXA,WAD8B;AAE9BU,QAAI,EAAJA,IAF8B;AAG9BT,kBAAc,EAAdA,cAH8B;AAI9BO,aAAS,EAATA,SAJ8B;AAK9BC,YAAQ,EAARA,QAL8B;AAM9BP,gBAAY,EAAZA,YAN8B;AAO9BC,gBAAY,EAAZA,YAP8B;AAQ9BC,qBAAiB,EAAjBA,iBAR8B;AAS9BC,qBAAiB,EAAjBA,iBAT8B;AAU9BC,iBAAa,EAAbA,aAV8B;AAW9BC,kBAAc,EAAdA,cAX8B;AAY9BkB,oBAAgB,EAAhBA,gBAZ8B;AAa9BF,sBAAkB,EAAlBA,kBAb8B;AAc9BD,qBAAiB,EAAjBA,iBAd8B;AAe9BI,UAAM,EAANA,MAf8B;AAgB9BC,WAAO,EAAPA,OAhB8B;AAiB9BE,sBAAkB,EAAlBA,kBAjB8B;AAkB9BC,wBAAoB,EAApBA;AAlB8B,GAAD,CAA/B;AAqBA,MAAIN,MAAM,GAAGS,aAAa,CAACT,MAA3B;AACA,MAAIO,QAAQ,GAAGE,aAAa,CAACF,QAA7B;AAEA,MAAIa,UAAJ;AACA,MAAIC,OAAO,GAAG,IAAd;;AAEA,MAAId,QAAQ,CAACT,iBAAb,EAAgC;AAC9BuB,WAAO,GAAGjC,SAAS,CAACkC,WAAV,CAAsBC,aAAtB,CAAoC,iBAAgB;AAAA,UAAbC,MAAa,SAAbA,MAAa;AAC5DJ,gBAAU,aAAMI,MAAM,CAACC,QAAb,cAAyBD,MAAM,CAACE,SAAhC,CAAV;AACD,KAFS,CAAV;AAGD;;AAED,WAASC,QAAT,CAAkBC,KAAlB,EAAyBC,EAAzB,EAA6B;AAC3B,QAAMC,YAAY,mCACb9B,MADa;AAEhB4B,WAAK,EAALA;AAFgB,MAAlB;;AAKA,QAAIR,UAAJ,EAAgB;AACdU,kBAAY,CAACpD,YAAb,GAA4B0C,UAA5B;AACD;;AAED,WAAOL,YAAY,CAChBgB,MADI,CACGxB,QAAQ,CAACR,kBAAT,CAA4B+B,YAA5B,CADH,EAEJE,IAFI,CAEC,UAACC,OAAD,EAAa;AACjB,UAAMC,IAAI,GAAGD,OAAO,CAACC,IAAR,CAAa1C,GAAb,CAAiB,UAAC2C,GAAD,EAAMC,QAAN;AAAA,eAC5BC,oCAAS,CAAC;AACRpC,0BAAgB,EAAEM,QAAQ,CAACN,gBADnB;AAERkC,aAAG,EAAHA,GAFQ;AAGRC,kBAAQ,EAARA,QAHQ;AAIRR,eAAK,EAALA,KAJQ;AAKRU,mBAAS,EAAEL;AALH,SAAD,CADmB;AAAA,OAAjB,CAAb;AAUA1B,cAAQ,CAACL,MAAT,CAAgB;AACdgC,YAAI,EAAJA,IADc;AAEdN,aAAK,EAALA,KAFc;AAGdU,iBAAS,EAAEL;AAHG,OAAhB;AAMA,aAAOC,IAAP;AACD,KApBI,EAqBJF,IArBI,CAqBCH,EArBD,WAsBE,UAACzB,CAAD,EAAO;AACZ,UACEA,CAAC,CAACmC,UAAF,KAAiB,GAAjB,IACAnC,CAAC,CAACoC,OAAF,KAAc,mCAFhB,EAGE;AACAjC,gBAAQ,CAACD,oBAAT;AACA;AACD,OAND,MAMO,IAAIF,CAAC,CAACmC,UAAF,KAAiB,GAArB,EAA0B;AAC/BhC,gBAAQ,CAACF,kBAAT;AACA;AACD;;AAEDE,cAAQ,CAACJ,OAAT,CAAiBC,CAAjB;AACD,KAnCI,CAAP;AAoCD;;AAEDuB,UAAQ,CAACnB,SAAT,GAAqB,UAACiC,OAAD,EAAa;AAChC,QAAMC,OAAO,GAAGlC,oCAAS,+CAAMR,MAAN,GAAiBO,QAAjB,GAA8BkC,OAA9B,EAAzB;AAEAzC,UAAM,GAAG0C,OAAO,CAAC1C,MAAjB;AACAO,YAAQ,GAAGmC,OAAO,CAACnC,QAAnB;;AAEA,QAAIA,QAAQ,CAACT,iBAAT,IAA8BuB,OAAO,KAAK,IAA9C,EAAoD;AAClDA,aAAO,GAAGjC,SAAS,CAACkC,WAAV,CAAsBC,aAAtB,CAAoC,iBAAgB;AAAA,YAAbC,MAAa,SAAbA,MAAa;AAC5DJ,kBAAU,aAAMI,MAAM,CAACC,QAAb,cAAyBD,MAAM,CAACE,SAAhC,CAAV;AACD,OAFS,CAAV;AAGD,KAJD,MAIO,IAAI,CAACnB,QAAQ,CAACT,iBAAV,IAA+BuB,OAAO,KAAK,IAA/C,EAAqD;AAC1DjC,eAAS,CAACkC,WAAV,CAAsBqB,UAAtB,CAAiCtB,OAAjC;AACAA,aAAO,GAAG,IAAV;AACAD,gBAAU,GAAG,IAAb;AACD;AACF,GAfD;;AAgBA,SAAOO,QAAP;AACD,C;;;;;;;;;;;ACnID;AACA;AAEe,SAASiB,yBAAT,CAAmCC,OAAnC,EAA4C;AACzD,MAAMC,SAAS,GAAG,kFACbC,mCADU,GAEVF,OAAO,CAACC,SAFE,CAAf;;AAKA,MAAME,MAAM,GAAGtC,wBAAwB,CAAC,kFACnCmC,OADkC;AAErC5C,oBAAgB,EAAE6C,SAAS,CAACG,KAFS;AAGrCH,aAAS,EAAEnD;AAH0B,KAAvC;AAMA,SAAO;AACLqD,UAAM,EAANA,MADK;AAELF,aAAS,EAATA,SAFK;AAGLI,cAAU,EAAE,OAHP;AAILC,QAAI,EAAE,QAJD;AAKLC,SAAK,EAAE;AALF,GAAP;AAOD,C;;;;;;;ACtBD;AACA;AAEA;AACA;AACA;AACA;AACA,IAAI,EAAE,cAAchE,SAAhB,CAAJ,EAAgC;AAC9BA,WAAS,CAACH,QAAV,GACE;AACA;AACA;AACA;AACA;AACCG,WAAS,CAACiE,YAAV,IACCjE,SAAS,CAACiE,YAAV,CAAuBC,OAAvB,CACE,YADF,EAEEC,MAAM,CAACC,SAAP,CAAiBC,WAFnB,CADF,IAKA,OAXF,CAD8B,CAYnB;AACZ,C;;;;;;;;ACpBD;AAAe,uEAAf,E;;;;;;;;ACAe,8TAAqQ,E;;;;;;;ACApR;AACA;;AAEA;AAEAC,mBAAO,CAAC,EAAD,CAAP;;AACA,IAAMd,yBAAyB,GAAGc,mBAAO,CAAC,EAAD,CAAP,WAAlC;;AAEA,IAAMC,GAAG,GAAGD,mBAAO,CAAC,EAAD,CAAP,WAAZ;;AACA,IAAME,SAAS,GAAGF,mBAAO,CAAC,EAAD,CAAzB;;AACAE,SAAS,CAACD,GAAD,EAAM;AAAEE,SAAO,EAAE;AAAX,CAAN,CAAT,C,CAEA;;AACAC,MAAM,CAACC,OAAP,GAAiBnB,yBAAjB;AACA,sC;;;;;;;;ACdA;AAAA;AAAA;AACA;AACA;AAEee,8HAAf,E;;;;;;;;;;ACJe,SAAS1D,gBAAT,OAMZ;AAAA,MALD+D,cAKC,QALDA,cAKC;AAAA,MAJDC,IAIC,QAJDA,IAIC;AAAA,MAHDxE,OAGC,QAHDA,OAGC;AAAA,MAFD0D,IAEC,QAFDA,IAEC;AAAA,MADDjE,IACC,QADDA,IACC;AACD,MAAMgF,GAAG,GAAG,UAAGf,IAAH,SAAUjE,IAAI,KAAK,SAAT,IAAsBO,OAAO,KAAKE,SAAlC,GAA8C,GAA9C,GAAoD,EAA9D,gBACXsE,IAAI,aAAMA,IAAN,SAAgB,EADT,gBAEXD,cAAc,aAAMA,cAAN,SAA0B,EAF7B,gBAGXvE,OAAO,GAAGA,OAAH,GAAa,EAHT,EAIT6D,OAJS,CAID,WAJC,EAIY,GAJZ,EAKTa,IALS,EAAZ;AAMA,SAAOD,GAAP;AACD,C;;;;;ACdc,0TAAiR,E;;ACAjR,+cAAma,E;;ACAna,8/BAAs9B,E;;ACAt9B,u5BAA62B,E;;ACA72B,+fAAkd,E;;ACAld,8mBAAokB,E;;ACAnlB;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAME,KAAK,GAAG;AACZC,SAAO,EAAEC,0BADG;AAEZL,MAAI,EAAEM,IAFM;AAGZ9E,SAAO,EAAE+E,OAHG;AAIZC,SAAO,EAAEC,GAJG;AAKZC,cAAY,EAAEC,KALF;AAMZC,UAAQ,EAAEC,QANE;AAOZC,SAAO,EAAEC,KAASA;AAPN,CAAd;AAUe,SAASC,mBAAT,OAAkD;AAAA,MAAnB/F,IAAmB,QAAnBA,IAAmB;AAAA,MAAbgG,SAAa,QAAbA,SAAa;AAAA,MACvD/B,IADuD,GACf+B,SADe,CACvD/B,IADuD;AAAA,MACjDa,cADiD,GACfkB,SADe,CACjDlB,cADiD;AAAA,MACjCC,IADiC,GACfiB,SADe,CACjCjB,IADiC;AAAA,MAC3BxE,OAD2B,GACfyF,SADe,CAC3BzF,OAD2B;AAG/D,MAAMyE,GAAG,GAAG,6CAAoCE,KAAK,CAAClF,IAAD,CAAL,CAAYiF,IAAZ,EAApC,8CACUhB,IADV,qDAGV,CAACc,IAAD,EAAOD,cAAP,EAAuBvE,OAAvB,EACC0F,MADD,CACQ,UAACC,KAAD;AAAA,WAAWA,KAAK,KAAKzF,SAArB;AAAA,GADR,EAEC0F,IAFD,CAEM,IAFN,CAHU,aAKW/B,OALX,CAKmB,WALnB,EAKgC,GALhC,CAAZ;AAOA,SAAOY,GAAP;AACD,C;;AC7Bc,omQAAwjQ,E;;ACAxjQ,ioBAAylB,E;;ACAxmB;AACA;AACA;AACA;AAEe;AACboB,QAAM,4IACyFC,OAAW,CAACpB,IAAZ,EADzF,6LAE2JqB,GAAO,CAACrB,IAAR,EAF3J,qCADO;AAKblB,OAAK,EAALA,gBALa;AAMbwC,YAAU,EAAVA,mBAAUA;AANG,CAAf,E;;;;;;;;;;;;;ACLe,SAASC,eAAT,CAAyBC,IAAzB,EAA+B;AAC5C,OAAK,IAAIC,QAAQ,GAAG,CAApB,EAAuBA,QAAQ,GAAGD,IAAI,CAACE,MAAvC,EAA+CD,QAAQ,EAAvD,EAA2D;AACzD,QAAME,GAAG,GAAGH,IAAI,CAACC,QAAD,CAAhB;AACA,QAAMG,IAAI,GAAGD,GAAG,CAACE,KAAJ,CAAU,gBAAV,CAAb;;AACA,QAAID,IAAJ,EAAU;AACR,aAAOA,IAAI,CAAC,CAAD,CAAX;AACD;AACF;;AAED,SAAOpG,SAAP;AACD,C;;ACVc,SAASsG,QAAT,CAAkBN,IAAlB,EAAwB;AACrC,MAAMO,KAAK,GAAG;AACZzG,WAAO,EAAE,SADG;AAEZwE,QAAI,EAAE,MAFM;AAGZ,2BAAuB,SAHX;AAIZ,wBAAoB,UAJR;AAKZ,uBAAmB,cALP;AAMZ,yBAAqB,SANT;AAOZ,wBAAoB,SAPR;AAQZ,oBAAgB;AARJ,GAAd;;AAWA,OAAK,IAAMkC,CAAX,IAAgBD,KAAhB,EAAuB;AACrB,QAAIP,IAAI,CAACS,OAAL,CAAaD,CAAb,MAAoB,CAAC,CAAzB,EAA4B;AAC1B,aAAOD,KAAK,CAACC,CAAD,CAAZ;AACD;AACF;;AAED,SAAO,SAAP;AACD,C;;;;;;;;ACnBD;AACA;;AAEA,SAASE,sBAAT,CAAgCC,iBAAhC,EAAmD;AACjD,MAAMC,YAAY,GAAGD,iBAAiB,CAAC,CAAD,CAAjB,CAAqBrD,KAA1C,CADiD,CAEjD;;AACA,MAAMuD,cAAc,GAAG,EAAvB;;AACA,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,iBAAiB,CAACT,MAAtC,EAA8C,EAAEY,CAAhD,EAAmD;AACjD,QAAIH,iBAAiB,CAACG,CAAD,CAAjB,CAAqBC,UAArB,KAAoC,MAAxC,EAAgD;AAC9CF,oBAAc,CAACG,IAAf,CAAoB;AAClBC,aAAK,EAAEH,CADW;AAElBI,aAAK,EAAEP,iBAAiB,CAACG,CAAD,CAAjB,CAAqBK;AAFV,OAApB;AAID;AACF,GAXgD,CAYjD;;;AACA,MAAIN,cAAc,CAACX,MAAf,KAA0B,CAA9B,EAAiC;AAC/B,WAAOU,YAAP;AACD,GAfgD,CAgBjD;;;AACAC,gBAAc,CAACO,IAAf,CAAoB,UAACC,CAAD,EAAIC,CAAJ,EAAU;AAC5B,QAAID,CAAC,CAACH,KAAF,GAAUI,CAAC,CAACJ,KAAhB,EAAuB;AACrB,aAAO,CAAC,CAAR;AACD,KAFD,MAEO,IAAIG,CAAC,CAACH,KAAF,GAAUI,CAAC,CAACJ,KAAhB,EAAuB;AAC5B,aAAO,CAAP;AACD;;AACD,WAAOG,CAAC,CAACJ,KAAF,GAAUK,CAAC,CAACL,KAAnB;AACD,GAPD,EAjBiD,CAyBjD;;AACA,SAAOJ,cAAc,CAAC,CAAD,CAAd,CAAkBI,KAAlB,KAA4B,CAA5B,aACAL,YADA,eACiBD,iBAAiB,CAACE,cAAc,CAAC,CAAD,CAAd,CAAkBI,KAAnB,CAAjB,CAA2C3D,KAD5D,mBAEAqD,iBAAiB,CAACE,cAAc,CAAC,CAAD,CAAd,CAAkBI,KAAnB,CAAjB,CAA2C3D,KAF3C,eAEqDsD,YAFrD,MAAP;AAGD;;AAED,SAASW,eAAT,CAAyBC,SAAzB,EAAoCC,oBAApC,EAA0D;AACxD,MAAMb,YAAY,GAAGa,oBAAoB,CAAC,CAAD,CAApB,CAAwBnE,KAA7C,CADwD,CAExD;;AACA,MAAMuD,cAAc,GAAG,EAAvB;;AACA,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGW,oBAAoB,CAACvB,MAAzC,EAAiD,EAAEY,CAAnD,EAAsD;AACpD,QAAIW,oBAAoB,CAACX,CAAD,CAApB,CAAwBC,UAAxB,KAAuC,MAA3C,EAAmD;AACjDF,oBAAc,CAACG,IAAf,CAAoB;AAClBC,aAAK,EAAEH,CADW;AAElBI,aAAK,EAAEO,oBAAoB,CAACX,CAAD,CAApB,CAAwBK;AAFb,OAApB;AAID;AACF,GAXuD,CAYxD;;;AACA,MAAIN,cAAc,CAACX,MAAf,KAA0B,CAA9B,EAAiC;AAC/B,WAAO;AAAEwB,cAAQ,EAAEF,SAAS,CAAC,CAAD,CAArB;AAA0BG,yBAAmB,EAAEf;AAA/C,KAAP;AACD,GAfuD,CAgBxD;;;AACAC,gBAAc,CAACO,IAAf,CAAoB,UAACC,CAAD,EAAIC,CAAJ,EAAU;AAC5B,QAAID,CAAC,CAACH,KAAF,GAAUI,CAAC,CAACJ,KAAhB,EAAuB;AACrB,aAAO,CAAC,CAAR;AACD,KAFD,MAEO,IAAIG,CAAC,CAACH,KAAF,GAAUI,CAAC,CAACJ,KAAhB,EAAuB;AAC5B,aAAO,CAAP;AACD;;AACD,WAAOG,CAAC,CAACJ,KAAF,GAAUK,CAAC,CAACL,KAAnB;AACD,GAPD;AASA,MAAMS,QAAQ,GAAGF,SAAS,CAACX,cAAc,CAAC,CAAD,CAAd,CAAkBI,KAAnB,CAA1B;AACA,SAAO;AACLS,YAAQ,EAARA,QADK;AAELC,uBAAmB,EAAEF,oBAAoB,CAACZ,cAAc,CAAC,CAAD,CAAd,CAAkBI,KAAnB,CAApB,CAA8C3D;AAF9D,GAAP;AAID;;AAEc,SAASZ,SAAT,OAMZ;AAAA,MALDpC,gBAKC,QALDA,gBAKC;AAAA,MAJDkC,GAIC,QAJDA,GAIC;AAAA,MAHDC,QAGC,QAHDA,QAGC;AAAA,MAFDR,KAEC,QAFDA,KAEC;AAAA,MADDU,SACC,QADDA,SACC;;AACD,MAAI;AACF,QAAMa,IAAI,GAAGhB,GAAG,CAACoF,YAAJ,CAAiB,CAAjB,CAAb;AACA,QAAM9H,OAAO,GAAG0C,GAAG,CAAC1C,OAApB;AACA,QAAMuE,cAAc,GAClB7B,GAAG,CAAC6B,cAAJ,IAAsB7B,GAAG,CAAC6B,cAAJ,CAAmB,CAAnB,MAA0Bb,IAAhD,GACIhB,GAAG,CAAC6B,cAAJ,CAAmB,CAAnB,CADJ,GAEIrE,SAHN;AAIA,QAAMsE,IAAI,GAAG9B,GAAG,CAAC8B,IAAJ,IAAY9B,GAAG,CAAC8B,IAAJ,CAAS,CAAT,MAAgBd,IAA5B,GAAmChB,GAAG,CAAC8B,IAAJ,CAAS,CAAT,CAAnC,GAAiDtE,SAA9D;AACA,QAAM6H,MAAM,GACVrF,GAAG,CAACqF,MAAJ,IAAcrF,GAAG,CAACqF,MAAJ,CAAW,CAAX,MAAkBrE,IAAhC,GAAuChB,GAAG,CAACqF,MAAJ,CAAW,CAAX,CAAvC,GAAuD7H,SADzD;AAGA,QAAM8H,MAAM,GACVtF,GAAG,CAACsF,MAAJ,IAActF,GAAG,CAACsF,MAAJ,CAAW,CAAX,MAAkBtE,IAAhC,GAAuChB,GAAG,CAACsF,MAAJ,CAAW,CAAX,CAAvC,GAAuD9H,SADzD;;AAXE,gBAeAwC,GAAG,CAACkF,QAAJ,IAAgBlF,GAAG,CAACkF,QAAJ,CAAaxB,MAA7B,GACIqB,eAAe,CAAC/E,GAAG,CAACkF,QAAL,EAAelF,GAAG,CAACuF,gBAAJ,CAAqBL,QAApC,CADnB,GAEI;AAAEA,cAAQ,EAAE1H,SAAZ;AAAuB2H,yBAAmB,EAAE3H;AAA5C,KAjBJ;AAAA,QAcM0H,QAdN,SAcMA,QAdN;AAAA,QAcgBC,mBAdhB,SAcgBA,mBAdhB;;AAmBF,QAAMpC,SAAS,GAAG;AAChB/B,UAAI,EAAEkD,sBAAsB,CAAClE,GAAG,CAACuF,gBAAJ,CAAqBH,YAAtB,CADZ;AAEhBtD,UAAI,EAAEA,IAAI,GACNoC,sBAAsB,CAAClE,GAAG,CAACuF,gBAAJ,CAAqBzD,IAAtB,CADhB,GAENtE,SAJY;AAKhBqE,oBAAc,EAAEA,cAAc,GAC1BqC,sBAAsB,CAAClE,GAAG,CAACuF,gBAAJ,CAAqB1D,cAAtB,CADI,GAE1BrE,SAPY;AAQhBF,aAAO,EAAEA,OAAO,GAAG0C,GAAG,CAACuF,gBAAJ,CAAqBjI,OAArB,CAA6BwD,KAAhC,GAAwCtD,SARxC;AAShB6H,YAAM,EAAEA,MAAM,GACVnB,sBAAsB,CAAClE,GAAG,CAACuF,gBAAJ,CAAqBF,MAAtB,CADZ,GAEV7H,SAXY;AAYhB8H,YAAM,EAAEA,MAAM,GACVpB,sBAAsB,CAAClE,GAAG,CAACuF,gBAAJ,CAAqBD,MAAtB,CADZ,GAEV9H,SAdY;AAehB0H,cAAQ,EAAEC;AAfM,KAAlB;AAkBA,QAAM7B,UAAU,GAAG;AACjBtC,UAAI,EAAJA,IADiB;AAEjBa,oBAAc,EAAdA,cAFiB;AAGjByD,YAAM,EAANA,MAHiB;AAIjBxD,UAAI,EAAJA,IAJiB;AAKjBuD,YAAM,EAANA,MALiB;AAMjB/H,aAAO,EAAPA,OANiB;AAOjBkI,iBAAW,EAAEjC,eAAe,CAACvD,GAAG,CAACyF,KAAL,CAPX;AAQjB1I,UAAI,EAAE+G,QAAQ,CAAC9D,GAAG,CAACyF,KAAL,CARG;AASjBC,YAAM,EAAE;AACNC,WAAG,EAAE3F,GAAG,CAAC4F,OAAJ,CAAYD,GADX;AAENE,WAAG,EAAE7F,GAAG,CAAC4F,OAAJ,CAAYC;AAFX,OATS;AAajBX,cAAQ,EAARA,QAbiB;AAcjBF,eAAS,EAAEhF,GAAG,CAACkF,QAAJ,IAAgBlF,GAAG,CAACkF,QAAJ,CAAaxB,MAA7B,GAAsC1D,GAAG,CAACkF,QAA1C,GAAqD1H;AAd/C,KAAnB,CArCE,CAsDF;;AACA,QAAMsD,KAAK,GAAGhD,gBAAgB,CAACwF,UAAD,CAA9B;AAEA,2CACKA,UADL;AAEEP,eAAS,EAATA,SAFF;AAGE/C,SAAG,EAAHA,GAHF;AAIEC,cAAQ,EAARA,QAJF;AAKER,WAAK,EAALA,KALF;AAMEU,eAAS,EAATA,SANF;AAOEW,WAAK,EAALA;AAPF;AASD,GAlED,CAkEE,OAAO7C,CAAP,EAAU;AACV;AACA6H,WAAO,CAACC,KAAR,CAAc,wBAAd,EAAwC/F,GAAxC;AACA8F,WAAO,CAACC,KAAR,CAAc9H,CAAd;AACA;;AACA,WAAO;AACL6C,WAAK,EAAE;AADF,KAAP;AAGD;AACF,C", "file": "placesAutocompleteDataset.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"placesAutocompleteDataset\"] = factory();\n\telse\n\t\troot[\"placesAutocompleteDataset\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 70);\n", "const extractParams = ({\n  hitsPerPage,\n  postcodeSearch,\n  aroundLatLng,\n  aroundRadius,\n  aroundLatLngViaIP,\n  insideBoundingBox,\n  insidePolygon,\n  getRankingInfo,\n  countries,\n  language,\n  type,\n}) => {\n  const extracted = {\n    countries,\n    hitsPerPage: hitsPerPage || 5,\n    language: language || navigator.language.split('-')[0],\n    type,\n  };\n\n  if (Array.isArray(countries)) {\n    extracted.countries = extracted.countries.map((country) =>\n      country.toLowerCase()\n    );\n  }\n\n  if (typeof extracted.language === 'string') {\n    extracted.language = extracted.language.toLowerCase();\n  }\n\n  if (aroundLatLng) {\n    extracted.aroundLatLng = aroundLatLng;\n  } else if (aroundLatLngViaIP !== undefined) {\n    extracted.aroundLatLngViaIP = aroundLatLngViaIP;\n  }\n\n  if (postcodeSearch) {\n    extracted.restrictSearchableAttributes = 'postcode';\n  }\n\n  return {\n    ...extracted,\n    aroundRadius,\n    insideBoundingBox,\n    insidePolygon,\n    getRankingInfo,\n  };\n};\n\nconst extractControls = ({\n  useDeviceLocation = false,\n  computeQueryParams = (params) => params,\n  formatInputValue,\n  onHits = () => {},\n  onError = (e) => {\n    throw e;\n  },\n  onRateLimitReached,\n  onInvalidCredentials,\n}) => ({\n  useDeviceLocation,\n  computeQueryParams,\n  formatInputValue,\n  onHits,\n  onError,\n  onRateLimitReached,\n  onInvalidCredentials,\n});\n\nlet params = {};\nlet controls = {};\n\nconst configure = (configuration) => {\n  params = extractParams({ ...params, ...configuration });\n  controls = extractControls({ ...controls, ...configuration });\n\n  return { params, controls };\n};\n\nexport default configure;\n", "export default \".algolia-places {\\n  width: 100%;\\n}\\n\\n.ap-input, .ap-hint {\\n  width: 100%;\\n  padding-right: 35px;\\n  padding-left: 16px;\\n  line-height: 40px;\\n  height: 40px;\\n  border: 1px solid #CCC;\\n  border-radius: 3px;\\n  outline: none;\\n  font: inherit;\\n  appearance: none;\\n  -webkit-appearance: none;\\n  box-sizing: border-box;\\n}\\n\\n.ap-input::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n.ap-input::-ms-clear {\\n  display: none;\\n}\\n\\n.ap-input:hover ~ .ap-input-icon svg,\\n.ap-input:focus ~ .ap-input-icon svg,\\n.ap-input-icon:hover svg {\\n  fill: #aaaaaa;\\n}\\n\\n.ap-dropdown-menu {\\n  width: 100%;\\n  background: #ffffff;\\n  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2), 0 2px 4px 0 rgba(0, 0, 0, 0.1);\\n  border-radius: 3px;\\n  margin-top: 3px;\\n  overflow: hidden;\\n}\\n\\n.ap-suggestion {\\n  cursor: pointer;\\n  height: 46px;\\n  line-height: 46px;\\n  padding-left: 18px;\\n  overflow: hidden;\\n}\\n\\n.ap-suggestion em {\\n  font-weight: bold;\\n  font-style: normal;\\n}\\n\\n.ap-address {\\n  font-size: smaller;\\n  margin-left: 12px;\\n  color: #aaaaaa;\\n}\\n\\n.ap-suggestion-icon {\\n  margin-right: 10px;\\n  width: 14px;\\n  height: 20px;\\n  vertical-align: middle;\\n}\\n\\n.ap-suggestion-icon svg {\\n  display: inherit;\\n  -webkit-transform: scale(0.9) translateY(2px);\\n          transform: scale(0.9) translateY(2px);\\n  fill: #cfcfcf;\\n}\\n\\n.ap-input-icon {\\n  border: 0;\\n  background: transparent;\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  right: 16px;\\n  outline: none;\\n}\\n\\n.ap-input-icon.ap-icon-pin {\\n  cursor: pointer;\\n}\\n\\n.ap-input-icon svg {\\n  fill: #cfcfcf;\\n  position: absolute;\\n  top: 50%;\\n  right: 0;\\n  -webkit-transform: translateY(-50%);\\n          transform: translateY(-50%);\\n}\\n\\n.ap-cursor {\\n  background: #efefef;\\n}\\n\\n.ap-cursor .ap-suggestion-icon svg {\\n  -webkit-transform: scale(1) translateY(2px);\\n          transform: scale(1) translateY(2px);\\n  fill: #aaaaaa;\\n}\\n\\n.ap-footer {\\n  opacity: .8;\\n  text-align: right;\\n  padding: .5em 1em .5em 0;\\n  font-size: 12px;\\n  line-height: 12px;\\n}\\n\\n.ap-footer a {\\n  color: inherit;\\n  text-decoration: none;\\n}\\n\\n.ap-footer a svg {\\n  vertical-align: middle;\\n}\\n\\n.ap-footer:hover {\\n  opacity: 1;\\n}\\n\";", "var containers = []; // will store container HTMLElement references\nvar styleElements = []; // will store {prepend: HTMLElement, append: HTMLElement}\n\nvar usage = 'insert-css: You need to provide a CSS string. Usage: insertCss(cssString[, options]).';\n\nfunction insertCss(css, options) {\n    options = options || {};\n\n    if (css === undefined) {\n        throw new Error(usage);\n    }\n\n    var position = options.prepend === true ? 'prepend' : 'append';\n    var container = options.container !== undefined ? options.container : document.querySelector('head');\n    var containerId = containers.indexOf(container);\n\n    // first time we see this container, create the necessary entries\n    if (containerId === -1) {\n        containerId = containers.push(container) - 1;\n        styleElements[containerId] = {};\n    }\n\n    // try to get the correponding container + position styleElement, create it otherwise\n    var styleElement;\n\n    if (styleElements[containerId] !== undefined && styleElements[containerId][position] !== undefined) {\n        styleElement = styleElements[containerId][position];\n    } else {\n        styleElement = styleElements[containerId][position] = createStyleElement();\n\n        if (position === 'prepend') {\n            container.insertBefore(styleElement, container.childNodes[0]);\n        } else {\n            container.appendChild(styleElement);\n        }\n    }\n\n    // strip potential UTF-8 BOM if css was read from a file\n    if (css.charCodeAt(0) === 0xFEFF) { css = css.substr(1, css.length); }\n\n    // actually add the stylesheet\n    if (styleElement.styleSheet) {\n        styleElement.styleSheet.cssText += css\n    } else {\n        styleElement.textContent += css;\n    }\n\n    return styleElement;\n};\n\nfunction createStyleElement() {\n    var styleElement = document.createElement('style');\n    styleElement.setAttribute('type', 'text/css');\n    return styleElement;\n}\n\nmodule.exports = insertCss;\nmodule.exports.insertCss = insertCss;\n", "import configure from './configure';\nimport formatHit from './formatHit';\nimport version from './version';\n\nexport default function createAutocompleteSource({\n  algoliasearch,\n  clientOptions,\n  apiKey,\n  appId,\n  hitsPerPage,\n  postcodeSearch,\n  aroundLatLng,\n  aroundRadius,\n  aroundLatLngViaIP,\n  insideBoundingBox,\n  insidePolygon,\n  getRankingInfo,\n  countries,\n  formatInputValue,\n  computeQueryParams = (params) => params,\n  useDeviceLocation = false,\n  language = navigator.language.split('-')[0],\n  onHits = () => {},\n  onError = (e) => {\n    throw e;\n  },\n  onRateLimitReached,\n  onInvalidCredentials,\n  type,\n}) {\n  const placesClient = algoliasearch.initPlaces(appId, apiKey, clientOptions);\n  placesClient.as.addAlgoliaAgent(`Algolia Places ${version}`);\n\n  const configuration = configure({\n    hitsPerPage,\n    type,\n    postcodeSearch,\n    countries,\n    language,\n    aroundLatLng,\n    aroundRadius,\n    aroundLatLngViaIP,\n    insideBoundingBox,\n    insidePolygon,\n    getRankingInfo,\n    formatInputValue,\n    computeQueryParams,\n    useDeviceLocation,\n    onHits,\n    onError,\n    onRateLimitReached,\n    onInvalidCredentials,\n  });\n\n  let params = configuration.params;\n  let controls = configuration.controls;\n\n  let userCoords;\n  let tracker = null;\n\n  if (controls.useDeviceLocation) {\n    tracker = navigator.geolocation.watchPosition(({ coords }) => {\n      userCoords = `${coords.latitude},${coords.longitude}`;\n    });\n  }\n\n  function searcher(query, cb) {\n    const searchParams = {\n      ...params,\n      query,\n    };\n\n    if (userCoords) {\n      searchParams.aroundLatLng = userCoords;\n    }\n\n    return placesClient\n      .search(controls.computeQueryParams(searchParams))\n      .then((content) => {\n        const hits = content.hits.map((hit, hitIndex) =>\n          formatHit({\n            formatInputValue: controls.formatInputValue,\n            hit,\n            hitIndex,\n            query,\n            rawAnswer: content,\n          })\n        );\n\n        controls.onHits({\n          hits,\n          query,\n          rawAnswer: content,\n        });\n\n        return hits;\n      })\n      .then(cb)\n      .catch((e) => {\n        if (\n          e.statusCode === 403 &&\n          e.message === 'Invalid Application-ID or API key'\n        ) {\n          controls.onInvalidCredentials();\n          return;\n        } else if (e.statusCode === 429) {\n          controls.onRateLimitReached();\n          return;\n        }\n\n        controls.onError(e);\n      });\n  }\n\n  searcher.configure = (partial) => {\n    const updated = configure({ ...params, ...controls, ...partial });\n\n    params = updated.params;\n    controls = updated.controls;\n\n    if (controls.useDeviceLocation && tracker === null) {\n      tracker = navigator.geolocation.watchPosition(({ coords }) => {\n        userCoords = `${coords.latitude},${coords.longitude}`;\n      });\n    } else if (!controls.useDeviceLocation && tracker !== null) {\n      navigator.geolocation.clearWatch(tracker);\n      tracker = null;\n      userCoords = null;\n    }\n  };\n  return searcher;\n}\n", "import createAutocompleteSource from './createAutocompleteSource';\nimport defaultTemplates from './defaultTemplates';\n\nexport default function createAutocompleteDataset(options) {\n  const templates = {\n    ...defaultTemplates,\n    ...options.templates,\n  };\n\n  const source = createAutocompleteSource({\n    ...options,\n    formatInputValue: templates.value,\n    templates: undefined,\n  });\n\n  return {\n    source,\n    templates,\n    displayKey: 'value',\n    name: 'places',\n    cache: false,\n  };\n}\n", "// polyfill for navigator.language (IE <= 10)\n// not polyfilled by https://cdn.polyfill.io/v2/docs/\n\n// Defined: http://www.whatwg.org/specs/web-apps/current-work/multipage/timers.html#navigatorlanguage\n//   with allowable values at http://www.ietf.org/rfc/bcp/bcp47.txt\n// Note that the HTML spec suggests that anonymizing services return \"en-US\" by default for\n//   user privacy (so your app may wish to provide a means of changing the locale)\nif (!('language' in navigator)) {\n  navigator.language =\n    // IE 10 in IE8 mode on Windows 7 uses upper-case in\n    // navigator.userLanguage country codes but per\n    // http://msdn.microsoft.com/en-us/library/ie/ms533052.aspx (via\n    // http://msdn.microsoft.com/en-us/library/ie/ms534713.aspx), they\n    // appear to be in lower case, so we bring them into harmony with navigator.language.\n    (navigator.userLanguage &&\n      navigator.userLanguage.replace(\n        /-[a-z]{2}$/,\n        String.prototype.toUpperCase\n      )) ||\n    'en-US'; // Default for anonymizing services: http://www.whatwg.org/specs/web-apps/current-work/multipage/timers.html#navigatorlanguage\n}\n", "export default '1.19.0';\n", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 14 20\\\"><path d=\\\"M7 0C3.13 0 0 3.13 0 7c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5C5.62 9.5 4.5 8.38 4.5 7S5.62 4.5 7 4.5 9.5 5.62 9.5 7 8.38 9.5 7 9.5z\\\"/></svg>\\n\";", "// we need to export using commonjs for ease of usage in all\n// JavaScript environments\n\n/* eslint-disable import/no-commonjs */\n\nrequire('./src/navigatorLanguage');\nconst createAutocompleteDataset = require('./src/createAutocompleteDataset')\n  .default;\nconst css = require('./babel-css').default;\nconst insertCss = require('insert-css');\ninsertCss(css, { prepend: true });\n\n// must use module.exports to be commonJS compatible\nmodule.exports = createAutocompleteDataset;\n/* eslint-enable import/no-commonjs */\n", "// this is proxy file so that babel inlines src/places.css into the css variable\n// since it is not a commonjs file\nimport css from './src/places.css';\n\nexport default css;\n", "export default function formatInputValue({\n  administrative,\n  city,\n  country,\n  name,\n  type,\n}) {\n  const out = `${name}${type !== 'country' && country !== undefined ? ',' : ''}\n ${city ? `${city},` : ''}\n ${administrative ? `${administrative},` : ''}\n ${country ? country : ''}`\n    .replace(/\\s*\\n\\s*/g, ' ')\n    .trim();\n  return out;\n}\n", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 18 19\\\"><path d=\\\"M12 9V3L9 0 6 3v2H0v14h18V9h-6zm-8 8H2v-2h2v2zm0-4H2v-2h2v2zm0-4H2V7h2v2zm6 8H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V7h2v2zm0-4H8V3h2v2zm6 12h-2v-2h2v2zm0-4h-2v-2h2v2z\\\"/></svg>\\n\";", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 20 20\\\">\\n  <path d=\\\"M10 0C4.48 0 0 4.48 0 10s4.48 10 10 10 10-4.48 10-10S15.52 0 10 0zM9 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L7 13v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H6V8h2c.55 0 1-.45 1-1V5h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z\\\"/>\\n</svg>\\n\";", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 54.9 50.5\\\"><path d=\\\"M9.6 12.7H8.5c-2.3 0-4.1 1.9-4.1 4.1v1.1c0 2.2 1.8 4 4 4.1v21.7h-.7c-1.3 0-2.3 1-2.3 2.3h7.1c0-1.3-1-2.3-2.3-2.3h-.5V22.1c2.2-.1 4-1.9 4-4.1v-1.1c0-2.3-1.8-4.2-4.1-4.2zM46 7.6h-7.5c0-1.8-1.5-3.3-3.3-3.3h-3.6c-1.8 0-3.3 1.5-3.3 3.3H21c-2.5 0-4.6 2-4.6 4.6v26.3c0 1.7 1.3 3.1 3 3.1h.8v1.6c0 1.7 1.4 3.1 3.1 3.1 1.7 0 3-1.4 3-3.1v-1.6h14.3v1.6c0 1.7 1.4 3.1 3.1 3.1 1.7 0 3.1-1.4 3.1-3.1v-1.6h.8c1.7 0 3.1-1.4 3.1-3.1V12.2c-.2-2.5-2.2-4.6-4.7-4.6zm-27.4 4.6c0-1.3 1.1-2.4 2.4-2.4h25c1.3 0 2.4 1.1 2.4 2.4v.3c0 1.3-1.1 2.4-2.4 2.4H21c-1.3 0-2.4-1.1-2.4-2.4v-.3zM21 38c-1.5 0-2.7-1.2-2.7-2.7 0-1.5 1.2-2.7 2.7-2.7 1.5 0 2.7 1.2 2.7 2.7 0 1.5-1.2 2.7-2.7 2.7zm0-10.1c-1.3 0-2.4-1.1-2.4-2.4v-6.6c0-1.3 1.1-2.4 2.4-2.4h25c1.3 0 2.4 1.1 2.4 2.4v6.6c0 1.3-1.1 2.4-2.4 2.4H21zm24.8 10c-1.5 0-2.7-1.2-2.7-2.7 0-1.5 1.2-2.7 2.7-2.7 1.5 0 2.7 1.2 2.7 2.7 0 1.5-1.2 2.7-2.7 2.7z\\\"/></svg>\\n\";", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 15 20\\\">\\n  <path d=\\\"M13.105 20l-2.366-3.354H4.26L1.907 20H0l3.297-4.787c-1.1-.177-2.196-1.287-2.194-2.642V2.68C1.1 1.28 2.317-.002 3.973 0h7.065c1.647-.002 2.863 1.28 2.86 2.676v9.895c.003 1.36-1.094 2.47-2.194 2.647L15 20h-1.895zM6.11 2h2.78c.264 0 .472-.123.472-.27v-.46c0-.147-.22-.268-.472-.27H6.11c-.252.002-.47.123-.47.27v.46c0 .146.206.27.47.27zm6.26 3.952V4.175c-.004-.74-.5-1.387-1.436-1.388H4.066c-.936 0-1.43.648-1.436 1.388v1.777c-.002.86.644 1.384 1.436 1.388h6.868c.793-.004 1.44-.528 1.436-1.388zm-8.465 5.386c-.69-.003-1.254.54-1.252 1.21-.002.673.56 1.217 1.252 1.222.697-.006 1.26-.55 1.262-1.22-.002-.672-.565-1.215-1.262-1.212zm8.42 1.21c-.005-.67-.567-1.213-1.265-1.21-.69-.003-1.253.54-1.25 1.21-.003.673.56 1.217 1.25 1.222.698-.006 1.26-.55 1.264-1.22z\\\"/>\\n</svg>\\n\";", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M12 .6L2.5 6.9h18.9L12 .6zM3.8 8.2c-.7 0-1.3.6-1.3 1.3v8.8L.3 22.1c-.2.3-.3.5-.3.6 0 .6.8.6 1.3.6h21.5c.4 0 1.3 0 1.3-.6 0-.2-.1-.3-.3-.6l-2.2-3.8V9.5c0-.7-.6-1.3-1.3-1.3H3.8zm2.5 2.5c.7 0 1.1.6 1.3 1.3v7.6H5.1V12c0-.7.5-1.3 1.2-1.3zm5.7 0c.7 0 1.3.6 1.3 1.3v7.6h-2.5V12c-.1-.7.5-1.3 1.2-1.3zm5.7 0c.7 0 1.3.6 1.3 1.3v7.6h-2.5V12c-.1-.7.5-1.3 1.2-1.3z\\\"/></svg>\\n\";", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M22.9 1.1s1.3.3-4.3 6.5l.7 3.8.2-.2c.4-.4 1-.4 1.3 0 .4.4.4 1 0 1.3l-1.2 1.2.3 1.7.1-.1c.4-.4 1-.4 1.3 0 .4.4.4 1 0 1.3l-1.1 1.1c.2 1.9.3 3.6.1 4.5 0 0-1.2 1.2-1.8.5 0 0-2.3-7.7-3.8-11.1-5.9 6-6.4 5.6-6.4 5.6s1.2 3.8-.2 5.2l-2.3-4.3h.1l-4.3-2.3c1.3-1.3 5.2-.2 5.2-.2s-.5-.4 5.6-6.3C8.9 7.7 1.2 5.5 1.2 5.5c-.7-.7.5-1.8.5-1.8.9-.2 2.6-.1 4.5.1l1.1-1.1c.4-.4 1-.4 1.3 0 .4.4.4 1 0 1.3l1.7.3 1.2-1.2c.4-.4 1-.4 1.3 0 .4.4.4 1 0 1.3l-.2.2 3.8.7c6.2-5.5 6.5-4.2 6.5-4.2z\\\"/></svg>\\n\";", "import addressIcon from './icons/address.svg';\nimport cityIcon from './icons/city.svg';\nimport countryIcon from './icons/country.svg';\nimport busIcon from './icons/bus.svg';\nimport trainIcon from './icons/train.svg';\nimport townhallIcon from './icons/townhall.svg';\nimport planeIcon from './icons/plane.svg';\n\nconst icons = {\n  address: addressIcon,\n  city: cityIcon,\n  country: countryIcon,\n  busStop: busIcon,\n  trainStation: trainIcon,\n  townhall: townhallIcon,\n  airport: planeIcon,\n};\n\nexport default function formatDropdownValue({ type, highlight }) {\n  const { name, administrative, city, country } = highlight;\n\n  const out = `<span class=\"ap-suggestion-icon\">${icons[type].trim()}</span>\n<span class=\"ap-name\">${name}</span>\n<span class=\"ap-address\">\n  ${[city, administrative, country]\n    .filter((token) => token !== undefined)\n    .join(', ')}</span>`.replace(/\\s*\\n\\s*/g, ' ');\n\n  return out;\n}\n", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"117\\\" height=\\\"17\\\" viewBox=\\\"0 0 130 19\\\"><g fill=\\\"none\\\" fill-rule=\\\"evenodd\\\"><g fill-rule=\\\"nonzero\\\"><path fill=\\\"#5468FF\\\" d=\\\"M59.399.044h13.299a2.372 2.372 0 0 1 2.377 2.364v13.234a2.372 2.372 0 0 1-2.377 2.364H59.399a2.372 2.372 0 0 1-2.377-2.364V2.403A2.368 2.368 0 0 1 59.399.044z\\\"/><path fill=\\\"#FFF\\\" d=\\\"M66.257 4.582c-2.815 0-5.1 2.272-5.1 5.078 0 2.806 2.284 5.072 5.1 5.072 2.815 0 5.1-2.272 5.1-5.078 0-2.806-2.279-5.072-5.1-5.072zm0 8.652c-1.983 0-3.593-1.602-3.593-3.574 0-1.972 1.61-3.574 3.593-3.574 1.983 0 3.593 1.602 3.593 3.574a3.582 3.582 0 0 1-3.593 3.574zm0-6.418V9.48c0 .***************.093l2.377-1.226c.055-.027.071-.093.044-.147a2.96 2.96 0 0 0-2.465-1.487c-.055 0-.11.044-.11.104h.001zm-3.33-1.956l-.312-.31a.783.783 0 0 0-1.106 0l-.372.37a.773.773 0 0 0 0 1.1l.307.305c.049.05.121.038.164-.01.181-.246.378-.48.597-.698.225-.223.455-.42.707-.599.055-.033.06-.109.016-.158h-.001zm5.001-.806v-.616a.781.781 0 0 0-.783-.779h-1.824a.78.78 0 0 0-.783.78v.631c0 .071.066.12.137.104a5.736 5.736 0 0 1 1.588-.223c.52 0 1.035.071 1.534.207a.106.106 0 0 0 .131-.104z\\\"/><path fill=\\\"#252C61\\\" d=\\\"M5.027 10.246c0 .698-.252 1.246-.757 1.644-.505.397-1.201.596-2.089.596-.888 0-1.615-.138-2.181-.414v-1.214c.358.168.739.301 1.141.397.403.097.778.145 1.125.145.508 0 .884-.097 1.125-.29a.945.945 0 0 0 .363-.779.978.978 0 0 0-.333-.747c-.222-.204-.68-.446-1.375-.725C1.33 8.57.825 8.24.531 7.865c-.294-.372-.44-.82-.44-1.343 0-.655.233-1.17.698-1.547.465-.376 1.09-.564 1.875-.564.752 0 1.5.165 2.245.494l-.408 1.047c-.698-.294-1.321-.44-1.869-.44-.415 0-.73.09-.945.271a.89.89 0 0 0-.322.717c0 .204.043.38.129.524.086.145.227.282.424.411.197.13.551.3 1.063.51.577.24.999.464 1.268.671.269.208.465.442.591.704.125.261.188.57.188.924l-.001.002zm3.98 2.24c-.924 0-1.646-.269-2.167-.808-.521-.539-.781-1.28-.781-2.226 0-.97.242-1.733.725-2.288.483-.555 1.148-.833 1.993-.833.784 0 1.404.238 1.858.714.455.476.682 1.132.682 1.966v.682H7.359c.018.577.174 1.02.467 1.33.294.31.707.464 1.241.464.351 0 .678-.033.98-.099a5.1 5.1 0 0 0 .975-.33v1.026a3.865 3.865 0 0 1-.935.312 5.723 5.723 0 0 1-1.08.091zm7.46-.107l-.252-.827h-.043c-.286.362-.575.608-.865.74-.29.13-.662.195-1.117.195-.584 0-1.039-.158-1.367-.473-.328-.315-.491-.76-.491-1.337 0-.612.227-1.074.682-1.386.455-.312 1.148-.482 2.079-.51l1.026-.032v-.317c0-.38-.089-.663-.266-.85-.177-.189-.452-.283-.824-.283-.304 0-.596.045-.875.134a6.68 6.68 0 0 0-.806.317l-.408-.902a4.414 4.414 0 0 1 1.058-.384 4.856 4.856 0 0 1 1.085-.132c.756 0 1.326.165 1.711.494.385.33.577.847.577 1.552v4.001h-.904zm5.677-6.048c.254 0 .464.018.628.054l-.124 1.176a2.383 2.383 0 0 0-.559-.064c-.505 0-.914.165-1.227.494-.313.33-.47.757-.47 1.284v3.104H19.13V6.44h.988l.167 1.047h.064c.197-.354.454-.636.771-.843a1.83 1.83 0 0 1 1.023-.312h.001zm4.125 6.155c-.899 0-1.582-.262-2.049-.787-.467-.525-.701-1.277-.701-2.259 0-.999.244-1.767.733-2.304.489-.537 1.195-.806 2.119-.806.627 0 1.191.116 1.692.35l-.381 1.014c-.534-.208-.974-.312-1.321-.312-1.028 0-1.542.682-1.542 2.046 0 .666.128 1.166.384 1.501.256.335.631.502 1.125.502a3.23 3.23 0 0 0 1.595-.419v1.101a2.53 2.53 0 0 1-.722.285 4.356 4.356 0 0 1-.932.086v.002zm8.277-.107h-1.268V8.727c0-.458-.092-.8-.277-1.026-.184-.226-.477-.338-.878-.338-.53 0-.919.158-1.168.475-.249.317-.373.848-.373 1.593v2.95H29.32V4.022h1.262v2.122c0 .34-.021.704-.064 1.09h.081a1.76 1.76 0 0 1 .717-.666c.306-.158.663-.236 1.072-.236 1.439 0 2.159.725 2.159 2.175v3.873l-.001-.002zm7.648-6.048c.741 0 1.319.27 1.732.806.414.537.62 1.291.62 2.261 0 .974-.209 1.732-.628 2.275-.419.542-1.001.814-1.746.814-.752 0-1.336-.27-1.751-.81h-.086l-.231.703h-.945V4.023h1.262V6.01l-.021.655-.032.553h.054c.401-.59.992-.886 1.772-.886zm2.917.107h1.375l1.208 3.368c.183.48.304.931.365 1.354h.043c.032-.197.091-.436.177-.717.086-.28.541-1.616 1.364-4.004h1.364l-2.541 6.73c-.462 1.235-1.232 1.853-2.31 1.853-.279 0-.551-.03-.816-.09v-1c.19.043.406.064.65.064.609 0 1.037-.353 1.284-1.058l.22-.559-2.385-5.94h.002zm-3.244.924c-.508 0-.875.15-1.098.448-.224.3-.339.8-.346 1.501v.086c0 .723.115 1.247.344 1.571.229.324.603.486 1.123.486.448 0 .787-.177 1.018-.532.231-.354.346-.867.346-1.536 0-1.35-.462-2.025-1.386-2.025l-.001.001zm-27.28 4.157c.458 0 .826-.128 1.104-.384.278-.256.416-.615.416-1.077v-.516l-.763.032c-.594.021-1.027.121-1.297.298s-.406.448-.406.814c0 .265.079.47.236.615.158.145.394.218.709.218h.001zM8.775 7.287c-.401 0-.722.127-.964.381s-.386.625-.432 1.112h2.696c-.007-.49-.125-.862-.354-1.115-.229-.252-.544-.379-.945-.379l-.001.001z\\\"/></g><path fill=\\\"#5468FF\\\" d=\\\"M102.162 13.784c0 1.455-.372 2.517-1.123 3.193-.75.676-1.895 1.013-3.44 1.013-.564 0-1.736-.109-2.673-.316l.345-1.689c.783.163 1.819.207 2.361.207.86 0 1.473-.174 1.84-.523.367-.349.548-.866.548-1.553v-.349a6.374 6.374 0 0 1-.838.316 4.151 4.151 0 0 1-1.194.158 4.515 4.515 0 0 1-1.616-.278 3.385 3.385 0 0 1-1.254-.817 3.744 3.744 0 0 1-.811-1.35c-.192-.54-.29-1.505-.29-2.213 0-.665.104-1.498.307-2.054a3.925 3.925 0 0 1 .904-1.433 4.124 4.124 0 0 1 1.441-.926 5.31 5.31 0 0 1 1.945-.365c.696 0 1.337.087 1.961.191a15.86 15.86 0 0 1 1.588.332v8.456h-.001zm-5.955-4.206c0 .893.197 1.885.592 2.3.394.413.904.62 1.528.62.34 0 .663-.049.964-.142a2.75 2.75 0 0 0 .734-.332v-5.29a8.531 8.531 0 0 0-1.413-.18c-.778-.022-1.369.294-1.786.801-.411.507-.619 1.395-.619 2.223zm16.121 0c0 .72-.104 1.264-.318 1.858a4.389 4.389 0 0 1-.904 1.52c-.389.42-.854.746-1.402.975-.548.23-1.391.36-1.813.36-.422-.005-1.26-.125-1.802-.36a4.088 4.088 0 0 1-1.397-.975 4.486 4.486 0 0 1-.909-1.52 5.037 5.037 0 0 1-.329-1.858c0-.719.099-1.41.318-1.999.219-.588.526-1.09.92-1.509.394-.42.865-.74 1.402-.97a4.547 4.547 0 0 1 1.786-.338 4.69 4.69 0 0 1 1.791.338c.548.23 1.019.55 1.402.97.389.42.69.921.909 1.51.23.587.345 1.28.345 1.998h.001zm-2.192.005c0-.92-.203-1.689-.597-2.223-.394-.539-.948-.806-1.654-.806-.707 0-1.26.267-1.654.806-.394.54-.586 1.302-.586 2.223 0 .932.197 1.558.592 2.098.394.545.948.812 1.654.812.707 0 1.26-.272 1.654-.812.394-.545.592-1.166.592-2.098h-.001zm6.963 4.708c-3.511.016-3.511-2.822-3.511-3.274L113.583.95l2.142-.338v10.003c0 .256 0 1.88 1.375 1.885v1.793h-.001zM120.873 14.291h-2.153V5.095l2.153-.338zM119.794 3.75c.718 0 1.304-.579 1.304-1.292 0-.714-.581-1.29-1.304-1.29-.723 0-1.304.577-1.304 1.29 0 .714.586 1.291 1.304 1.291zm6.431 1.012c.707 0 1.304.087 1.786.262.482.174.871.42 1.156.73.285.311.488.735.608 1.182.126.447.186.937.186 1.476v5.481a25.24 25.24 0 0 1-1.495.251c-.668.098-1.419.147-2.251.147a6.829 6.829 0 0 1-1.517-.158 3.213 3.213 0 0 1-1.178-.507 2.455 2.455 0 0 1-.761-.904c-.181-.37-.274-.893-.274-1.438 0-.523.104-.855.307-1.215.208-.36.487-.654.838-.883a3.609 3.609 0 0 1 1.227-.49 7.073 7.073 0 0 1 2.202-.103c.263.027.537.076.833.147v-.349c0-.245-.027-.479-.088-.697a1.486 1.486 0 0 0-.307-.583c-.148-.169-.34-.3-.581-.392a2.536 2.536 0 0 0-.915-.163c-.493 0-.942.06-1.353.131-.411.071-.75.153-1.008.245l-.257-1.749c.268-.093.668-.185 1.183-.278a9.335 9.335 0 0 1 1.66-.142h-.001zm.179 7.73c.657 0 1.145-.038 1.484-.104V10.22a5.097 5.097 0 0 0-1.978-.104c-.241.033-.46.098-.652.191a1.167 1.167 0 0 0-.466.392c-.121.17-.175.267-.175.523 0 .501.175.79.493.981.323.196.75.29 1.293.29h.001zM84.108 4.816c.707 0 1.304.087 1.786.262.482.174.871.42 1.156.73.29.316.487.735.608 1.182.126.447.186.937.186 1.476v5.481a25.24 25.24 0 0 1-1.495.251c-.668.098-1.419.147-2.251.147a6.829 6.829 0 0 1-1.517-.158 3.213 3.213 0 0 1-1.178-.507 2.455 2.455 0 0 1-.761-.904c-.181-.37-.274-.893-.274-1.438 0-.523.104-.855.307-1.215.208-.36.487-.654.838-.883a3.609 3.609 0 0 1 1.227-.49 7.073 7.073 0 0 1 2.202-.103c.257.027.537.076.833.147v-.349c0-.245-.027-.479-.088-.697a1.486 1.486 0 0 0-.307-.583c-.148-.169-.34-.3-.581-.392a2.536 2.536 0 0 0-.915-.163c-.493 0-.942.06-1.353.131-.411.071-.75.153-1.008.245l-.257-1.749c.268-.093.668-.185 1.183-.278a8.89 8.89 0 0 1 1.66-.142h-.001zm.185 7.736c.657 0 1.145-.038 1.484-.104V10.28a5.097 5.097 0 0 0-1.978-.104c-.241.033-.46.098-.652.191a1.167 1.167 0 0 0-.466.392c-.121.17-.175.267-.175.523 0 .501.175.79.493.981.318.191.75.29 1.293.29h.001zm8.683 1.738c-3.511.016-3.511-2.822-3.511-3.274L89.46.948 91.602.61v10.003c0 .256 0 1.88 1.375 1.885v1.793h-.001z\\\"/></g></svg>\";", "export default \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"12\\\" height=\\\"12\\\">\\n  <path fill=\\\"#797979\\\" fill-rule=\\\"evenodd\\\" d=\\\"M6.577.5L5.304.005 2.627 1.02 0 0l.992 2.767-.986 2.685.998 2.76-1 2.717.613.22 3.39-3.45.563.06.726-.69s-.717-.92-.91-1.86c.193-.146.184-.14.355-.285C4.1 1.93 6.58.5 6.58.5zm-4.17 11.354l.22.12 2.68-1.05 2.62 1.04 2.644-1.03 1.02-2.717-.33-.944s-1.13 1.26-3.44.878c-.174.29-.25.37-.25.37s-1.11-.31-1.683-.89c-.573.58-.795.71-.795.71l.08.634-2.76 2.89zm6.26-4.395c1.817 0 3.29-1.53 3.29-3.4 0-1.88-1.473-3.4-3.29-3.4s-3.29 1.52-3.29 3.4c0 1.87 1.473 3.4 3.29 3.4z\\\"/>\\n</svg>\\n\";", "import value from './formatInputValue';\nimport suggestion from './formatDropdownValue';\nimport algoliaLogo from './icons/algolia.svg';\nimport osmLogo from './icons/osm.svg';\n\nexport default {\n  footer: `<div class=\"ap-footer\">\n  <a href=\"https://www.algolia.com/places\" title=\"Search by Algolia\" class=\"ap-footer-algolia\">${algoliaLogo.trim()}</a>\n  using <a href=\"https://community.algolia.com/places/documentation.html#license\" class=\"ap-footer-osm\" title=\"Algolia Places data © OpenStreetMap contributors\">${osmLogo.trim()} <span>data</span></a>\n  </div>`,\n  value,\n  suggestion,\n};\n", "export default function findCountryCode(tags) {\n  for (let tagIndex = 0; tagIndex < tags.length; tagIndex++) {\n    const tag = tags[tagIndex];\n    const find = tag.match(/country\\/(.*)?/);\n    if (find) {\n      return find[1];\n    }\n  }\n\n  return undefined;\n}\n", "export default function findType(tags) {\n  const types = {\n    country: 'country',\n    city: 'city',\n    'amenity/bus_station': 'busStop',\n    'amenity/townhall': 'townhall',\n    'railway/station': 'trainStation',\n    'aeroway/aerodrome': 'airport',\n    'aeroway/terminal': 'airport',\n    'aeroway/gate': 'airport',\n  };\n\n  for (const t in types) {\n    if (tags.indexOf(t) !== -1) {\n      return types[t];\n    }\n  }\n\n  return 'address';\n}\n", "import findCountryCode from './findCountryCode';\nimport findType from './findType';\n\nfunction getBestHighlightedForm(highlightedValues) {\n  const defaultValue = highlightedValues[0].value;\n  // collect all other matches\n  const bestAttributes = [];\n  for (let i = 1; i < highlightedValues.length; ++i) {\n    if (highlightedValues[i].matchLevel !== 'none') {\n      bestAttributes.push({\n        index: i,\n        words: highlightedValues[i].matchedWords,\n      });\n    }\n  }\n  // no matches in this attribute, retrieve first value\n  if (bestAttributes.length === 0) {\n    return defaultValue;\n  }\n  // sort the matches by `desc(words), asc(index)`\n  bestAttributes.sort((a, b) => {\n    if (a.words > b.words) {\n      return -1;\n    } else if (a.words < b.words) {\n      return 1;\n    }\n    return a.index - b.index;\n  });\n  // and append the best match to the first value\n  return bestAttributes[0].index === 0\n    ? `${defaultValue} (${highlightedValues[bestAttributes[1].index].value})`\n    : `${highlightedValues[bestAttributes[0].index].value} (${defaultValue})`;\n}\n\nfunction getBestPostcode(postcodes, highlightedPostcodes) {\n  const defaultValue = highlightedPostcodes[0].value;\n  // collect all other matches\n  const bestAttributes = [];\n  for (let i = 1; i < highlightedPostcodes.length; ++i) {\n    if (highlightedPostcodes[i].matchLevel !== 'none') {\n      bestAttributes.push({\n        index: i,\n        words: highlightedPostcodes[i].matchedWords,\n      });\n    }\n  }\n  // no matches in this attribute, retrieve first value\n  if (bestAttributes.length === 0) {\n    return { postcode: postcodes[0], highlightedPostcode: defaultValue };\n  }\n  // sort the matches by `desc(words)`\n  bestAttributes.sort((a, b) => {\n    if (a.words > b.words) {\n      return -1;\n    } else if (a.words < b.words) {\n      return 1;\n    }\n    return a.index - b.index;\n  });\n\n  const postcode = postcodes[bestAttributes[0].index];\n  return {\n    postcode,\n    highlightedPostcode: highlightedPostcodes[bestAttributes[0].index].value,\n  };\n}\n\nexport default function formatHit({\n  formatInputValue,\n  hit,\n  hitIndex,\n  query,\n  rawAnswer,\n}) {\n  try {\n    const name = hit.locale_names[0];\n    const country = hit.country;\n    const administrative =\n      hit.administrative && hit.administrative[0] !== name\n        ? hit.administrative[0]\n        : undefined;\n    const city = hit.city && hit.city[0] !== name ? hit.city[0] : undefined;\n    const suburb =\n      hit.suburb && hit.suburb[0] !== name ? hit.suburb[0] : undefined;\n\n    const county =\n      hit.county && hit.county[0] !== name ? hit.county[0] : undefined;\n\n    const { postcode, highlightedPostcode } =\n      hit.postcode && hit.postcode.length\n        ? getBestPostcode(hit.postcode, hit._highlightResult.postcode)\n        : { postcode: undefined, highlightedPostcode: undefined };\n\n    const highlight = {\n      name: getBestHighlightedForm(hit._highlightResult.locale_names),\n      city: city\n        ? getBestHighlightedForm(hit._highlightResult.city)\n        : undefined,\n      administrative: administrative\n        ? getBestHighlightedForm(hit._highlightResult.administrative)\n        : undefined,\n      country: country ? hit._highlightResult.country.value : undefined,\n      suburb: suburb\n        ? getBestHighlightedForm(hit._highlightResult.suburb)\n        : undefined,\n      county: county\n        ? getBestHighlightedForm(hit._highlightResult.county)\n        : undefined,\n      postcode: highlightedPostcode,\n    };\n\n    const suggestion = {\n      name,\n      administrative,\n      county,\n      city,\n      suburb,\n      country,\n      countryCode: findCountryCode(hit._tags),\n      type: findType(hit._tags),\n      latlng: {\n        lat: hit._geoloc.lat,\n        lng: hit._geoloc.lng,\n      },\n      postcode,\n      postcodes: hit.postcode && hit.postcode.length ? hit.postcode : undefined,\n    };\n\n    // this is the value to put inside the <input value=\n    const value = formatInputValue(suggestion);\n\n    return {\n      ...suggestion,\n      highlight,\n      hit,\n      hitIndex,\n      query,\n      rawAnswer,\n      value,\n    };\n  } catch (e) {\n    /* eslint-disable no-console */\n    console.error('Could not parse object', hit);\n    console.error(e);\n    /* eslint-enable no-console */\n    return {\n      value: 'Could not parse object',\n    };\n  }\n}\n"], "sourceRoot": ""}