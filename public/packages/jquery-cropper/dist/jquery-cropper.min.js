!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(require("jquery"),require("cropperjs")):"function"==typeof define&&define.amd?define(["jquery","cropperjs"],r):r((e=e||self).j<PERSON><PERSON><PERSON>,<PERSON><PERSON>)}(this,(function(e,r){"use strict";if(e=e&&e.hasOwnProperty("default")?e.default:e,r=r&&r.hasOwnProperty("default")?r.default:r,e&&e.fn&&r){var t=e.fn.cropper,n="cropper";e.fn.cropper=function(t){for(var o=arguments.length,f=new Array(1<o?o-1:0),i=1;i<o;i++)f[i-1]=arguments[i];var p;return this.each((function(o,i){var a=e(i),u="destroy"===t,c=a.data(n);if(!c){if(u)return;var s=e.extend({},a.data(),e.isPlainObject(t)&&t);c=new r(i,s),a.data(n,c)}if("string"==typeof t){var d=c[t];e.isFunction(d)&&((p=d.apply(c,f))===c&&(p=void 0),u&&a.removeData(n))}})),void 0!==p?p:this},e.fn.cropper.Constructor=r,e.fn.cropper.setDefaults=r.setDefaults,e.fn.cropper.noConflict=function(){return e.fn.cropper=t,this}}}));
