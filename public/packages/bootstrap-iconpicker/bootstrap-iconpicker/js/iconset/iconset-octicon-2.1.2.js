/*!========================================================================
 * Bootstrap: iconset-octicon-2.1.2.js by @recktoner
 * https://victor-valencia.github.com/bootstrap-iconpicker
 *
 * Iconset: Octicons 2.1.2
 * https://octicons.github.com/
 * ========================================================================
 * Copyright 2013-2017 Victor Valencia Rico.
 * Licensed under MIT license.
 * https://github.com/victor-valencia/bootstrap-iconpicker/blob/master/LICENSE
 * ======================================================================== */

;(function($){

    $.iconset_octicon = {
        iconClass: 'octicon',
        iconClassFix: 'octicon-',
        icons: [
            '',
            'alert',
            'alignment-align',
            'alignment-aligned-to',
            'alignment-unalign',
            'arrow-down',
            'arrow-left',
            'arrow-right',
            'arrow-small-down',
            'arrow-small-left',
            'arrow-small-right',
            'arrow-small-up',
            'arrow-up',
            'beer',
            'book',
            'bookmark',
            'briefcase',
            'broadcast',
            'browser',
            'bug',
            'calendar',
            'check',
            'checklist',
            'chevron-down',
            'chevron-left',
            'chevron-right',
            'chevron-up',
            'circle-slash',
            'circuit-board',
            'clippy',
            'clock',
            'cloud-download',
            'cloud-upload',
            'code',
            'color-mode',
            'comment',
            'comment-discussion',
            'credit-card',
            'dash',
            'dashboard',
            'database',
            'device-camera',
            'device-camera-video',
            'device-desktop',
            'device-mobile',
            'diff',
            'diff-added',
            'diff-ignored',
            'diff-modified',
            'diff-removed',
            'diff-renamed',
            'ellipsis',
            'eye',
            'file-binary',
            'file-code',
            'file-directory',
            'file-media',
            'file-pdf',
            'file-submodule',
            'file-symlink-directory',
            'file-symlink-file',
            'file-text',
            'file-zip',
            'flame',
            'fold',
            'gear',
            'gift',
            'gist',
            'gist-secret',
            'git-branch',
            'git-commit',
            'git-compare',
            'git-merge',
            'git-pull-request',
            'globe',
            'graph',
            'heart',
            'history',
            'home',
            'horizontal-rule',
            'hourglass',
            'hubot',
            'inbox',
            'info',
            'issue-closed',
            'issue-opened',
            'issue-reopened',
            'jersey',
            'jump-down',
            'jump-left',
            'jump-right',
            'jump-up',
            'key',
            'keyboard',
            'law',
            'light-bulb',
            'link',
            'link-external',
            'list-ordered',
            'list-unordered',
            'location',
            'lock',
            'mail',
            'mail-read',
            'mail-reply',
            'mark-github',
            'markdown',
            'megaphone',
            'mention',
            'microscope',
            'milestone',
            'mirror',
            'mortar-board',
            'move-down',
            'move-left',
            'move-right',
            'move-up',
            'mute',
            'no-newline',
            'octoface',
            'organization',
            'package',
            'paintcan',
            'pencil',
            'person',
            'pin',
            'playback-fast-forward',
            'playback-pause',
            'playback-play',
            'playback-rewind',
            'plug',
            'plus',
            'podium',
            'primitive-dot',
            'primitive-square',
            'pulse',
            'puzzle',
            'question',
            'quote',
            'radio-tower',
            'repo',
            'repo-clone',
            'repo-force-push',
            'repo-forked',
            'repo-pull',
            'repo-push',
            'rocket',
            'rss',
            'ruby',
            'screen-full',
            'screen-normal',
            'search',
            'server',
            'settings',
            'sign-in',
            'sign-out',
            'split',
            'squirrel',
            'star',
            'steps',
            'stop',
            'sync',
            'tag',
            'telescope',
            'terminal',
            'three-bars',
            'tools',
            'trashcan',
            'triangle-down',
            'triangle-left',
            'triangle-right',
            'triangle-up',
            'unfold',
            'unmute',
            'versions',
            'x',
            'zap'
        ]
    };

})(jQuery);
