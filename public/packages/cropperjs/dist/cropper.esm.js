function ownKeys(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}function _objectSpread2(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(i),!0).forEach((function(e){_defineProperty(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):ownKeys(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function _typeof(t){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof(t)}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,e){for(var i=0;i<e.length;i++){var a=e[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,a.key,a)}}function _createClass(t,e,i){return e&&_defineProperties(t.prototype,e),i&&_defineProperties(t,i),t}function _defineProperty(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function _toConsumableArray(t){return _arrayWithoutHoles(t)||_iterableToArray(t)||_unsupportedIterableToArray(t)||_nonIterableSpread()}function _arrayWithoutHoles(t){if(Array.isArray(t))return _arrayLikeToArray(t)}function _iterableToArray(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function _unsupportedIterableToArray(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(t,e):void 0}}function _arrayLikeToArray(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,a=new Array(e);i<e;i++)a[i]=t[i];return a}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var IS_BROWSER="undefined"!=typeof window&&void 0!==window.document,WINDOW=IS_BROWSER?window:{},IS_TOUCH_DEVICE=!(!IS_BROWSER||!WINDOW.document.documentElement)&&"ontouchstart"in WINDOW.document.documentElement,HAS_POINTER_EVENT=!!IS_BROWSER&&"PointerEvent"in WINDOW,NAMESPACE="cropper",ACTION_ALL="all",ACTION_CROP="crop",ACTION_MOVE="move",ACTION_ZOOM="zoom",ACTION_EAST="e",ACTION_WEST="w",ACTION_SOUTH="s",ACTION_NORTH="n",ACTION_NORTH_EAST="ne",ACTION_NORTH_WEST="nw",ACTION_SOUTH_EAST="se",ACTION_SOUTH_WEST="sw",CLASS_CROP="".concat(NAMESPACE,"-crop"),CLASS_DISABLED="".concat(NAMESPACE,"-disabled"),CLASS_HIDDEN="".concat(NAMESPACE,"-hidden"),CLASS_HIDE="".concat(NAMESPACE,"-hide"),CLASS_INVISIBLE="".concat(NAMESPACE,"-invisible"),CLASS_MODAL="".concat(NAMESPACE,"-modal"),CLASS_MOVE="".concat(NAMESPACE,"-move"),DATA_ACTION="".concat(NAMESPACE,"Action"),DATA_PREVIEW="".concat(NAMESPACE,"Preview"),DRAG_MODE_CROP="crop",DRAG_MODE_MOVE="move",DRAG_MODE_NONE="none",EVENT_CROP="crop",EVENT_CROP_END="cropend",EVENT_CROP_MOVE="cropmove",EVENT_CROP_START="cropstart",EVENT_DBLCLICK="dblclick",EVENT_TOUCH_START=IS_TOUCH_DEVICE?"touchstart":"mousedown",EVENT_TOUCH_MOVE=IS_TOUCH_DEVICE?"touchmove":"mousemove",EVENT_TOUCH_END=IS_TOUCH_DEVICE?"touchend touchcancel":"mouseup",EVENT_POINTER_DOWN=HAS_POINTER_EVENT?"pointerdown":EVENT_TOUCH_START,EVENT_POINTER_MOVE=HAS_POINTER_EVENT?"pointermove":EVENT_TOUCH_MOVE,EVENT_POINTER_UP=HAS_POINTER_EVENT?"pointerup pointercancel":EVENT_TOUCH_END,EVENT_READY="ready",EVENT_RESIZE="resize",EVENT_WHEEL="wheel",EVENT_ZOOM="zoom",MIME_TYPE_JPEG="image/jpeg",REGEXP_ACTIONS=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,REGEXP_DATA_URL=/^data:/,REGEXP_DATA_URL_JPEG=/^data:image\/jpeg;base64,/,REGEXP_TAG_NAME=/^img|canvas$/i,MIN_CONTAINER_WIDTH=200,MIN_CONTAINER_HEIGHT=100,DEFAULTS={viewMode:0,dragMode:DRAG_MODE_CROP,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:MIN_CONTAINER_WIDTH,minContainerHeight:MIN_CONTAINER_HEIGHT,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},TEMPLATE='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>',isNaN=Number.isNaN||WINDOW.isNaN;function isNumber(t){return"number"==typeof t&&!isNaN(t)}var isPositiveNumber=function(t){return t>0&&t<1/0};function isUndefined(t){return void 0===t}function isObject(t){return"object"===_typeof(t)&&null!==t}var hasOwnProperty=Object.prototype.hasOwnProperty;function isPlainObject(t){if(!isObject(t))return!1;try{var e=t.constructor,i=e.prototype;return e&&i&&hasOwnProperty.call(i,"isPrototypeOf")}catch(t){return!1}}function isFunction(t){return"function"==typeof t}var slice=Array.prototype.slice;function toArray(t){return Array.from?Array.from(t):slice.call(t)}function forEach(t,e){return t&&isFunction(e)&&(Array.isArray(t)||isNumber(t.length)?toArray(t).forEach((function(i,a){e.call(t,i,a,t)})):isObject(t)&&Object.keys(t).forEach((function(i){e.call(t,t[i],i,t)}))),t}var assign=Object.assign||function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),a=1;a<e;a++)i[a-1]=arguments[a];return isObject(t)&&i.length>0&&i.forEach((function(e){isObject(e)&&Object.keys(e).forEach((function(i){t[i]=e[i]}))})),t},REGEXP_DECIMALS=/\.\d*(?:0|9){12}\d*$/;function normalizeDecimalNumber(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e11;return REGEXP_DECIMALS.test(t)?Math.round(t*e)/e:t}var REGEXP_SUFFIX=/^width|height|left|top|marginLeft|marginTop$/;function setStyle(t,e){var i=t.style;forEach(e,(function(t,e){REGEXP_SUFFIX.test(e)&&isNumber(t)&&(t="".concat(t,"px")),i[e]=t}))}function hasClass(t,e){return t.classList?t.classList.contains(e):t.className.indexOf(e)>-1}function addClass(t,e){if(e)if(isNumber(t.length))forEach(t,(function(t){addClass(t,e)}));else if(t.classList)t.classList.add(e);else{var i=t.className.trim();i?i.indexOf(e)<0&&(t.className="".concat(i," ").concat(e)):t.className=e}}function removeClass(t,e){e&&(isNumber(t.length)?forEach(t,(function(t){removeClass(t,e)})):t.classList?t.classList.remove(e):t.className.indexOf(e)>=0&&(t.className=t.className.replace(e,"")))}function toggleClass(t,e,i){e&&(isNumber(t.length)?forEach(t,(function(t){toggleClass(t,e,i)})):i?addClass(t,e):removeClass(t,e))}var REGEXP_CAMEL_CASE=/([a-z\d])([A-Z])/g;function toParamCase(t){return t.replace(REGEXP_CAMEL_CASE,"$1-$2").toLowerCase()}function getData(t,e){return isObject(t[e])?t[e]:t.dataset?t.dataset[e]:t.getAttribute("data-".concat(toParamCase(e)))}function setData(t,e,i){isObject(i)?t[e]=i:t.dataset?t.dataset[e]=i:t.setAttribute("data-".concat(toParamCase(e)),i)}function removeData(t,e){if(isObject(t[e]))try{delete t[e]}catch(i){t[e]=void 0}else if(t.dataset)try{delete t.dataset[e]}catch(i){t.dataset[e]=void 0}else t.removeAttribute("data-".concat(toParamCase(e)))}var REGEXP_SPACES=/\s\s*/,onceSupported=function(){var t=!1;if(IS_BROWSER){var e=!1,i=function(){},a=Object.defineProperty({},"once",{get:function(){return t=!0,e},set:function(t){e=t}});WINDOW.addEventListener("test",i,a),WINDOW.removeEventListener("test",i,a)}return t}();function removeListener(t,e,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=i;e.trim().split(REGEXP_SPACES).forEach((function(e){if(!onceSupported){var r=t.listeners;r&&r[e]&&r[e][i]&&(n=r[e][i],delete r[e][i],0===Object.keys(r[e]).length&&delete r[e],0===Object.keys(r).length&&delete t.listeners)}t.removeEventListener(e,n,a)}))}function addListener(t,e,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=i;e.trim().split(REGEXP_SPACES).forEach((function(e){if(a.once&&!onceSupported){var r=t.listeners,o=void 0===r?{}:r;n=function(){delete o[e][i],t.removeEventListener(e,n,a);for(var r=arguments.length,s=new Array(r),h=0;h<r;h++)s[h]=arguments[h];i.apply(t,s)},o[e]||(o[e]={}),o[e][i]&&t.removeEventListener(e,o[e][i],a),o[e][i]=n,t.listeners=o}t.addEventListener(e,n,a)}))}function dispatchEvent(t,e,i){var a;return isFunction(Event)&&isFunction(CustomEvent)?a=new CustomEvent(e,{detail:i,bubbles:!0,cancelable:!0}):(a=document.createEvent("CustomEvent")).initCustomEvent(e,!0,!0,i),t.dispatchEvent(a)}function getOffset(t){var e=t.getBoundingClientRect();return{left:e.left+(window.pageXOffset-document.documentElement.clientLeft),top:e.top+(window.pageYOffset-document.documentElement.clientTop)}}var location=WINDOW.location,REGEXP_ORIGINS=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function isCrossOriginURL(t){var e=t.match(REGEXP_ORIGINS);return null!==e&&(e[1]!==location.protocol||e[2]!==location.hostname||e[3]!==location.port)}function addTimestamp(t){var e="timestamp=".concat((new Date).getTime());return t+(-1===t.indexOf("?")?"?":"&")+e}function getTransforms(t){var e=t.rotate,i=t.scaleX,a=t.scaleY,n=t.translateX,r=t.translateY,o=[];isNumber(n)&&0!==n&&o.push("translateX(".concat(n,"px)")),isNumber(r)&&0!==r&&o.push("translateY(".concat(r,"px)")),isNumber(e)&&0!==e&&o.push("rotate(".concat(e,"deg)")),isNumber(i)&&1!==i&&o.push("scaleX(".concat(i,")")),isNumber(a)&&1!==a&&o.push("scaleY(".concat(a,")"));var s=o.length?o.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}function getMaxZoomRatio(t){var e=_objectSpread2({},t),i=0;return forEach(t,(function(t,a){delete e[a],forEach(e,(function(e){var a=Math.abs(t.startX-e.startX),n=Math.abs(t.startY-e.startY),r=Math.abs(t.endX-e.endX),o=Math.abs(t.endY-e.endY),s=Math.sqrt(a*a+n*n),h=(Math.sqrt(r*r+o*o)-s)/s;Math.abs(h)>Math.abs(i)&&(i=h)}))})),i}function getPointer(t,e){var i=t.pageX,a=t.pageY,n={endX:i,endY:a};return e?n:_objectSpread2({startX:i,startY:a},n)}function getPointersCenter(t){var e=0,i=0,a=0;return forEach(t,(function(t){var n=t.startX,r=t.startY;e+=n,i+=r,a+=1})),{pageX:e/=a,pageY:i/=a}}function getAdjustedSizes(t){var e=t.aspectRatio,i=t.height,a=t.width,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"contain",r=isPositiveNumber(a),o=isPositiveNumber(i);if(r&&o){var s=i*e;"contain"===n&&s>a||"cover"===n&&s<a?i=a/e:a=i*e}else r?i=a/e:o&&(a=i*e);return{width:a,height:i}}function getRotatedSizes(t){var e=t.width,i=t.height,a=t.degree;if(90===(a=Math.abs(a)%180))return{width:i,height:e};var n=a%90*Math.PI/180,r=Math.sin(n),o=Math.cos(n),s=e*o+i*r,h=e*r+i*o;return a>90?{width:h,height:s}:{width:s,height:h}}function getSourceCanvas(t,e,i,a){var n=e.aspectRatio,r=e.naturalWidth,o=e.naturalHeight,s=e.rotate,h=void 0===s?0:s,c=e.scaleX,d=void 0===c?1:c,l=e.scaleY,p=void 0===l?1:l,m=i.aspectRatio,u=i.naturalWidth,g=i.naturalHeight,E=a.fillColor,f=void 0===E?"transparent":E,v=a.imageSmoothingEnabled,C=void 0===v||v,T=a.imageSmoothingQuality,N=void 0===T?"low":T,A=a.maxWidth,_=void 0===A?1/0:A,O=a.maxHeight,b=void 0===O?1/0:O,w=a.minWidth,S=void 0===w?0:w,y=a.minHeight,D=void 0===y?0:y,M=document.createElement("canvas"),x=M.getContext("2d"),I=getAdjustedSizes({aspectRatio:m,width:_,height:b}),R=getAdjustedSizes({aspectRatio:m,width:S,height:D},"cover"),L=Math.min(I.width,Math.max(R.width,u)),P=Math.min(I.height,Math.max(R.height,g)),H=getAdjustedSizes({aspectRatio:n,width:_,height:b}),W=getAdjustedSizes({aspectRatio:n,width:S,height:D},"cover"),B=Math.min(H.width,Math.max(W.width,r)),V=Math.min(H.height,Math.max(W.height,o)),k=[-B/2,-V/2,B,V];return M.width=normalizeDecimalNumber(L),M.height=normalizeDecimalNumber(P),x.fillStyle=f,x.fillRect(0,0,L,P),x.save(),x.translate(L/2,P/2),x.rotate(h*Math.PI/180),x.scale(d,p),x.imageSmoothingEnabled=C,x.imageSmoothingQuality=N,x.drawImage.apply(x,[t].concat(_toConsumableArray(k.map((function(t){return Math.floor(normalizeDecimalNumber(t))}))))),x.restore(),M}var fromCharCode=String.fromCharCode;function getStringFromCharCode(t,e,i){var a="";i+=e;for(var n=e;n<i;n+=1)a+=fromCharCode(t.getUint8(n));return a}var REGEXP_DATA_URL_HEAD=/^data:.*,/;function dataURLToArrayBuffer(t){var e=t.replace(REGEXP_DATA_URL_HEAD,""),i=atob(e),a=new ArrayBuffer(i.length),n=new Uint8Array(a);return forEach(n,(function(t,e){n[e]=i.charCodeAt(e)})),a}function arrayBufferToDataURL(t,e){for(var i=[],a=new Uint8Array(t);a.length>0;)i.push(fromCharCode.apply(null,toArray(a.subarray(0,8192)))),a=a.subarray(8192);return"data:".concat(e,";base64,").concat(btoa(i.join("")))}function resetAndGetOrientation(t){var e,i=new DataView(t);try{var a,n,r;if(255===i.getUint8(0)&&216===i.getUint8(1))for(var o=i.byteLength,s=2;s+1<o;){if(255===i.getUint8(s)&&225===i.getUint8(s+1)){n=s;break}s+=1}if(n){var h=n+10;if("Exif"===getStringFromCharCode(i,n+4,4)){var c=i.getUint16(h);if(((a=18761===c)||19789===c)&&42===i.getUint16(h+2,a)){var d=i.getUint32(h+4,a);d>=8&&(r=h+d)}}}if(r){var l,p,m=i.getUint16(r,a);for(p=0;p<m;p+=1)if(l=r+12*p+2,274===i.getUint16(l,a)){l+=8,e=i.getUint16(l,a),i.setUint16(l,1,a);break}}}catch(t){e=1}return e}function parseOrientation(t){var e=0,i=1,a=1;switch(t){case 2:i=-1;break;case 3:e=-180;break;case 4:a=-1;break;case 5:e=90,a=-1;break;case 6:e=90;break;case 7:e=90,i=-1;break;case 8:e=-90}return{rotate:e,scaleX:i,scaleY:a}}var render={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,e=this.options,i=this.container,a=this.cropper,n=Number(e.minContainerWidth),r=Number(e.minContainerHeight);addClass(a,CLASS_HIDDEN),removeClass(t,CLASS_HIDDEN);var o={width:Math.max(i.offsetWidth,n>=0?n:MIN_CONTAINER_WIDTH),height:Math.max(i.offsetHeight,r>=0?r:MIN_CONTAINER_HEIGHT)};this.containerData=o,setStyle(a,{width:o.width,height:o.height}),addClass(t,CLASS_HIDDEN),removeClass(a,CLASS_HIDDEN)},initCanvas:function(){var t=this.containerData,e=this.imageData,i=this.options.viewMode,a=Math.abs(e.rotate)%180==90,n=a?e.naturalHeight:e.naturalWidth,r=a?e.naturalWidth:e.naturalHeight,o=n/r,s=t.width,h=t.height;t.height*o>t.width?3===i?s=t.height*o:h=t.width/o:3===i?h=t.width/o:s=t.height*o;var c={aspectRatio:o,naturalWidth:n,naturalHeight:r,width:s,height:h};this.canvasData=c,this.limited=1===i||2===i,this.limitCanvas(!0,!0),c.width=Math.min(Math.max(c.width,c.minWidth),c.maxWidth),c.height=Math.min(Math.max(c.height,c.minHeight),c.maxHeight),c.left=(t.width-c.width)/2,c.top=(t.height-c.height)/2,c.oldLeft=c.left,c.oldTop=c.top,this.initialCanvasData=assign({},c)},limitCanvas:function(t,e){var i=this.options,a=this.containerData,n=this.canvasData,r=this.cropBoxData,o=i.viewMode,s=n.aspectRatio,h=this.cropped&&r;if(t){var c=Number(i.minCanvasWidth)||0,d=Number(i.minCanvasHeight)||0;o>1?(c=Math.max(c,a.width),d=Math.max(d,a.height),3===o&&(d*s>c?c=d*s:d=c/s)):o>0&&(c?c=Math.max(c,h?r.width:0):d?d=Math.max(d,h?r.height:0):h&&(c=r.width,(d=r.height)*s>c?c=d*s:d=c/s));var l=getAdjustedSizes({aspectRatio:s,width:c,height:d});c=l.width,d=l.height,n.minWidth=c,n.minHeight=d,n.maxWidth=1/0,n.maxHeight=1/0}if(e)if(o>(h?0:1)){var p=a.width-n.width,m=a.height-n.height;n.minLeft=Math.min(0,p),n.minTop=Math.min(0,m),n.maxLeft=Math.max(0,p),n.maxTop=Math.max(0,m),h&&this.limited&&(n.minLeft=Math.min(r.left,r.left+(r.width-n.width)),n.minTop=Math.min(r.top,r.top+(r.height-n.height)),n.maxLeft=r.left,n.maxTop=r.top,2===o&&(n.width>=a.width&&(n.minLeft=Math.min(0,p),n.maxLeft=Math.max(0,p)),n.height>=a.height&&(n.minTop=Math.min(0,m),n.maxTop=Math.max(0,m))))}else n.minLeft=-n.width,n.minTop=-n.height,n.maxLeft=a.width,n.maxTop=a.height},renderCanvas:function(t,e){var i=this.canvasData,a=this.imageData;if(e){var n=getRotatedSizes({width:a.naturalWidth*Math.abs(a.scaleX||1),height:a.naturalHeight*Math.abs(a.scaleY||1),degree:a.rotate||0}),r=n.width,o=n.height,s=i.width*(r/i.naturalWidth),h=i.height*(o/i.naturalHeight);i.left-=(s-i.width)/2,i.top-=(h-i.height)/2,i.width=s,i.height=h,i.aspectRatio=r/o,i.naturalWidth=r,i.naturalHeight=o,this.limitCanvas(!0,!1)}(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCanvas(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,setStyle(this.canvas,assign({width:i.width,height:i.height},getTransforms({translateX:i.left,translateY:i.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var e=this.canvasData,i=this.imageData,a=i.naturalWidth*(e.width/e.naturalWidth),n=i.naturalHeight*(e.height/e.naturalHeight);assign(i,{width:a,height:n,left:(e.width-a)/2,top:(e.height-n)/2}),setStyle(this.image,assign({width:i.width,height:i.height},getTransforms(assign({translateX:i.left,translateY:i.top},i)))),t&&this.output()},initCropBox:function(){var t=this.options,e=this.canvasData,i=t.aspectRatio||t.initialAspectRatio,a=Number(t.autoCropArea)||.8,n={width:e.width,height:e.height};i&&(e.height*i>e.width?n.height=n.width/i:n.width=n.height*i),this.cropBoxData=n,this.limitCropBox(!0,!0),n.width=Math.min(Math.max(n.width,n.minWidth),n.maxWidth),n.height=Math.min(Math.max(n.height,n.minHeight),n.maxHeight),n.width=Math.max(n.minWidth,n.width*a),n.height=Math.max(n.minHeight,n.height*a),n.left=e.left+(e.width-n.width)/2,n.top=e.top+(e.height-n.height)/2,n.oldLeft=n.left,n.oldTop=n.top,this.initialCropBoxData=assign({},n)},limitCropBox:function(t,e){var i=this.options,a=this.containerData,n=this.canvasData,r=this.cropBoxData,o=this.limited,s=i.aspectRatio;if(t){var h=Number(i.minCropBoxWidth)||0,c=Number(i.minCropBoxHeight)||0,d=o?Math.min(a.width,n.width,n.width+n.left,a.width-n.left):a.width,l=o?Math.min(a.height,n.height,n.height+n.top,a.height-n.top):a.height;h=Math.min(h,a.width),c=Math.min(c,a.height),s&&(h&&c?c*s>h?c=h/s:h=c*s:h?c=h/s:c&&(h=c*s),l*s>d?l=d/s:d=l*s),r.minWidth=Math.min(h,d),r.minHeight=Math.min(c,l),r.maxWidth=d,r.maxHeight=l}e&&(o?(r.minLeft=Math.max(0,n.left),r.minTop=Math.max(0,n.top),r.maxLeft=Math.min(a.width,n.left+n.width)-r.width,r.maxTop=Math.min(a.height,n.top+n.height)-r.height):(r.minLeft=0,r.minTop=0,r.maxLeft=a.width-r.width,r.maxTop=a.height-r.height))},renderCropBox:function(){var t=this.options,e=this.containerData,i=this.cropBoxData;(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCropBox(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,t.movable&&t.cropBoxMovable&&setData(this.face,DATA_ACTION,i.width>=e.width&&i.height>=e.height?ACTION_MOVE:ACTION_ALL),setStyle(this.cropBox,assign({width:i.width,height:i.height},getTransforms({translateX:i.left,translateY:i.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),dispatchEvent(this.element,EVENT_CROP,this.getData())}},preview={initPreview:function(){var t=this.element,e=this.crossOrigin,i=this.options.preview,a=e?this.crossOriginUrl:this.url,n=t.alt||"The image to preview",r=document.createElement("img");if(e&&(r.crossOrigin=e),r.src=a,r.alt=n,this.viewBox.appendChild(r),this.viewBoxImage=r,i){var o=i;"string"==typeof i?o=t.ownerDocument.querySelectorAll(i):i.querySelector&&(o=[i]),this.previews=o,forEach(o,(function(t){var i=document.createElement("img");setData(t,DATA_PREVIEW,{width:t.offsetWidth,height:t.offsetHeight,html:t.innerHTML}),e&&(i.crossOrigin=e),i.src=a,i.alt=n,i.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',t.innerHTML="",t.appendChild(i)}))}},resetPreview:function(){forEach(this.previews,(function(t){var e=getData(t,DATA_PREVIEW);setStyle(t,{width:e.width,height:e.height}),t.innerHTML=e.html,removeData(t,DATA_PREVIEW)}))},preview:function(){var t=this.imageData,e=this.canvasData,i=this.cropBoxData,a=i.width,n=i.height,r=t.width,o=t.height,s=i.left-e.left-t.left,h=i.top-e.top-t.top;this.cropped&&!this.disabled&&(setStyle(this.viewBoxImage,assign({width:r,height:o},getTransforms(assign({translateX:-s,translateY:-h},t)))),forEach(this.previews,(function(e){var i=getData(e,DATA_PREVIEW),c=i.width,d=i.height,l=c,p=d,m=1;a&&(p=n*(m=c/a)),n&&p>d&&(l=a*(m=d/n),p=d),setStyle(e,{width:l,height:p}),setStyle(e.getElementsByTagName("img")[0],assign({width:r*m,height:o*m},getTransforms(assign({translateX:-s*m,translateY:-h*m},t))))})))}},events={bind:function(){var t=this.element,e=this.options,i=this.cropper;isFunction(e.cropstart)&&addListener(t,EVENT_CROP_START,e.cropstart),isFunction(e.cropmove)&&addListener(t,EVENT_CROP_MOVE,e.cropmove),isFunction(e.cropend)&&addListener(t,EVENT_CROP_END,e.cropend),isFunction(e.crop)&&addListener(t,EVENT_CROP,e.crop),isFunction(e.zoom)&&addListener(t,EVENT_ZOOM,e.zoom),addListener(i,EVENT_POINTER_DOWN,this.onCropStart=this.cropStart.bind(this)),e.zoomable&&e.zoomOnWheel&&addListener(i,EVENT_WHEEL,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&addListener(i,EVENT_DBLCLICK,this.onDblclick=this.dblclick.bind(this)),addListener(t.ownerDocument,EVENT_POINTER_MOVE,this.onCropMove=this.cropMove.bind(this)),addListener(t.ownerDocument,EVENT_POINTER_UP,this.onCropEnd=this.cropEnd.bind(this)),e.responsive&&addListener(window,EVENT_RESIZE,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,e=this.options,i=this.cropper;isFunction(e.cropstart)&&removeListener(t,EVENT_CROP_START,e.cropstart),isFunction(e.cropmove)&&removeListener(t,EVENT_CROP_MOVE,e.cropmove),isFunction(e.cropend)&&removeListener(t,EVENT_CROP_END,e.cropend),isFunction(e.crop)&&removeListener(t,EVENT_CROP,e.crop),isFunction(e.zoom)&&removeListener(t,EVENT_ZOOM,e.zoom),removeListener(i,EVENT_POINTER_DOWN,this.onCropStart),e.zoomable&&e.zoomOnWheel&&removeListener(i,EVENT_WHEEL,this.onWheel,{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&removeListener(i,EVENT_DBLCLICK,this.onDblclick),removeListener(t.ownerDocument,EVENT_POINTER_MOVE,this.onCropMove),removeListener(t.ownerDocument,EVENT_POINTER_UP,this.onCropEnd),e.responsive&&removeListener(window,EVENT_RESIZE,this.onResize)}},handlers={resize:function(){if(!this.disabled){var t,e,i=this.options,a=this.container,n=this.containerData,r=a.offsetWidth/n.width,o=a.offsetHeight/n.height,s=Math.abs(r-1)>Math.abs(o-1)?r:o;if(1!==s)i.restore&&(t=this.getCanvasData(),e=this.getCropBoxData()),this.render(),i.restore&&(this.setCanvasData(forEach(t,(function(e,i){t[i]=e*s}))),this.setCropBoxData(forEach(e,(function(t,i){e[i]=t*s}))))}},dblclick:function(){this.disabled||this.options.dragMode===DRAG_MODE_NONE||this.setDragMode(hasClass(this.dragBox,CLASS_CROP)?DRAG_MODE_MOVE:DRAG_MODE_CROP)},wheel:function(t){var e=this,i=Number(this.options.wheelZoomRatio)||.1,a=1;this.disabled||(t.preventDefault(),this.wheeling||(this.wheeling=!0,setTimeout((function(){e.wheeling=!1}),50),t.deltaY?a=t.deltaY>0?1:-1:t.wheelDelta?a=-t.wheelDelta/120:t.detail&&(a=t.detail>0?1:-1),this.zoom(-a*i,t)))},cropStart:function(t){var e=t.buttons,i=t.button;if(!(this.disabled||("mousedown"===t.type||"pointerdown"===t.type&&"mouse"===t.pointerType)&&(isNumber(e)&&1!==e||isNumber(i)&&0!==i||t.ctrlKey))){var a,n=this.options,r=this.pointers;t.changedTouches?forEach(t.changedTouches,(function(t){r[t.identifier]=getPointer(t)})):r[t.pointerId||0]=getPointer(t),a=Object.keys(r).length>1&&n.zoomable&&n.zoomOnTouch?ACTION_ZOOM:getData(t.target,DATA_ACTION),REGEXP_ACTIONS.test(a)&&!1!==dispatchEvent(this.element,EVENT_CROP_START,{originalEvent:t,action:a})&&(t.preventDefault(),this.action=a,this.cropping=!1,a===ACTION_CROP&&(this.cropping=!0,addClass(this.dragBox,CLASS_MODAL)))}},cropMove:function(t){var e=this.action;if(!this.disabled&&e){var i=this.pointers;t.preventDefault(),!1!==dispatchEvent(this.element,EVENT_CROP_MOVE,{originalEvent:t,action:e})&&(t.changedTouches?forEach(t.changedTouches,(function(t){assign(i[t.identifier]||{},getPointer(t,!0))})):assign(i[t.pointerId||0]||{},getPointer(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var e=this.action,i=this.pointers;t.changedTouches?forEach(t.changedTouches,(function(t){delete i[t.identifier]})):delete i[t.pointerId||0],e&&(t.preventDefault(),Object.keys(i).length||(this.action=""),this.cropping&&(this.cropping=!1,toggleClass(this.dragBox,CLASS_MODAL,this.cropped&&this.options.modal)),dispatchEvent(this.element,EVENT_CROP_END,{originalEvent:t,action:e}))}}},change={change:function(t){var e,i=this.options,a=this.canvasData,n=this.containerData,r=this.cropBoxData,o=this.pointers,s=this.action,h=i.aspectRatio,c=r.left,d=r.top,l=r.width,p=r.height,m=c+l,u=d+p,g=0,E=0,f=n.width,v=n.height,C=!0;!h&&t.shiftKey&&(h=l&&p?l/p:1),this.limited&&(g=r.minLeft,E=r.minTop,f=g+Math.min(n.width,a.width,a.left+a.width),v=E+Math.min(n.height,a.height,a.top+a.height));var T=o[Object.keys(o)[0]],N={x:T.endX-T.startX,y:T.endY-T.startY},A=function(t){switch(t){case ACTION_EAST:m+N.x>f&&(N.x=f-m);break;case ACTION_WEST:c+N.x<g&&(N.x=g-c);break;case ACTION_NORTH:d+N.y<E&&(N.y=E-d);break;case ACTION_SOUTH:u+N.y>v&&(N.y=v-u)}};switch(s){case ACTION_ALL:c+=N.x,d+=N.y;break;case ACTION_EAST:if(N.x>=0&&(m>=f||h&&(d<=E||u>=v))){C=!1;break}A(ACTION_EAST),(l+=N.x)<0&&(s=ACTION_WEST,c-=l=-l),h&&(p=l/h,d+=(r.height-p)/2);break;case ACTION_NORTH:if(N.y<=0&&(d<=E||h&&(c<=g||m>=f))){C=!1;break}A(ACTION_NORTH),p-=N.y,d+=N.y,p<0&&(s=ACTION_SOUTH,d-=p=-p),h&&(l=p*h,c+=(r.width-l)/2);break;case ACTION_WEST:if(N.x<=0&&(c<=g||h&&(d<=E||u>=v))){C=!1;break}A(ACTION_WEST),l-=N.x,c+=N.x,l<0&&(s=ACTION_EAST,c-=l=-l),h&&(p=l/h,d+=(r.height-p)/2);break;case ACTION_SOUTH:if(N.y>=0&&(u>=v||h&&(c<=g||m>=f))){C=!1;break}A(ACTION_SOUTH),(p+=N.y)<0&&(s=ACTION_NORTH,d-=p=-p),h&&(l=p*h,c+=(r.width-l)/2);break;case ACTION_NORTH_EAST:if(h){if(N.y<=0&&(d<=E||m>=f)){C=!1;break}A(ACTION_NORTH),p-=N.y,d+=N.y,l=p*h}else A(ACTION_NORTH),A(ACTION_EAST),N.x>=0?m<f?l+=N.x:N.y<=0&&d<=E&&(C=!1):l+=N.x,N.y<=0?d>E&&(p-=N.y,d+=N.y):(p-=N.y,d+=N.y);l<0&&p<0?(s=ACTION_SOUTH_WEST,d-=p=-p,c-=l=-l):l<0?(s=ACTION_NORTH_WEST,c-=l=-l):p<0&&(s=ACTION_SOUTH_EAST,d-=p=-p);break;case ACTION_NORTH_WEST:if(h){if(N.y<=0&&(d<=E||c<=g)){C=!1;break}A(ACTION_NORTH),p-=N.y,d+=N.y,l=p*h,c+=r.width-l}else A(ACTION_NORTH),A(ACTION_WEST),N.x<=0?c>g?(l-=N.x,c+=N.x):N.y<=0&&d<=E&&(C=!1):(l-=N.x,c+=N.x),N.y<=0?d>E&&(p-=N.y,d+=N.y):(p-=N.y,d+=N.y);l<0&&p<0?(s=ACTION_SOUTH_EAST,d-=p=-p,c-=l=-l):l<0?(s=ACTION_NORTH_EAST,c-=l=-l):p<0&&(s=ACTION_SOUTH_WEST,d-=p=-p);break;case ACTION_SOUTH_WEST:if(h){if(N.x<=0&&(c<=g||u>=v)){C=!1;break}A(ACTION_WEST),l-=N.x,c+=N.x,p=l/h}else A(ACTION_SOUTH),A(ACTION_WEST),N.x<=0?c>g?(l-=N.x,c+=N.x):N.y>=0&&u>=v&&(C=!1):(l-=N.x,c+=N.x),N.y>=0?u<v&&(p+=N.y):p+=N.y;l<0&&p<0?(s=ACTION_NORTH_EAST,d-=p=-p,c-=l=-l):l<0?(s=ACTION_SOUTH_EAST,c-=l=-l):p<0&&(s=ACTION_NORTH_WEST,d-=p=-p);break;case ACTION_SOUTH_EAST:if(h){if(N.x>=0&&(m>=f||u>=v)){C=!1;break}A(ACTION_EAST),p=(l+=N.x)/h}else A(ACTION_SOUTH),A(ACTION_EAST),N.x>=0?m<f?l+=N.x:N.y>=0&&u>=v&&(C=!1):l+=N.x,N.y>=0?u<v&&(p+=N.y):p+=N.y;l<0&&p<0?(s=ACTION_NORTH_WEST,d-=p=-p,c-=l=-l):l<0?(s=ACTION_SOUTH_WEST,c-=l=-l):p<0&&(s=ACTION_NORTH_EAST,d-=p=-p);break;case ACTION_MOVE:this.move(N.x,N.y),C=!1;break;case ACTION_ZOOM:this.zoom(getMaxZoomRatio(o),t),C=!1;break;case ACTION_CROP:if(!N.x||!N.y){C=!1;break}e=getOffset(this.cropper),c=T.startX-e.left,d=T.startY-e.top,l=r.minWidth,p=r.minHeight,N.x>0?s=N.y>0?ACTION_SOUTH_EAST:ACTION_NORTH_EAST:N.x<0&&(c-=l,s=N.y>0?ACTION_SOUTH_WEST:ACTION_NORTH_WEST),N.y<0&&(d-=p),this.cropped||(removeClass(this.cropBox,CLASS_HIDDEN),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0))}C&&(r.width=l,r.height=p,r.left=c,r.top=d,this.action=s,this.renderCropBox()),forEach(o,(function(t){t.startX=t.endX,t.startY=t.endY}))}},methods={crop:function(){return!this.ready||this.cropped||this.disabled||(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&addClass(this.dragBox,CLASS_MODAL),removeClass(this.cropBox,CLASS_HIDDEN),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=assign({},this.initialImageData),this.canvasData=assign({},this.initialCanvasData),this.cropBoxData=assign({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(assign(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),removeClass(this.dragBox,CLASS_MODAL),addClass(this.cropBox,CLASS_HIDDEN)),this},replace:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return!this.disabled&&t&&(this.isImg&&(this.element.src=t),e?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,forEach(this.previews,(function(e){e.getElementsByTagName("img")[0].src=t})))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,removeClass(this.cropper,CLASS_DISABLED)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,addClass(this.cropper,CLASS_DISABLED)),this},destroy:function(){var t=this.element;return t[NAMESPACE]?(t[NAMESPACE]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.canvasData,a=i.left,n=i.top;return this.moveTo(isUndefined(t)?t:a+Number(t),isUndefined(e)?e:n+Number(e))},moveTo:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.canvasData,a=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.movable&&(isNumber(t)&&(i.left=t,a=!0),isNumber(e)&&(i.top=e,a=!0),a&&this.renderCanvas(!0)),this},zoom:function(t,e){var i=this.canvasData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(i.width*t/i.naturalWidth,null,e)},zoomTo:function(t,e,i){var a=this.options,n=this.canvasData,r=n.width,o=n.height,s=n.naturalWidth,h=n.naturalHeight;if((t=Number(t))>=0&&this.ready&&!this.disabled&&a.zoomable){var c=s*t,d=h*t;if(!1===dispatchEvent(this.element,EVENT_ZOOM,{ratio:t,oldRatio:r/s,originalEvent:i}))return this;if(i){var l=this.pointers,p=getOffset(this.cropper),m=l&&Object.keys(l).length?getPointersCenter(l):{pageX:i.pageX,pageY:i.pageY};n.left-=(c-r)*((m.pageX-p.left-n.left)/r),n.top-=(d-o)*((m.pageY-p.top-n.top)/o)}else isPlainObject(e)&&isNumber(e.x)&&isNumber(e.y)?(n.left-=(c-r)*((e.x-n.left)/r),n.top-=(d-o)*((e.y-n.top)/o)):(n.left-=(c-r)/2,n.top-=(d-o)/2);n.width=c,n.height=d,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return isNumber(t=Number(t))&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var e=this.imageData.scaleY;return this.scale(t,isNumber(e)?e:1)},scaleY:function(t){var e=this.imageData.scaleX;return this.scale(isNumber(e)?e:1,t)},scale:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.imageData,a=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.scalable&&(isNumber(t)&&(i.scaleX=t,a=!0),isNumber(e)&&(i.scaleY=e,a=!0),a&&this.renderCanvas(!0,!0)),this},getData:function(){var t,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=this.options,a=this.imageData,n=this.canvasData,r=this.cropBoxData;if(this.ready&&this.cropped){t={x:r.left-n.left,y:r.top-n.top,width:r.width,height:r.height};var o=a.width/a.naturalWidth;if(forEach(t,(function(e,i){t[i]=e/o})),e){var s=Math.round(t.y+t.height),h=Math.round(t.x+t.width);t.x=Math.round(t.x),t.y=Math.round(t.y),t.width=h-t.x,t.height=s-t.y}}else t={x:0,y:0,width:0,height:0};return i.rotatable&&(t.rotate=a.rotate||0),i.scalable&&(t.scaleX=a.scaleX||1,t.scaleY=a.scaleY||1),t},setData:function(t){var e=this.options,i=this.imageData,a=this.canvasData,n={};if(this.ready&&!this.disabled&&isPlainObject(t)){var r=!1;e.rotatable&&isNumber(t.rotate)&&t.rotate!==i.rotate&&(i.rotate=t.rotate,r=!0),e.scalable&&(isNumber(t.scaleX)&&t.scaleX!==i.scaleX&&(i.scaleX=t.scaleX,r=!0),isNumber(t.scaleY)&&t.scaleY!==i.scaleY&&(i.scaleY=t.scaleY,r=!0)),r&&this.renderCanvas(!0,!0);var o=i.width/i.naturalWidth;isNumber(t.x)&&(n.left=t.x*o+a.left),isNumber(t.y)&&(n.top=t.y*o+a.top),isNumber(t.width)&&(n.width=t.width*o),isNumber(t.height)&&(n.height=t.height*o),this.setCropBoxData(n)}return this},getContainerData:function(){return this.ready?assign({},this.containerData):{}},getImageData:function(){return this.sized?assign({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,e={};return this.ready&&forEach(["left","top","width","height","naturalWidth","naturalHeight"],(function(i){e[i]=t[i]})),e},setCanvasData:function(t){var e=this.canvasData,i=e.aspectRatio;return this.ready&&!this.disabled&&isPlainObject(t)&&(isNumber(t.left)&&(e.left=t.left),isNumber(t.top)&&(e.top=t.top),isNumber(t.width)?(e.width=t.width,e.height=t.width/i):isNumber(t.height)&&(e.height=t.height,e.width=t.height*i),this.renderCanvas(!0)),this},getCropBoxData:function(){var t,e=this.cropBoxData;return this.ready&&this.cropped&&(t={left:e.left,top:e.top,width:e.width,height:e.height}),t||{}},setCropBoxData:function(t){var e,i,a=this.cropBoxData,n=this.options.aspectRatio;return this.ready&&this.cropped&&!this.disabled&&isPlainObject(t)&&(isNumber(t.left)&&(a.left=t.left),isNumber(t.top)&&(a.top=t.top),isNumber(t.width)&&t.width!==a.width&&(e=!0,a.width=t.width),isNumber(t.height)&&t.height!==a.height&&(i=!0,a.height=t.height),n&&(e?a.height=a.width/n:i&&(a.width=a.height*n)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var e=this.canvasData,i=getSourceCanvas(this.image,this.imageData,e,t);if(!this.cropped)return i;var a=this.getData(),n=a.x,r=a.y,o=a.width,s=a.height,h=i.width/Math.floor(e.naturalWidth);1!==h&&(n*=h,r*=h,o*=h,s*=h);var c=o/s,d=getAdjustedSizes({aspectRatio:c,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),l=getAdjustedSizes({aspectRatio:c,width:t.minWidth||0,height:t.minHeight||0},"cover"),p=getAdjustedSizes({aspectRatio:c,width:t.width||(1!==h?i.width:o),height:t.height||(1!==h?i.height:s)}),m=p.width,u=p.height;m=Math.min(d.width,Math.max(l.width,m)),u=Math.min(d.height,Math.max(l.height,u));var g=document.createElement("canvas"),E=g.getContext("2d");g.width=normalizeDecimalNumber(m),g.height=normalizeDecimalNumber(u),E.fillStyle=t.fillColor||"transparent",E.fillRect(0,0,m,u);var f=t.imageSmoothingEnabled,v=void 0===f||f,C=t.imageSmoothingQuality;E.imageSmoothingEnabled=v,C&&(E.imageSmoothingQuality=C);var T,N,A,_,O,b,w=i.width,S=i.height,y=n,D=r;y<=-o||y>w?(y=0,T=0,A=0,O=0):y<=0?(A=-y,y=0,O=T=Math.min(w,o+y)):y<=w&&(A=0,O=T=Math.min(o,w-y)),T<=0||D<=-s||D>S?(D=0,N=0,_=0,b=0):D<=0?(_=-D,D=0,b=N=Math.min(S,s+D)):D<=S&&(_=0,b=N=Math.min(s,S-D));var M=[y,D,T,N];if(O>0&&b>0){var x=m/o;M.push(A*x,_*x,O*x,b*x)}return E.drawImage.apply(E,[i].concat(_toConsumableArray(M.map((function(t){return Math.floor(normalizeDecimalNumber(t))}))))),g},setAspectRatio:function(t){var e=this.options;return this.disabled||isUndefined(t)||(e.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var e=this.options,i=this.dragBox,a=this.face;if(this.ready&&!this.disabled){var n=t===DRAG_MODE_CROP,r=e.movable&&t===DRAG_MODE_MOVE;t=n||r?t:DRAG_MODE_NONE,e.dragMode=t,setData(i,DATA_ACTION,t),toggleClass(i,CLASS_CROP,n),toggleClass(i,CLASS_MOVE,r),e.cropBoxMovable||(setData(a,DATA_ACTION,t),toggleClass(a,CLASS_CROP,n),toggleClass(a,CLASS_MOVE,r))}return this}},AnotherCropper=WINDOW.Cropper,Cropper=function(){function t(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(_classCallCheck(this,t),!e||!REGEXP_TAG_NAME.test(e.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=e,this.options=assign({},DEFAULTS,isPlainObject(i)&&i),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return _createClass(t,[{key:"init",value:function(){var t,e=this.element,i=e.tagName.toLowerCase();if(!e[NAMESPACE]){if(e[NAMESPACE]=this,"img"===i){if(this.isImg=!0,t=e.getAttribute("src")||"",this.originalUrl=t,!t)return;t=e.src}else"canvas"===i&&window.HTMLCanvasElement&&(t=e.toDataURL());this.load(t)}}},{key:"load",value:function(t){var e=this;if(t){this.url=t,this.imageData={};var i=this.element,a=this.options;if(a.rotatable||a.scalable||(a.checkOrientation=!1),a.checkOrientation&&window.ArrayBuffer)if(REGEXP_DATA_URL.test(t))REGEXP_DATA_URL_JPEG.test(t)?this.read(dataURLToArrayBuffer(t)):this.clone();else{var n=new XMLHttpRequest,r=this.clone.bind(this);this.reloading=!0,this.xhr=n,n.onabort=r,n.onerror=r,n.ontimeout=r,n.onprogress=function(){n.getResponseHeader("content-type")!==MIME_TYPE_JPEG&&n.abort()},n.onload=function(){e.read(n.response)},n.onloadend=function(){e.reloading=!1,e.xhr=null},a.checkCrossOrigin&&isCrossOriginURL(t)&&i.crossOrigin&&(t=addTimestamp(t)),n.open("GET",t,!0),n.responseType="arraybuffer",n.withCredentials="use-credentials"===i.crossOrigin,n.send()}else this.clone()}}},{key:"read",value:function(t){var e=this.options,i=this.imageData,a=resetAndGetOrientation(t),n=0,r=1,o=1;if(a>1){this.url=arrayBufferToDataURL(t,MIME_TYPE_JPEG);var s=parseOrientation(a);n=s.rotate,r=s.scaleX,o=s.scaleY}e.rotatable&&(i.rotate=n),e.scalable&&(i.scaleX=r,i.scaleY=o),this.clone()}},{key:"clone",value:function(){var t=this.element,e=this.url,i=t.crossOrigin,a=e;this.options.checkCrossOrigin&&isCrossOriginURL(e)&&(i||(i="anonymous"),a=addTimestamp(e)),this.crossOrigin=i,this.crossOriginUrl=a;var n=document.createElement("img");i&&(n.crossOrigin=i),n.src=a||e,n.alt=t.alt||"The image to crop",this.image=n,n.onload=this.start.bind(this),n.onerror=this.stop.bind(this),addClass(n,CLASS_HIDE),t.parentNode.insertBefore(n,t.nextSibling)}},{key:"start",value:function(){var t=this,e=this.image;e.onload=null,e.onerror=null,this.sizing=!0;var i=WINDOW.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(WINDOW.navigator.userAgent),a=function(e,i){assign(t.imageData,{naturalWidth:e,naturalHeight:i,aspectRatio:e/i}),t.initialImageData=assign({},t.imageData),t.sizing=!1,t.sized=!0,t.build()};if(!e.naturalWidth||i){var n=document.createElement("img"),r=document.body||document.documentElement;this.sizingImage=n,n.onload=function(){a(n.width,n.height),i||r.removeChild(n)},n.src=e.src,i||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",r.appendChild(n))}else a(e.naturalWidth,e.naturalHeight)}},{key:"stop",value:function(){var t=this.image;t.onload=null,t.onerror=null,t.parentNode.removeChild(t),this.image=null}},{key:"build",value:function(){if(this.sized&&!this.ready){var t=this.element,e=this.options,i=this.image,a=t.parentNode,n=document.createElement("div");n.innerHTML=TEMPLATE;var r=n.querySelector(".".concat(NAMESPACE,"-container")),o=r.querySelector(".".concat(NAMESPACE,"-canvas")),s=r.querySelector(".".concat(NAMESPACE,"-drag-box")),h=r.querySelector(".".concat(NAMESPACE,"-crop-box")),c=h.querySelector(".".concat(NAMESPACE,"-face"));this.container=a,this.cropper=r,this.canvas=o,this.dragBox=s,this.cropBox=h,this.viewBox=r.querySelector(".".concat(NAMESPACE,"-view-box")),this.face=c,o.appendChild(i),addClass(t,CLASS_HIDDEN),a.insertBefore(r,t.nextSibling),this.isImg||removeClass(i,CLASS_HIDE),this.initPreview(),this.bind(),e.initialAspectRatio=Math.max(0,e.initialAspectRatio)||NaN,e.aspectRatio=Math.max(0,e.aspectRatio)||NaN,e.viewMode=Math.max(0,Math.min(3,Math.round(e.viewMode)))||0,addClass(h,CLASS_HIDDEN),e.guides||addClass(h.getElementsByClassName("".concat(NAMESPACE,"-dashed")),CLASS_HIDDEN),e.center||addClass(h.getElementsByClassName("".concat(NAMESPACE,"-center")),CLASS_HIDDEN),e.background&&addClass(r,"".concat(NAMESPACE,"-bg")),e.highlight||addClass(c,CLASS_INVISIBLE),e.cropBoxMovable&&(addClass(c,CLASS_MOVE),setData(c,DATA_ACTION,ACTION_ALL)),e.cropBoxResizable||(addClass(h.getElementsByClassName("".concat(NAMESPACE,"-line")),CLASS_HIDDEN),addClass(h.getElementsByClassName("".concat(NAMESPACE,"-point")),CLASS_HIDDEN)),this.render(),this.ready=!0,this.setDragMode(e.dragMode),e.autoCrop&&this.crop(),this.setData(e.data),isFunction(e.ready)&&addListener(t,EVENT_READY,e.ready,{once:!0}),dispatchEvent(t,EVENT_READY)}}},{key:"unbuild",value:function(){this.ready&&(this.ready=!1,this.unbind(),this.resetPreview(),this.cropper.parentNode.removeChild(this.cropper),removeClass(this.element,CLASS_HIDDEN))}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],[{key:"noConflict",value:function(){return window.Cropper=AnotherCropper,t}},{key:"setDefaults",value:function(t){assign(DEFAULTS,isPlainObject(t)&&t)}}]),t}();assign(Cropper.prototype,render,preview,events,handlers,change,methods);export default Cropper;
