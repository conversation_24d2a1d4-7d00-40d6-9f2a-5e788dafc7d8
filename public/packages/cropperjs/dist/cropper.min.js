!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).Cropper=e()}(this,(function(){"use strict";function t(t,e){var i,a=Object.keys(t);return Object.getOwnPropertySymbols&&(i=Object.getOwnPropertySymbols(t),e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)),a}function e(e){for(var i=1;i<arguments.length;i++){var a=null!=arguments[i]?arguments[i]:{};i%2?t(Object(a),!0).forEach((function(t){var i,n;i=e,t=a[n=t],n in i?Object.defineProperty(i,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):i[n]=t})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function a(t,e){for(var i=0;i<e.length;i++){var a=e[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,a.key,a)}}function n(t){return function(t){if(Array.isArray(t))return o(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return o(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(i="Object"===i&&t.constructor?t.constructor.name:i)||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?o(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,a=new Array(e);i<e;i++)a[i]=t[i];return a}var h=(Rt="undefined"!=typeof window&&void 0!==window.document)?window:{},r=!(!Rt||!h.document.documentElement)&&"ontouchstart"in h.document.documentElement,s=Rt&&"PointerEvent"in h,c="cropper",d="all",l="crop",p="move",m="zoom",u="e",g="w",f="s",v="n",w="ne",b="nw",y="se",x="sw",M="".concat(c,"-crop"),C="".concat(c,"-disabled"),D="".concat(c,"-hidden"),B="".concat(c,"-hide"),k="".concat(c,"-invisible"),O="".concat(c,"-modal"),T="".concat(c,"-move"),E="".concat(c,"Action"),W="".concat(c,"Preview"),H="crop",N="move",L="none",z="crop",Y="cropend",X="cropmove",R="cropstart",S="dblclick",A=s?"pointerdown":r?"touchstart":"mousedown",j=s?"pointermove":r?"touchmove":"mousemove",I=s?"pointerup pointercancel":r?"touchend touchcancel":"mouseup",P="zoom",U="image/jpeg",q=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,$=/^data:/,Q=/^data:image\/jpeg;base64,/,K=/^img|canvas$/i,Z={viewMode:0,dragMode:H,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:200,minContainerHeight:100,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},G=Number.isNaN||h.isNaN;function V(t){return"number"==typeof t&&!G(t)}var F=function(t){return 0<t&&t<1/0};function J(t){return void 0===t}function _(t){return"object"===i(t)&&null!==t}var tt=Object.prototype.hasOwnProperty;function et(t){if(!_(t))return!1;try{var e=t.constructor,i=e.prototype;return e&&i&&tt.call(i,"isPrototypeOf")}catch(t){return!1}}function it(t){return"function"==typeof t}var at=Array.prototype.slice;function nt(t){return Array.from?Array.from(t):at.call(t)}function ot(t,e){return t&&it(e)&&(Array.isArray(t)||V(t.length)?nt(t).forEach((function(i,a){e.call(t,i,a,t)})):_(t)&&Object.keys(t).forEach((function(i){e.call(t,t[i],i,t)}))),t}var ht=Object.assign||function(t){for(var e=arguments.length,i=new Array(1<e?e-1:0),a=1;a<e;a++)i[a-1]=arguments[a];return _(t)&&0<i.length&&i.forEach((function(e){_(e)&&Object.keys(e).forEach((function(i){t[i]=e[i]}))})),t},rt=/\.\d*(?:0|9){12}\d*$/;function st(t,e){return e=1<arguments.length&&void 0!==e?e:1e11,rt.test(t)?Math.round(t*e)/e:t}var ct=/^width|height|left|top|marginLeft|marginTop$/;function dt(t,e){var i=t.style;ot(e,(function(t,e){ct.test(e)&&V(t)&&(t="".concat(t,"px")),i[e]=t}))}function lt(t,e){var i;e&&(V(t.length)?ot(t,(function(t){lt(t,e)})):t.classList?t.classList.add(e):(i=t.className.trim())?i.indexOf(e)<0&&(t.className="".concat(i," ").concat(e)):t.className=e)}function pt(t,e){e&&(V(t.length)?ot(t,(function(t){pt(t,e)})):t.classList?t.classList.remove(e):0<=t.className.indexOf(e)&&(t.className=t.className.replace(e,"")))}function mt(t,e,i){e&&(V(t.length)?ot(t,(function(t){mt(t,e,i)})):(i?lt:pt)(t,e))}var ut=/([a-z\d])([A-Z])/g;function gt(t){return t.replace(ut,"$1-$2").toLowerCase()}function ft(t,e){return _(t[e])?t[e]:t.dataset?t.dataset[e]:t.getAttribute("data-".concat(gt(e)))}function vt(t,e,i){_(i)?t[e]=i:t.dataset?t.dataset[e]=i:t.setAttribute("data-".concat(gt(e)),i)}var wt,bt,yt=/\s\s*/,xt=(bt=!1,Rt&&(wt=!1,St=function(){},jt=Object.defineProperty({},"once",{get:function(){return bt=!0,wt},set:function(t){wt=t}}),h.addEventListener("test",St,jt),h.removeEventListener("test",St,jt)),bt);function Mt(t,e,i,a){var n=3<arguments.length&&void 0!==a?a:{},o=i;e.trim().split(yt).forEach((function(e){var a;xt||(a=t.listeners)&&a[e]&&a[e][i]&&(o=a[e][i],delete a[e][i],0===Object.keys(a[e]).length&&delete a[e],0===Object.keys(a).length&&delete t.listeners),t.removeEventListener(e,o,n)}))}function Ct(t,e,i,a){var n=3<arguments.length&&void 0!==a?a:{},o=i;e.trim().split(yt).forEach((function(e){var a,h;n.once&&!xt&&(a=t.listeners,o=function(){delete h[e][i],t.removeEventListener(e,o,n);for(var a=arguments.length,r=new Array(a),s=0;s<a;s++)r[s]=arguments[s];i.apply(t,r)},(h=void 0===a?{}:a)[e]||(h[e]={}),h[e][i]&&t.removeEventListener(e,h[e][i],n),h[e][i]=o,t.listeners=h),t.addEventListener(e,o,n)}))}function Dt(t,e,i){var a;return it(Event)&&it(CustomEvent)?a=new CustomEvent(e,{detail:i,bubbles:!0,cancelable:!0}):(a=document.createEvent("CustomEvent")).initCustomEvent(e,!0,!0,i),t.dispatchEvent(a)}function Bt(t){return{left:(t=t.getBoundingClientRect()).left+(window.pageXOffset-document.documentElement.clientLeft),top:t.top+(window.pageYOffset-document.documentElement.clientTop)}}var kt=h.location,Ot=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function Tt(t){return null!==(t=t.match(Ot))&&(t[1]!==kt.protocol||t[2]!==kt.hostname||t[3]!==kt.port)}function Et(t){var e="timestamp=".concat((new Date).getTime());return t+(-1===t.indexOf("?")?"?":"&")+e}function Wt(t){var e=t.rotate,i=t.scaleX,a=t.scaleY,n=t.translateX,o=t.translateY;t=[];return V(n)&&0!==n&&t.push("translateX(".concat(n,"px)")),V(o)&&0!==o&&t.push("translateY(".concat(o,"px)")),V(e)&&0!==e&&t.push("rotate(".concat(e,"deg)")),V(i)&&1!==i&&t.push("scaleX(".concat(i,")")),V(a)&&1!==a&&t.push("scaleY(".concat(a,")")),{WebkitTransform:t=t.length?t.join(" "):"none",msTransform:t,transform:t}}function Ht(t,i){var a=t.pageX,n=t.pageY;t={endX:a,endY:n};return i?t:e({startX:a,startY:n},t)}function Nt(t,e){var i=t.aspectRatio,a=t.height,n=t.width,o=1<arguments.length&&void 0!==e?e:"contain",h=F(n);t=F(a);return h&&t?(e=a*i,"contain"===o&&n<e||"cover"===o&&e<n?a=n/i:n=a*i):h?a=n/i:t&&(n=a*i),{width:n,height:a}}var Lt=String.fromCharCode,zt=/^data:.*,/;var Yt={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,e=this.options,i=this.container,a=this.cropper,n=Number(e.minContainerWidth);e=Number(e.minContainerHeight);lt(a,D),pt(t,D),e={width:Math.max(i.offsetWidth,0<=n?n:200),height:Math.max(i.offsetHeight,0<=e?e:100)},dt(a,{width:(this.containerData=e).width,height:e.height}),lt(t,D),pt(a,D)},initCanvas:function(){var t=this.containerData,e=this.imageData,i=this.options.viewMode,a=(h=Math.abs(e.rotate)%180==90)?e.naturalHeight:e.naturalWidth,n=h?e.naturalWidth:e.naturalHeight,o=a/n,h=t.width;e=t.height;t.height*o>t.width?3===i?h=t.height*o:e=t.width/o:3===i?e=t.width/o:h=t.height*o,e={aspectRatio:o,naturalWidth:a,naturalHeight:n,width:h,height:e},this.canvasData=e,this.limited=1===i||2===i,this.limitCanvas(!0,!0),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),e.left=(t.width-e.width)/2,e.top=(t.height-e.height)/2,e.oldLeft=e.left,e.oldTop=e.top,this.initialCanvasData=ht({},e)},limitCanvas:function(t,e){var i,a=this.options,n=this.containerData,o=this.canvasData,h=this.cropBoxData,r=a.viewMode,s=o.aspectRatio,c=this.cropped&&h;t&&(t=Number(a.minCanvasWidth)||0,i=Number(a.minCanvasHeight)||0,1<r?(t=Math.max(t,n.width),i=Math.max(i,n.height),3===r&&(t<i*s?t=i*s:i=t/s)):0<r&&(t?t=Math.max(t,c?h.width:0):i?i=Math.max(i,c?h.height:0):c&&((t=h.width)<(i=h.height)*s?t=i*s:i=t/s)),t=(s=Nt({aspectRatio:s,width:t,height:i})).width,i=s.height,o.minWidth=t,o.minHeight=i,o.maxWidth=1/0,o.maxHeight=1/0),e&&((c?0:1)<r?(i=n.width-o.width,e=n.height-o.height,o.minLeft=Math.min(0,i),o.minTop=Math.min(0,e),o.maxLeft=Math.max(0,i),o.maxTop=Math.max(0,e),c&&this.limited&&(o.minLeft=Math.min(h.left,h.left+(h.width-o.width)),o.minTop=Math.min(h.top,h.top+(h.height-o.height)),o.maxLeft=h.left,o.maxTop=h.top,2===r&&(o.width>=n.width&&(o.minLeft=Math.min(0,i),o.maxLeft=Math.max(0,i)),o.height>=n.height&&(o.minTop=Math.min(0,e),o.maxTop=Math.max(0,e))))):(o.minLeft=-o.width,o.minTop=-o.height,o.maxLeft=n.width,o.maxTop=n.height))},renderCanvas:function(t,e){var i,a,n=this.canvasData,o=this.imageData;e&&(i=(a=function(t){var e=t.width,i=t.height,a=t.degree;if(90==(a=Math.abs(a)%180))return{width:i,height:e};var n=a%90*Math.PI/180,o=Math.sin(n);n=e*(t=Math.cos(n))+i*o,t=e*o+i*t;return 90<a?{width:t,height:n}:{width:n,height:t}}({width:o.naturalWidth*Math.abs(o.scaleX||1),height:o.naturalHeight*Math.abs(o.scaleY||1),degree:o.rotate||0})).width,e=a.height,o=n.width*(i/n.naturalWidth),a=n.height*(e/n.naturalHeight),n.left-=(o-n.width)/2,n.top-=(a-n.height)/2,n.width=o,n.height=a,n.aspectRatio=i/e,n.naturalWidth=i,n.naturalHeight=e,this.limitCanvas(!0,!1)),(n.width>n.maxWidth||n.width<n.minWidth)&&(n.left=n.oldLeft),(n.height>n.maxHeight||n.height<n.minHeight)&&(n.top=n.oldTop),n.width=Math.min(Math.max(n.width,n.minWidth),n.maxWidth),n.height=Math.min(Math.max(n.height,n.minHeight),n.maxHeight),this.limitCanvas(!1,!0),n.left=Math.min(Math.max(n.left,n.minLeft),n.maxLeft),n.top=Math.min(Math.max(n.top,n.minTop),n.maxTop),n.oldLeft=n.left,n.oldTop=n.top,dt(this.canvas,ht({width:n.width,height:n.height},Wt({translateX:n.left,translateY:n.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var e=this.canvasData,i=this.imageData,a=i.naturalWidth*(e.width/e.naturalWidth),n=i.naturalHeight*(e.height/e.naturalHeight);ht(i,{width:a,height:n,left:(e.width-a)/2,top:(e.height-n)/2}),dt(this.image,ht({width:i.width,height:i.height},Wt(ht({translateX:i.left,translateY:i.top},i)))),t&&this.output()},initCropBox:function(){var t=this.options,e=this.canvasData,i=t.aspectRatio||t.initialAspectRatio,a=Number(t.autoCropArea)||.8;t={width:e.width,height:e.height};i&&(e.height*i>e.width?t.height=t.width/i:t.width=t.height*i),this.cropBoxData=t,this.limitCropBox(!0,!0),t.width=Math.min(Math.max(t.width,t.minWidth),t.maxWidth),t.height=Math.min(Math.max(t.height,t.minHeight),t.maxHeight),t.width=Math.max(t.minWidth,t.width*a),t.height=Math.max(t.minHeight,t.height*a),t.left=e.left+(e.width-t.width)/2,t.top=e.top+(e.height-t.height)/2,t.oldLeft=t.left,t.oldTop=t.top,this.initialCropBoxData=ht({},t)},limitCropBox:function(t,e){var i,a,n=this.options,o=this.containerData,h=this.canvasData,r=this.cropBoxData,s=this.limited,c=n.aspectRatio;t&&(i=Number(n.minCropBoxWidth)||0,a=Number(n.minCropBoxHeight)||0,t=s?Math.min(o.width,h.width,h.width+h.left,o.width-h.left):o.width,n=s?Math.min(o.height,h.height,h.height+h.top,o.height-h.top):o.height,i=Math.min(i,o.width),a=Math.min(a,o.height),c&&(i&&a?i<a*c?a=i/c:i=a*c:i?a=i/c:a&&(i=a*c),t<n*c?n=t/c:t=n*c),r.minWidth=Math.min(i,t),r.minHeight=Math.min(a,n),r.maxWidth=t,r.maxHeight=n),e&&(s?(r.minLeft=Math.max(0,h.left),r.minTop=Math.max(0,h.top),r.maxLeft=Math.min(o.width,h.left+h.width)-r.width,r.maxTop=Math.min(o.height,h.top+h.height)-r.height):(r.minLeft=0,r.minTop=0,r.maxLeft=o.width-r.width,r.maxTop=o.height-r.height))},renderCropBox:function(){var t=this.options,e=this.containerData,i=this.cropBoxData;(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCropBox(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,t.movable&&t.cropBoxMovable&&vt(this.face,E,i.width>=e.width&&i.height>=e.height?p:d),dt(this.cropBox,ht({width:i.width,height:i.height},Wt({translateX:i.left,translateY:i.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),Dt(this.element,z,this.getData())}},Xt={initPreview:function(){var t=this.element,e=this.crossOrigin,i=this.options.preview,a=e?this.crossOriginUrl:this.url,n=t.alt||"The image to preview",o=document.createElement("img");e&&(o.crossOrigin=e),o.src=a,o.alt=n,this.viewBox.appendChild(o),this.viewBoxImage=o,i&&("string"==typeof(o=i)?o=t.ownerDocument.querySelectorAll(i):i.querySelector&&(o=[i]),ot(this.previews=o,(function(t){var i=document.createElement("img");vt(t,W,{width:t.offsetWidth,height:t.offsetHeight,html:t.innerHTML}),e&&(i.crossOrigin=e),i.src=a,i.alt=n,i.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',t.innerHTML="",t.appendChild(i)})))},resetPreview:function(){ot(this.previews,(function(t){var e=ft(t,W);dt(t,{width:e.width,height:e.height}),t.innerHTML=e.html,function(t,e){if(_(t[e]))try{delete t[e]}catch(i){t[e]=void 0}else if(t.dataset)try{delete t.dataset[e]}catch(i){t.dataset[e]=void 0}else t.removeAttribute("data-".concat(gt(e)))}(t,W)}))},preview:function(){var t=this.imageData,e=this.canvasData,i=this.cropBoxData,a=i.width,n=i.height,o=t.width,h=t.height,r=i.left-e.left-t.left,s=i.top-e.top-t.top;this.cropped&&!this.disabled&&(dt(this.viewBoxImage,ht({width:o,height:h},Wt(ht({translateX:-r,translateY:-s},t)))),ot(this.previews,(function(e){var i=(p=ft(e,W)).width,c=p.height,d=i,l=c,p=1;a&&(l=n*(p=i/a)),n&&c<l&&(d=a*(p=c/n),l=c),dt(e,{width:d,height:l}),dt(e.getElementsByTagName("img")[0],ht({width:o*p,height:h*p},Wt(ht({translateX:-r*p,translateY:-s*p},t))))})))}},Rt=(s={bind:function(){var t=this.element,e=this.options,i=this.cropper;it(e.cropstart)&&Ct(t,R,e.cropstart),it(e.cropmove)&&Ct(t,X,e.cropmove),it(e.cropend)&&Ct(t,Y,e.cropend),it(e.crop)&&Ct(t,z,e.crop),it(e.zoom)&&Ct(t,P,e.zoom),Ct(i,A,this.onCropStart=this.cropStart.bind(this)),e.zoomable&&e.zoomOnWheel&&Ct(i,"wheel",this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&Ct(i,S,this.onDblclick=this.dblclick.bind(this)),Ct(t.ownerDocument,j,this.onCropMove=this.cropMove.bind(this)),Ct(t.ownerDocument,I,this.onCropEnd=this.cropEnd.bind(this)),e.responsive&&Ct(window,"resize",this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,e=this.options,i=this.cropper;it(e.cropstart)&&Mt(t,R,e.cropstart),it(e.cropmove)&&Mt(t,X,e.cropmove),it(e.cropend)&&Mt(t,Y,e.cropend),it(e.crop)&&Mt(t,z,e.crop),it(e.zoom)&&Mt(t,P,e.zoom),Mt(i,A,this.onCropStart),e.zoomable&&e.zoomOnWheel&&Mt(i,"wheel",this.onWheel,{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&Mt(i,S,this.onDblclick),Mt(t.ownerDocument,j,this.onCropMove),Mt(t.ownerDocument,I,this.onCropEnd),e.responsive&&Mt(window,"resize",this.onResize)}},r={resize:function(){var t,e,i,a,n,o,h;this.disabled||(t=this.options,e=this.container,a=this.containerData,i=e.offsetWidth/a.width,a=e.offsetHeight/a.height,1!=(n=Math.abs(i-1)>Math.abs(a-1)?i:a)&&(t.restore&&(o=this.getCanvasData(),h=this.getCropBoxData()),this.render(),t.restore&&(this.setCanvasData(ot(o,(function(t,e){o[e]=t*n}))),this.setCropBoxData(ot(h,(function(t,e){h[e]=t*n}))))))},dblclick:function(){var t,e;this.disabled||this.options.dragMode===L||this.setDragMode((t=this.dragBox,e=M,(t.classList?t.classList.contains(e):-1<t.className.indexOf(e))?N:H))},wheel:function(t){var e=this,i=Number(this.options.wheelZoomRatio)||.1,a=1;this.disabled||(t.preventDefault(),this.wheeling||(this.wheeling=!0,setTimeout((function(){e.wheeling=!1}),50),t.deltaY?a=0<t.deltaY?1:-1:t.wheelDelta?a=-t.wheelDelta/120:t.detail&&(a=0<t.detail?1:-1),this.zoom(-a*i,t)))},cropStart:function(t){var e,i=t.buttons,a=t.button;this.disabled||("mousedown"===t.type||"pointerdown"===t.type&&"mouse"===t.pointerType)&&(V(i)&&1!==i||V(a)&&0!==a||t.ctrlKey)||(a=this.options,e=this.pointers,t.changedTouches?ot(t.changedTouches,(function(t){e[t.identifier]=Ht(t)})):e[t.pointerId||0]=Ht(t),a=1<Object.keys(e).length&&a.zoomable&&a.zoomOnTouch?m:ft(t.target,E),q.test(a)&&!1!==Dt(this.element,R,{originalEvent:t,action:a})&&(t.preventDefault(),this.action=a,this.cropping=!1,a===l&&(this.cropping=!0,lt(this.dragBox,O))))},cropMove:function(t){var e,i=this.action;!this.disabled&&i&&(e=this.pointers,t.preventDefault(),!1!==Dt(this.element,X,{originalEvent:t,action:i})&&(t.changedTouches?ot(t.changedTouches,(function(t){ht(e[t.identifier]||{},Ht(t,!0))})):ht(e[t.pointerId||0]||{},Ht(t,!0)),this.change(t)))},cropEnd:function(t){var e,i;this.disabled||(e=this.action,i=this.pointers,t.changedTouches?ot(t.changedTouches,(function(t){delete i[t.identifier]})):delete i[t.pointerId||0],e&&(t.preventDefault(),Object.keys(i).length||(this.action=""),this.cropping&&(this.cropping=!1,mt(this.dragBox,O,this.cropped&&this.options.modal)),Dt(this.element,Y,{originalEvent:t,action:e})))}},{change:function(t){var i=this.options,a=this.canvasData,n=this.containerData,o=this.cropBoxData,h=this.pointers,r=this.action,s=i.aspectRatio,c=o.left,M=o.top,C=o.width,B=o.height,k=c+C,O=M+B,T=0,E=0,W=n.width,H=n.height,N=!0;function L(t){switch(t){case u:k+S.x>W&&(S.x=W-k);break;case g:c+S.x<T&&(S.x=T-c);break;case v:M+S.y<E&&(S.y=E-M);break;case f:O+S.y>H&&(S.y=H-O)}}!s&&t.shiftKey&&(s=C&&B?C/B:1),this.limited&&(T=o.minLeft,E=o.minTop,W=T+Math.min(n.width,a.width,a.left+a.width),H=E+Math.min(n.height,a.height,a.top+a.height));var z,Y,X,R=h[Object.keys(h)[0]],S={x:R.endX-R.startX,y:R.endY-R.startY};switch(r){case d:c+=S.x,M+=S.y;break;case u:if(0<=S.x&&(W<=k||s&&(M<=E||H<=O))){N=!1;break}L(u),(C+=S.x)<0&&(r=g,c-=C=-C),s&&(M+=(o.height-(B=C/s))/2);break;case v:if(S.y<=0&&(M<=E||s&&(c<=T||W<=k))){N=!1;break}L(v),B-=S.y,M+=S.y,B<0&&(r=f,M-=B=-B),s&&(c+=(o.width-(C=B*s))/2);break;case g:if(S.x<=0&&(c<=T||s&&(M<=E||H<=O))){N=!1;break}L(g),C-=S.x,c+=S.x,C<0&&(r=u,c-=C=-C),s&&(M+=(o.height-(B=C/s))/2);break;case f:if(0<=S.y&&(H<=O||s&&(c<=T||W<=k))){N=!1;break}L(f),(B+=S.y)<0&&(r=v,M-=B=-B),s&&(c+=(o.width-(C=B*s))/2);break;case w:if(s){if(S.y<=0&&(M<=E||W<=k)){N=!1;break}L(v),B-=S.y,M+=S.y,C=B*s}else L(v),L(u),!(0<=S.x)||k<W?C+=S.x:S.y<=0&&M<=E&&(N=!1),S.y<=0&&!(E<M)||(B-=S.y,M+=S.y);C<0&&B<0?(r=x,M-=B=-B,c-=C=-C):C<0?(r=b,c-=C=-C):B<0&&(r=y,M-=B=-B);break;case b:if(s){if(S.y<=0&&(M<=E||c<=T)){N=!1;break}L(v),B-=S.y,M+=S.y,c+=o.width-(C=B*s)}else L(v),L(g),!(S.x<=0)||T<c?(C-=S.x,c+=S.x):S.y<=0&&M<=E&&(N=!1),S.y<=0&&!(E<M)||(B-=S.y,M+=S.y);C<0&&B<0?(r=y,M-=B=-B,c-=C=-C):C<0?(r=w,c-=C=-C):B<0&&(r=x,M-=B=-B);break;case x:if(s){if(S.x<=0&&(c<=T||H<=O)){N=!1;break}L(g),C-=S.x,c+=S.x,B=C/s}else L(f),L(g),!(S.x<=0)||T<c?(C-=S.x,c+=S.x):0<=S.y&&H<=O&&(N=!1),0<=S.y&&!(O<H)||(B+=S.y);C<0&&B<0?(r=w,M-=B=-B,c-=C=-C):C<0?(r=y,c-=C=-C):B<0&&(r=b,M-=B=-B);break;case y:if(s){if(0<=S.x&&(W<=k||H<=O)){N=!1;break}L(u),B=(C+=S.x)/s}else L(f),L(u),!(0<=S.x)||k<W?C+=S.x:0<=S.y&&H<=O&&(N=!1),0<=S.y&&!(O<H)||(B+=S.y);C<0&&B<0?(r=b,M-=B=-B,c-=C=-C):C<0?(r=x,c-=C=-C):B<0&&(r=w,M-=B=-B);break;case p:this.move(S.x,S.y),N=!1;break;case m:this.zoom((Y=e({},z=h),X=0,ot(z,(function(t,e){delete Y[e],ot(Y,(function(e){var i=Math.abs(t.startX-e.startX),a=Math.abs(t.startY-e.startY),n=Math.abs(t.endX-e.endX);e=Math.abs(t.endY-e.endY),a=Math.sqrt(i*i+a*a),a=(Math.sqrt(n*n+e*e)-a)/a;Math.abs(a)>Math.abs(X)&&(X=a)}))})),X),t),N=!1;break;case l:if(!S.x||!S.y){N=!1;break}z=Bt(this.cropper),c=R.startX-z.left,M=R.startY-z.top,C=o.minWidth,B=o.minHeight,0<S.x?r=0<S.y?y:w:S.x<0&&(c-=C,r=0<S.y?x:b),S.y<0&&(M-=B),this.cropped||(pt(this.cropBox,D),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0))}N&&(o.width=C,o.height=B,o.left=c,o.top=M,this.action=r,this.renderCropBox()),ot(h,(function(t){t.startX=t.endX,t.startY=t.endY}))}}),St={crop:function(){return!this.ready||this.cropped||this.disabled||(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&lt(this.dragBox,O),pt(this.cropBox,D),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=ht({},this.initialImageData),this.canvasData=ht({},this.initialCanvasData),this.cropBoxData=ht({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(ht(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),pt(this.dragBox,O),lt(this.cropBox,D)),this},replace:function(t){var e=1<arguments.length&&void 0!==arguments[1]&&arguments[1];return!this.disabled&&t&&(this.isImg&&(this.element.src=t),e?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,ot(this.previews,(function(e){e.getElementsByTagName("img")[0].src=t})))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,pt(this.cropper,C)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,lt(this.cropper,C)),this},destroy:function(){var t=this.element;return t[c]&&(t[c]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate()),this},move:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:t,i=(a=this.canvasData).left,a=a.top;return this.moveTo(J(t)?t:i+Number(t),J(e)?e:a+Number(e))},moveTo:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:t,i=this.canvasData,a=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.movable&&(V(t)&&(i.left=t,a=!0),V(e)&&(i.top=e,a=!0),a&&this.renderCanvas(!0)),this},zoom:function(t,e){var i=this.canvasData;return t=Number(t),this.zoomTo(i.width*(t=t<0?1/(1-t):1+t)/i.naturalWidth,null,e)},zoomTo:function(t,e,i){var a,n,o,h=this.options,r=this.canvasData,s=r.width,c=r.height,d=r.naturalWidth,l=r.naturalHeight;if(0<=(t=Number(t))&&this.ready&&!this.disabled&&h.zoomable){if(h=d*t,l*=t,!1===Dt(this.element,P,{ratio:t,oldRatio:s/d,originalEvent:i}))return this;i?(t=this.pointers,d=Bt(this.cropper),i=t&&Object.keys(t).length?(o=n=a=0,ot(t,(function(t){var e=t.startX;t=t.startY;a+=e,n+=t,o+=1})),{pageX:a/=o,pageY:n/=o}):{pageX:i.pageX,pageY:i.pageY},r.left-=(h-s)*((i.pageX-d.left-r.left)/s),r.top-=(l-c)*((i.pageY-d.top-r.top)/c)):et(e)&&V(e.x)&&V(e.y)?(r.left-=(h-s)*((e.x-r.left)/s),r.top-=(l-c)*((e.y-r.top)/c)):(r.left-=(h-s)/2,r.top-=(l-c)/2),r.width=h,r.height=l,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return V(t=Number(t))&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var e=this.imageData.scaleY;return this.scale(t,V(e)?e:1)},scaleY:function(t){var e=this.imageData.scaleX;return this.scale(V(e)?e:1,t)},scale:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:t,i=this.imageData,a=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.scalable&&(V(t)&&(i.scaleX=t,a=!0),V(e)&&(i.scaleY=e,a=!0),a&&this.renderCanvas(!0,!0)),this},getData:function(){var t,e,i=0<arguments.length&&void 0!==arguments[0]&&arguments[0],a=this.options,n=this.imageData,o=this.canvasData,h=this.cropBoxData;return this.ready&&this.cropped?(t={x:h.left-o.left,y:h.top-o.top,width:h.width,height:h.height},e=n.width/n.naturalWidth,ot(t,(function(i,a){t[a]=i/e})),i&&(h=Math.round(t.y+t.height),i=Math.round(t.x+t.width),t.x=Math.round(t.x),t.y=Math.round(t.y),t.width=i-t.x,t.height=h-t.y)):t={x:0,y:0,width:0,height:0},a.rotatable&&(t.rotate=n.rotate||0),a.scalable&&(t.scaleX=n.scaleX||1,t.scaleY=n.scaleY||1),t},setData:function(t){var e,i=this.options,a=this.imageData,n=this.canvasData,o={};return this.ready&&!this.disabled&&et(t)&&(e=!1,i.rotatable&&V(t.rotate)&&t.rotate!==a.rotate&&(a.rotate=t.rotate,e=!0),i.scalable&&(V(t.scaleX)&&t.scaleX!==a.scaleX&&(a.scaleX=t.scaleX,e=!0),V(t.scaleY)&&t.scaleY!==a.scaleY&&(a.scaleY=t.scaleY,e=!0)),e&&this.renderCanvas(!0,!0),a=a.width/a.naturalWidth,V(t.x)&&(o.left=t.x*a+n.left),V(t.y)&&(o.top=t.y*a+n.top),V(t.width)&&(o.width=t.width*a),V(t.height)&&(o.height=t.height*a),this.setCropBoxData(o)),this},getContainerData:function(){return this.ready?ht({},this.containerData):{}},getImageData:function(){return this.sized?ht({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,e={};return this.ready&&ot(["left","top","width","height","naturalWidth","naturalHeight"],(function(i){e[i]=t[i]})),e},setCanvasData:function(t){var e=this.canvasData,i=e.aspectRatio;return this.ready&&!this.disabled&&et(t)&&(V(t.left)&&(e.left=t.left),V(t.top)&&(e.top=t.top),V(t.width)?(e.width=t.width,e.height=t.width/i):V(t.height)&&(e.height=t.height,e.width=t.height*i),this.renderCanvas(!0)),this},getCropBoxData:function(){var t,e=this.cropBoxData;return(t=this.ready&&this.cropped?{left:e.left,top:e.top,width:e.width,height:e.height}:t)||{}},setCropBoxData:function(t){var e,i,a=this.cropBoxData,n=this.options.aspectRatio;return this.ready&&this.cropped&&!this.disabled&&et(t)&&(V(t.left)&&(a.left=t.left),V(t.top)&&(a.top=t.top),V(t.width)&&t.width!==a.width&&(e=!0,a.width=t.width),V(t.height)&&t.height!==a.height&&(i=!0,a.height=t.height),n&&(e?a.height=a.width/n:i&&(a.width=a.height*n)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var e,i,a,o,h,r,s,c,d,l,p,m,u=this.canvasData,g=(w=this.image,i=u,x=t,a=(e=this.imageData).aspectRatio,o=e.naturalWidth,b=e.naturalHeight,h=void 0===(m=e.rotate)?0:m,g=void 0===(v=e.scaleX)?1:v,y=void 0===(l=e.scaleY)?1:l,r=i.aspectRatio,s=i.naturalWidth,c=i.naturalHeight,d=void 0===(p=x.fillColor)?"transparent":p,f=void 0===(M=x.imageSmoothingEnabled)||M,v=void 0===(m=x.imageSmoothingQuality)?"low":m,l=void 0===(e=x.maxWidth)?1/0:e,p=void 0===(i=x.maxHeight)?1/0:i,m=void 0===(M=x.minWidth)?0:M,i=void 0===(e=x.minHeight)?0:e,x=(M=document.createElement("canvas")).getContext("2d"),e=Nt({aspectRatio:r,width:l,height:p}),r=Nt({aspectRatio:r,width:m,height:i},"cover"),s=Math.min(e.width,Math.max(r.width,s)),c=Math.min(e.height,Math.max(r.height,c)),p=Nt({aspectRatio:a,width:l,height:p}),i=Nt({aspectRatio:a,width:m,height:i},"cover"),b=[-(o=Math.min(p.width,Math.max(i.width,o)))/2,-(b=Math.min(p.height,Math.max(i.height,b)))/2,o,b],M.width=st(s),M.height=st(c),x.fillStyle=d,x.fillRect(0,0,s,c),x.save(),x.translate(s/2,c/2),x.rotate(h*Math.PI/180),x.scale(g,y),x.imageSmoothingEnabled=f,x.imageSmoothingQuality=v,x.drawImage.apply(x,[w].concat(n(b.map((function(t){return Math.floor(st(t))}))))),x.restore(),M);if(!this.cropped)return g;var f=(y=this.getData()).x,v=y.y,w=y.width,b=y.height;1!=(x=g.width/Math.floor(u.naturalWidth))&&(f*=x,v*=x,w*=x,b*=x);var y=Nt({aspectRatio:M=w/b,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),x=(u=Nt({aspectRatio:M,width:t.minWidth||0,height:t.minHeight||0},"cover"),(M=Nt({aspectRatio:M,width:t.width||(1!=x?g.width:w),height:t.height||(1!=x?g.height:b)})).width),M=M.height;x=Math.min(y.width,Math.max(u.width,x)),M=Math.min(y.height,Math.max(u.height,M)),u=(y=document.createElement("canvas")).getContext("2d");y.width=st(x),y.height=st(M),u.fillStyle=t.fillColor||"transparent",u.fillRect(0,0,x,M),M=t.imageSmoothingEnabled,t=t.imageSmoothingQuality,u.imageSmoothingEnabled=void 0===M||M,t&&(u.imageSmoothingQuality=t);var C,D,B,k,O;M=g.width,t=g.height;(f=f)<=-w||M<f?k=D=C=f=0:f<=0?(D=-f,f=0,k=C=Math.min(M,w+f)):f<=M&&(D=0,k=C=Math.min(w,M-f)),C<=0||v<=-b||t<v?O=B=T=v=0:v<=0?(B=-v,v=0,O=T=Math.min(t,b+v)):v<=t&&(B=0,O=T=Math.min(b,t-v));var T=[f,v,C,T];return 0<k&&0<O&&T.push(D*(w=x/w),B*w,k*w,O*w),u.drawImage.apply(u,[g].concat(n(T.map((function(t){return Math.floor(st(t))}))))),y},setAspectRatio:function(t){var e=this.options;return this.disabled||J(t)||(e.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var e,i,a=this.options,n=this.dragBox,o=this.face;return this.ready&&!this.disabled&&(i=a.movable&&t===N,a.dragMode=t=(e=t===H)||i?t:L,vt(n,E,t),mt(n,M,e),mt(n,T,i),a.cropBoxMovable||(vt(o,E,t),mt(o,M,e),mt(o,T,i))),this}},At=h.Cropper,jt=function(){function t(e){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),!e||!K.test(e.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=e,this.options=ht({},Z,et(i)&&i),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}var e,i,n;return e=t,n=[{key:"noConflict",value:function(){return window.Cropper=At,t}},{key:"setDefaults",value:function(t){ht(Z,et(t)&&t)}}],(i=[{key:"init",value:function(){var t,e=this.element,i=e.tagName.toLowerCase();if(!e[c]){if(e[c]=this,"img"===i){if(this.isImg=!0,t=e.getAttribute("src")||"",!(this.originalUrl=t))return;t=e.src}else"canvas"===i&&window.HTMLCanvasElement&&(t=e.toDataURL());this.load(t)}}},{key:"load",value:function(t){var e,i,a,n,o,h,r=this;t&&(this.url=t,this.imageData={},e=this.element,(i=this.options).rotatable||i.scalable||(i.checkOrientation=!1),i.checkOrientation&&window.ArrayBuffer?$.test(t)?Q.test(t)?this.read((h=(h=t).replace(zt,""),a=atob(h),h=new ArrayBuffer(a.length),ot(n=new Uint8Array(h),(function(t,e){n[e]=a.charCodeAt(e)})),h)):this.clone():(o=new XMLHttpRequest,h=this.clone.bind(this),this.reloading=!0,(this.xhr=o).onabort=h,o.onerror=h,o.ontimeout=h,o.onprogress=function(){o.getResponseHeader("content-type")!==U&&o.abort()},o.onload=function(){r.read(o.response)},o.onloadend=function(){r.reloading=!1,r.xhr=null},i.checkCrossOrigin&&Tt(t)&&e.crossOrigin&&(t=Et(t)),o.open("GET",t,!0),o.responseType="arraybuffer",o.withCredentials="use-credentials"===e.crossOrigin,o.send()):this.clone())}},{key:"read",value:function(t){var e=this.options,i=this.imageData,a=function(t){var e,i,a,n,o,h,r,s=new DataView(t);try{if(255===s.getUint8(0)&&216===s.getUint8(1))for(var c=s.byteLength,d=2;d+1<c;){if(255===s.getUint8(d)&&225===s.getUint8(d+1)){i=d;break}d+=1}if(i&&(n=i+10,"Exif"===function(t,e,i){var a="";i+=e;for(var n=e;n<i;n+=1)a+=Lt(t.getUint8(n));return a}(s,i+4,4)&&(!(r=18761===(o=s.getUint16(n)))&&19789!==o||42!==s.getUint16(n+2,r)||8<=(h=s.getUint32(n+4,r))&&(a=n+h))),a)for(var l,p=s.getUint16(a,r),m=0;m<p;m+=1)if(l=a+12*m+2,274===s.getUint16(l,r)){l+=8,e=s.getUint16(l,r),s.setUint16(l,1,r);break}}catch(t){e=1}return e}(t),n=0,o=1,h=1;1<a&&(this.url=function(t,e){for(var i=[],a=new Uint8Array(t);0<a.length;)i.push(Lt.apply(null,nt(a.subarray(0,8192)))),a=a.subarray(8192);return"data:".concat("image/jpeg",";base64,").concat(btoa(i.join("")))}(t),n=(a=function(t){var e=0,i=1,a=1;switch(t){case 2:i=-1;break;case 3:e=-180;break;case 4:a=-1;break;case 5:e=90,a=-1;break;case 6:e=90;break;case 7:e=90,i=-1;break;case 8:e=-90}return{rotate:e,scaleX:i,scaleY:a}}(a)).rotate,o=a.scaleX,h=a.scaleY),e.rotatable&&(i.rotate=n),e.scalable&&(i.scaleX=o,i.scaleY=h),this.clone()}},{key:"clone",value:function(){var t=this.element,e=this.url,i=t.crossOrigin,a=e;this.options.checkCrossOrigin&&Tt(e)&&(i=i||"anonymous",a=Et(e)),this.crossOrigin=i,this.crossOriginUrl=a;var n=document.createElement("img");i&&(n.crossOrigin=i),n.src=a||e,n.alt=t.alt||"The image to crop",(this.image=n).onload=this.start.bind(this),n.onerror=this.stop.bind(this),lt(n,B),t.parentNode.insertBefore(n,t.nextSibling)}},{key:"start",value:function(){var t=this,e=this.image;function i(e,i){ht(t.imageData,{naturalWidth:e,naturalHeight:i,aspectRatio:e/i}),t.initialImageData=ht({},t.imageData),t.sizing=!1,t.sized=!0,t.build()}e.onload=null,e.onerror=null,this.sizing=!0;var a,n,o=h.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(h.navigator.userAgent);!e.naturalWidth||o?(a=document.createElement("img"),n=document.body||document.documentElement,(this.sizingImage=a).onload=function(){i(a.width,a.height),o||n.removeChild(a)},a.src=e.src,o||(a.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",n.appendChild(a))):i(e.naturalWidth,e.naturalHeight)}},{key:"stop",value:function(){var t=this.image;t.onload=null,t.onerror=null,t.parentNode.removeChild(t),this.image=null}},{key:"build",value:function(){var t,e,i,a,n,o,h,r,s;this.sized&&!this.ready&&(t=this.element,e=this.options,i=this.image,a=t.parentNode,(s=document.createElement("div")).innerHTML='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>',o=(n=s.querySelector(".".concat(c,"-container"))).querySelector(".".concat(c,"-canvas")),h=n.querySelector(".".concat(c,"-drag-box")),s=(r=n.querySelector(".".concat(c,"-crop-box"))).querySelector(".".concat(c,"-face")),this.container=a,this.cropper=n,this.canvas=o,this.dragBox=h,this.cropBox=r,this.viewBox=n.querySelector(".".concat(c,"-view-box")),this.face=s,o.appendChild(i),lt(t,D),a.insertBefore(n,t.nextSibling),this.isImg||pt(i,B),this.initPreview(),this.bind(),e.initialAspectRatio=Math.max(0,e.initialAspectRatio)||NaN,e.aspectRatio=Math.max(0,e.aspectRatio)||NaN,e.viewMode=Math.max(0,Math.min(3,Math.round(e.viewMode)))||0,lt(r,D),e.guides||lt(r.getElementsByClassName("".concat(c,"-dashed")),D),e.center||lt(r.getElementsByClassName("".concat(c,"-center")),D),e.background&&lt(n,"".concat(c,"-bg")),e.highlight||lt(s,k),e.cropBoxMovable&&(lt(s,T),vt(s,E,d)),e.cropBoxResizable||(lt(r.getElementsByClassName("".concat(c,"-line")),D),lt(r.getElementsByClassName("".concat(c,"-point")),D)),this.render(),this.ready=!0,this.setDragMode(e.dragMode),e.autoCrop&&this.crop(),this.setData(e.data),it(e.ready)&&Ct(t,"ready",e.ready,{once:!0}),Dt(t,"ready"))}},{key:"unbuild",value:function(){this.ready&&(this.ready=!1,this.unbind(),this.resetPreview(),this.cropper.parentNode.removeChild(this.cropper),pt(this.element,D))}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}])&&a(e.prototype,i),n&&a(e,n),t}();return ht(jt.prototype,Yt,Xt,s,r,Rt,St),jt}));
