{"version": 3, "file": "lang/summernote-mn-MN.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA;AAEA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,KADF;AAEJC,QAAAA,MAAM,EAAE,OAFJ;AAGJC,QAAAA,SAAS,EAAE,gBAHP;AAIJC,QAAAA,KAAK,EAAE,UAJH;AAKJC,QAAAA,MAAM,EAAE,OALJ;AAMJC,QAAAA,IAAI,EAAE,MANF;AAOJC,QAAAA,WAAW,EAAE,cAPT;AAQJC,QAAAA,SAAS,EAAE,cARP;AASJC,QAAAA,aAAa,EAAE,OATX;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,MAAM,EAAE,SAFH;AAGLC,QAAAA,UAAU,EAAE,cAHP;AAILC,QAAAA,UAAU,EAAE,YAJP;AAKLC,QAAAA,aAAa,EAAE,YALV;AAMLC,QAAAA,SAAS,EAAE,sBANN;AAOLC,QAAAA,UAAU,EAAE,wBAPP;AAQLC,QAAAA,SAAS,EAAE,0BARN;AASLC,QAAAA,YAAY,EAAE,cATT;AAULC,QAAAA,WAAW,EAAE,eAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,UAZN;AAaLC,QAAAA,aAAa,EAAE,6BAbV;AAcLC,QAAAA,SAAS,EAAE,oBAdN;AAeLC,QAAAA,eAAe,EAAE,uBAfZ;AAgBLC,QAAAA,eAAe,EAAE,oBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,6BAjBjB;AAkBLC,QAAAA,GAAG,EAAE,aAlBA;AAmBLC,QAAAA,MAAM,EAAE,gBAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,eAFN;AAGLpB,QAAAA,MAAM,EAAE,eAHH;AAILgB,QAAAA,GAAG,EAAE,YAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,SADF;AAEJtB,QAAAA,MAAM,EAAE,iBAFJ;AAGJuB,QAAAA,MAAM,EAAE,iBAHJ;AAIJC,QAAAA,IAAI,EAAE,WAJF;AAKJC,QAAAA,aAAa,EAAE,iBALX;AAMJT,QAAAA,GAAG,EAAE,6BAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,WAAW,EAAE,eAFR;AAGLC,QAAAA,WAAW,EAAE,eAHR;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,kBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,eAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,UADF;AAELC,QAAAA,CAAC,EAAE,GAFE;AAGLC,QAAAA,UAAU,EAAE,UAHP;AAILC,QAAAA,GAAG,EAAE,YAJA;AAKLC,QAAAA,EAAE,EAAE,UALC;AAMLC,QAAAA,EAAE,EAAE,UANC;AAOLC,QAAAA,EAAE,EAAE,UAPC;AAQLC,QAAAA,EAAE,EAAE,UARC;AASLC,QAAAA,EAAE,EAAE,UATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,iBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,SADC;AAEPC,QAAAA,UAAU,EAAE,mBAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,OADF;AAETC,QAAAA,OAAO,EAAE,iBAFA;AAGTC,QAAAA,MAAM,EAAE,iBAHC;AAITC,QAAAA,IAAI,EAAE,oBAJG;AAKTC,QAAAA,MAAM,EAAE,eALC;AAMTC,QAAAA,KAAK,EAAE,sBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,uBADH;AAELC,QAAAA,IAAI,EAAE,cAFD;AAGLC,QAAAA,UAAU,EAAE,cAHP;AAILC,QAAAA,UAAU,EAAE,aAJP;AAKLC,QAAAA,WAAW,EAAE,UALR;AAMLC,QAAAA,cAAc,EAAE,iBANX;AAOLC,QAAAA,KAAK,EAAE,2BAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,gBADH;AAERC,QAAAA,KAAK,EAAE,OAFC;AAGRC,QAAAA,cAAc,EAAE,wBAHR;AAIRC,QAAAA,MAAM,EAAE,QAJA;AAKRC,QAAAA,mBAAmB,EAAE,2BALb;AAMRC,QAAAA,aAAa,EAAE,2BANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,kBADf;AAEJ,gBAAQ,yBAFJ;AAGJ,gBAAQ,yBAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,kBANJ;AAOJ,kBAAU,oBAPN;AAQJ,qBAAa,uBART;AASJ,yBAAiB,2BATb;AAUJ,wBAAgB,eAVZ;AAWJ,uBAAe,gBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,iBAbZ;AAcJ,uBAAe,gBAdX;AAeJ,+BAAuB,uBAfnB;AAgBJ,6BAAqB,qBAhBjB;AAiBJ,mBAAW,8BAjBP;AAkBJ,kBAAU,6BAlBN;AAmBJ,sBAAc,sDAnBV;AAoBJ,oBAAY,sCApBR;AAqBJ,oBAAY,sCArBR;AAsBJ,oBAAY,sCAtBR;AAuBJ,oBAAY,sCAvBR;AAwBJ,oBAAY,sCAxBR;AAyBJ,oBAAY,sCAzBR;AA0BJ,gCAAwB,wBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,QADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,gBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-mn-MN.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "// Starsoft Mongolia LLC Temuujin Ariunbold\n\n(function($) {\n  $.extend($.summernote.lang, {\n    'mn-MN': {\n      font: {\n        bold: 'Тод',\n        italic: 'Налуу',\n        underline: 'Доогуур зураас',\n        clear: 'Цэвэрлэх',\n        height: 'Өндөр',\n        name: 'Фонт',\n        superscript: 'Дээд илтгэгч',\n        subscript: 'Доод илтгэгч',\n        strikethrough: 'Дарах',\n        size: 'Хэмжээ',\n      },\n      image: {\n        image: 'Зураг',\n        insert: 'Оруулах',\n        resizeFull: 'Хэмжээ бүтэн',\n        resizeHalf: 'Хэмжээ 1/2',\n        resizeQuarter: 'Хэмжээ 1/4',\n        floatLeft: 'Зүүн талд байрлуулах',\n        floatRight: 'Баруун талд байрлуулах',\n        floatNone: 'Анхдагч байрлалд аваачих',\n        shapeRounded: 'Хүрээ: Дугуй',\n        shapeCircle: 'Хүрээ: Тойрог',\n        shapeThumbnail: 'Хүрээ: Хураангуй',\n        shapeNone: 'Хүрээгүй',\n        dragImageHere: 'Зургийг энд чирч авчирна уу',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Файлуудаас сонгоно уу',\n        maximumFileSize: 'Файлын дээд хэмжээ',\n        maximumFileSizeError: 'Файлын дээд хэмжээ хэтэрсэн',\n        url: 'Зургийн URL',\n        remove: 'Зургийг устгах',\n        original: 'Original',\n      },\n      video: {\n        video: 'Видео',\n        videoLink: 'Видео холбоос',\n        insert: 'Видео оруулах',\n        url: 'Видео URL?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion болон Youku)',\n      },\n      link: {\n        link: 'Холбоос',\n        insert: 'Холбоос оруулах',\n        unlink: 'Холбоос арилгах',\n        edit: 'Засварлах',\n        textToDisplay: 'Харуулах бичвэр',\n        url: 'Энэ холбоос хаашаа очих вэ?',\n        openInNewWindow: 'Шинэ цонхонд нээх',\n      },\n      table: {\n        table: 'Хүснэгт',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Хэвтээ шугам оруулах',\n      },\n      style: {\n        style: 'Хэв маяг',\n        p: 'p',\n        blockquote: 'Иш татах',\n        pre: 'Эх сурвалж',\n        h1: 'Гарчиг 1',\n        h2: 'Гарчиг 2',\n        h3: 'Гарчиг 3',\n        h4: 'Гарчиг 4',\n        h5: 'Гарчиг 5',\n        h6: 'Гарчиг 6',\n      },\n      lists: {\n        unordered: 'Эрэмбэлэгдээгүй',\n        ordered: 'Эрэмбэлэгдсэн',\n      },\n      options: {\n        help: 'Тусламж',\n        fullscreen: 'Дэлгэцийг дүүргэх',\n        codeview: 'HTML-Code харуулах',\n      },\n      paragraph: {\n        paragraph: 'Хэсэг',\n        outdent: 'Догол мөр хасах',\n        indent: 'Догол мөр нэмэх',\n        left: 'Зүүн тийш эгнүүлэх',\n        center: 'Төвд эгнүүлэх',\n        right: 'Баруун тийш эгнүүлэх',\n        justify: 'Мөрийг тэгшлэх',\n      },\n      color: {\n        recent: 'Сүүлд хэрэглэсэн өнгө',\n        more: 'Өөр өнгөнүүд',\n        background: 'Дэвсгэр өнгө',\n        foreground: 'Үсгийн өнгө',\n        transparent: 'Тунгалаг',\n        setTransparent: 'Тунгалаг болгох',\n        reset: 'Анхдагч өнгөөр тохируулах',\n        resetToDefault: 'Хэвд нь оруулах',\n      },\n      shortcut: {\n        shortcuts: 'Богино холбоос',\n        close: 'Хаалт',\n        textFormatting: 'Бичвэрийг хэлбэржүүлэх',\n        action: 'Үйлдэл',\n        paragraphFormatting: 'Догол мөрийг хэлбэржүүлэх',\n        documentStyle: 'Бичиг баримтын хэв загвар',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Буцаах',\n        redo: 'Дахин хийх',\n      },\n      specialChar: {\n        specialChar: 'Тусгай тэмдэгт',\n        select: 'Тусгай тэмдэгт сонгох',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "superscript", "subscript", "strikethrough", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}