{"version": 3, "file": "lang/summernote-nb-NO.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,KADF;AAEJC,QAAAA,MAAM,EAAE,QAFJ;AAGJC,QAAAA,SAAS,EAAE,YAHP;AAIJC,QAAAA,KAAK,EAAE,mBAJH;AAKJC,QAAAA,MAAM,EAAE,YALJ;AAMJC,QAAAA,IAAI,EAAE,YANF;AAOJC,QAAAA,aAAa,EAAE,cAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,aATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,MAAM,EAAE,gBAFH;AAGLC,QAAAA,UAAU,EAAE,qBAHP;AAILC,QAAAA,UAAU,EAAE,qBAJP;AAKLC,QAAAA,aAAa,EAAE,sBALV;AAMLC,QAAAA,SAAS,EAAE,kBANN;AAOLC,QAAAA,UAAU,EAAE,gBAPP;AAQLC,QAAAA,SAAS,EAAE,YARN;AASLC,QAAAA,YAAY,EAAE,cATT;AAULC,QAAAA,WAAW,EAAE,cAVR;AAWLC,QAAAA,cAAc,EAAE,gBAXX;AAYLC,QAAAA,SAAS,EAAE,aAZN;AAaLC,QAAAA,aAAa,EAAE,kBAbV;AAcLC,QAAAA,SAAS,EAAE,oBAdN;AAeLC,QAAAA,eAAe,EAAE,gBAfZ;AAgBLC,QAAAA,eAAe,EAAE,kBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,gCAjBjB;AAkBLC,QAAAA,GAAG,EAAE,WAlBA;AAmBLC,QAAAA,MAAM,EAAE,aAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,YAFN;AAGLpB,QAAAA,MAAM,EAAE,gBAHH;AAILgB,QAAAA,GAAG,EAAE,WAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,OADF;AAEJtB,QAAAA,MAAM,EAAE,gBAFJ;AAGJuB,QAAAA,MAAM,EAAE,aAHJ;AAIJC,QAAAA,IAAI,EAAE,SAJF;AAKJC,QAAAA,aAAa,EAAE,eALX;AAMJT,QAAAA,GAAG,EAAE,yCAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,WAAW,EAAE,mBAFR;AAGLC,QAAAA,WAAW,EAAE,oBAHR;AAILC,QAAAA,UAAU,EAAE,kCAJP;AAKLC,QAAAA,WAAW,EAAE,gCALR;AAMLC,QAAAA,MAAM,EAAE,WANH;AAOLC,QAAAA,MAAM,EAAE,eAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,CAAC,EAAE,UAFE;AAGLC,QAAAA,UAAU,EAAE,OAHP;AAILC,QAAAA,GAAG,EAAE,MAJA;AAKLC,QAAAA,EAAE,EAAE,cALC;AAMLC,QAAAA,EAAE,EAAE,cANC;AAOLC,QAAAA,EAAE,EAAE,cAPC;AAQLC,QAAAA,EAAE,EAAE,cARC;AASLC,QAAAA,EAAE,EAAE,cATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,YADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,UAAU,EAAE,YAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,SADF;AAETC,QAAAA,OAAO,EAAE,aAFA;AAGTC,QAAAA,MAAM,EAAE,SAHC;AAITC,QAAAA,IAAI,EAAE,gBAJG;AAKTC,QAAAA,MAAM,EAAE,WALC;AAMTC,QAAAA,KAAK,EAAE,cANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,mBADH;AAELC,QAAAA,IAAI,EAAE,cAFD;AAGLC,QAAAA,UAAU,EAAE,gBAHP;AAILC,QAAAA,UAAU,EAAE,aAJP;AAKLC,QAAAA,WAAW,EAAE,eALR;AAMLC,QAAAA,cAAc,EAAE,oBANX;AAOLC,QAAAA,KAAK,EAAE,WAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,cADH;AAERC,QAAAA,KAAK,EAAE,MAFC;AAGRC,QAAAA,cAAc,EAAE,kBAHR;AAIRC,QAAAA,MAAM,EAAE,UAJA;AAKRC,QAAAA,mBAAmB,EAAE,qBALb;AAMRC,QAAAA,aAAa,EAAE;AANP,OAxGH;AAgHPzB,MAAAA,IAAI,EAAE;AACJ,2BAAmB,kBADf;AAEJ,gBAAQ,sBAFJ;AAGJ,gBAAQ,wBAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,kBANJ;AAOJ,kBAAU,qBAPN;AAQJ,qBAAa,2BART;AASJ,yBAAiB,2BATb;AAUJ,wBAAgB,kBAVZ;AAWJ,uBAAe,uBAXX;AAYJ,yBAAiB,yBAZb;AAaJ,wBAAgB,sBAbZ;AAcJ,uBAAe,qBAdX;AAeJ,+BAAuB,oBAfnB;AAgBJ,6BAAqB,oBAhBjB;AAiBJ,mBAAW,yBAjBP;AAkBJ,kBAAU,0BAlBN;AAmBJ,sBAAc,qDAnBV;AAoBJ,oBAAY,oCApBR;AAqBJ,oBAAY,oCArBR;AAsBJ,oBAAY,oCAtBR;AAuBJ,oBAAY,oCAvBR;AAwBJ,oBAAY,oCAxBR;AAyBJ,oBAAY,oCAzBR;AA0BJ,gCAAwB,2BA1BpB;AA2BJ,2BAAmB;AA3Bf,OAhHC;AA6IP0B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA7IF;AAiJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,gBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAjJN;AADiB,GAA5B;AAwJD,CAzJD,EAyJGC,MAzJH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-nb-NO.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'nb-NO': {\n      font: {\n        bold: 'Fet',\n        italic: 'Kursiv',\n        underline: 'Understrek',\n        clear: 'Fjern formatering',\n        height: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n        name: 'Skrifttype',\n        strikethrough: 'Gje<PERSON>omstrek',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Skriftstørrelse',\n      },\n      image: {\n        image: 'Bilde',\n        insert: 'Sett inn bilde',\n        resizeFull: 'Sett full størrelse',\n        resizeHalf: 'Sett halv størrelse',\n        resizeQuarter: 'Sett kvart størrelse',\n        floatLeft: 'Flyt til venstre',\n        floatRight: 'Flyt til høyre',\n        floatNone: 'Fjern flyt',\n        shapeRounded: 'Form: Rundet',\n        shapeCircle: 'Form: Sirkel',\n        shapeThumbnail: 'Form: Miniatyr',\n        shapeNone: 'Form: Ingen',\n        dragImageHere: 'Dra et bilde hit',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Velg fra filer',\n        maximumFileSize: 'Max filstørrelse',\n        maximumFileSizeError: 'Maks filstørrelse overskredet.',\n        url: 'Bilde-URL',\n        remove: 'Fjern bilde',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Videolenke',\n        insert: 'Sett inn video',\n        url: 'Video-URL',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion eller Youku)',\n      },\n      link: {\n        link: 'Lenke',\n        insert: 'Sett inn lenke',\n        unlink: 'Fjern lenke',\n        edit: 'Rediger',\n        textToDisplay: 'Visningstekst',\n        url: 'Til hvilken URL skal denne lenken peke?',\n        openInNewWindow: 'Åpne i nytt vindu',\n      },\n      table: {\n        table: 'Tabell',\n        addRowAbove: 'Legg til rad over',\n        addRowBelow: 'Legg til rad under',\n        addColLeft: 'Legg til kolonne på venstre side',\n        addColRight: 'Legg til kolonne på høyre side',\n        delRow: 'Slett rad',\n        delCol: 'Slett kolonne',\n        delTable: 'Slett tabell',\n      },\n      hr: {\n        insert: 'Sett inn horisontal linje',\n      },\n      style: {\n        style: 'Stil',\n        p: 'Paragraf',\n        blockquote: 'Sitat',\n        pre: 'Kode',\n        h1: 'Overskrift 1',\n        h2: 'Overskrift 2',\n        h3: 'Overskrift 3',\n        h4: 'Overskrift 4',\n        h5: 'Overskrift 5',\n        h6: 'Overskrift 6',\n      },\n      lists: {\n        unordered: 'Punktliste',\n        ordered: 'Nummerert liste',\n      },\n      options: {\n        help: 'Hjelp',\n        fullscreen: 'Fullskjerm',\n        codeview: 'HTML-visning',\n      },\n      paragraph: {\n        paragraph: 'Avsnitt',\n        outdent: 'Tilbakerykk',\n        indent: 'Innrykk',\n        left: 'Venstrejustert',\n        center: 'Midtstilt',\n        right: 'Høyrejustert',\n        justify: 'Blokkjustert',\n      },\n      color: {\n        recent: 'Nylig valgt farge',\n        more: 'Flere farger',\n        background: 'Bakgrunnsfarge',\n        foreground: 'Skriftfarge',\n        transparent: 'Gjennomsiktig',\n        setTransparent: 'Sett gjennomsiktig',\n        reset: 'Nullstill',\n        resetToDefault: 'Nullstill til standard',\n      },\n      shortcut: {\n        shortcuts: 'Hurtigtaster',\n        close: 'Lukk',\n        textFormatting: 'Tekstformatering',\n        action: 'Handling',\n        paragraphFormatting: 'Avsnittsformatering',\n        documentStyle: 'Dokumentstil',\n      },\n      help: {\n        'insertParagraph': 'Sett inn avsnitt',\n        'undo': 'Angre siste handling',\n        'redo': 'Gjør om siste handling',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Angi en fet stil',\n        'italic': 'Angi en kursiv stil',\n        'underline': 'Sett en understreket stil',\n        'strikethrough': 'Sett en gjennomgående sti',\n        'removeFormat': 'Tøm formattering',\n        'justifyLeft': 'Angi venstrejustering',\n        'justifyCenter': 'Angi sentrert justering',\n        'justifyRight': 'Angi høyre justering',\n        'justifyFull': 'Angi full justering',\n        'insertUnorderedList': 'Bytt uordnet liste',\n        'insertOrderedList': 'Bytt sortert liste',\n        'outdent': 'Utrykk på valgt avsnitt',\n        'indent': 'Innrykk på valgt avsnitt',\n        'formatPara': 'Endre gjeldende blokkformat til et avsnitt (P-kode)',\n        'formatH1': 'Endre gjeldende blokkformat til H1',\n        'formatH2': 'Endre gjeldende blokkformat til H2',\n        'formatH3': 'Endre gjeldende blokkformat til H3',\n        'formatH4': 'Endre gjeldende blokkformat til H4',\n        'formatH5': 'Endre gjeldende blokkformat til H5',\n        'formatH6': 'Endre gjeldende blokkformat til H6',\n        'insertHorizontalRule': 'Sett inn horisontal deler',\n        'linkDialog.show': 'Vis koblingsdialog',\n      },\n      history: {\n        undo: 'Angre',\n        redo: 'Gjør om',\n      },\n      specialChar: {\n        specialChar: 'SPESIELLE TEGN',\n        select: 'Velg spesielle tegn',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}