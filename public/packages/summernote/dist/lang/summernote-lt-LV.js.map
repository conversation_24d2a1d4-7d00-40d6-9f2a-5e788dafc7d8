{"version": 3, "file": "lang/summernote-lt-LV.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,aADF;AAEJC,QAAAA,MAAM,EAAE,SAFJ;AAGJC,QAAAA,SAAS,EAAE,YAHP;AAIJC,QAAAA,KAAK,EAAE,oBAJH;AAKJC,QAAAA,MAAM,EAAE,kBALJ;AAMJC,QAAAA,IAAI,EAAE,OANF;AAOJC,QAAAA,aAAa,EAAE,YAPX;AAQJC,QAAAA,WAAW,EAAE,YART;AASJC,QAAAA,SAAS,EAAE,aATP;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,MAAM,EAAE,iBAFH;AAGLC,QAAAA,UAAU,EAAE,eAHP;AAILC,QAAAA,UAAU,EAAE,eAJP;AAKLC,QAAAA,aAAa,EAAE,eALV;AAMLC,QAAAA,SAAS,EAAE,oBANN;AAOLC,QAAAA,UAAU,EAAE,kBAPP;AAQLC,QAAAA,SAAS,EAAE,YARN;AASLC,QAAAA,YAAY,EAAE,qBATT;AAULC,QAAAA,WAAW,EAAE,cAVR;AAWLC,QAAAA,cAAc,EAAE,gBAXX;AAYLC,QAAAA,SAAS,EAAE,iBAZN;AAaLC,QAAAA,aAAa,EAAE,uBAbV;AAcLC,QAAAA,SAAS,EAAE,oBAdN;AAeLC,QAAAA,eAAe,EAAE,kBAfZ;AAgBLC,QAAAA,eAAe,EAAE,0BAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,2BAjBjB;AAkBLC,QAAAA,GAAG,EAAE,YAlBA;AAmBLC,QAAAA,MAAM,EAAE,cAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,YAFN;AAGLpB,QAAAA,MAAM,EAAE,cAHH;AAILgB,QAAAA,GAAG,EAAE,YAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,OADF;AAEJtB,QAAAA,MAAM,EAAE,gBAFJ;AAGJuB,QAAAA,MAAM,EAAE,cAHJ;AAIJC,QAAAA,IAAI,EAAE,SAJF;AAKJC,QAAAA,aAAa,EAAE,eALX;AAMJT,QAAAA,GAAG,EAAE,gCAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,WAAW,EAAE,eAFR;AAGLC,QAAAA,WAAW,EAAE,eAHR;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,kBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,eAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,CAAC,EAAE,SAFE;AAGLC,QAAAA,UAAU,EAAE,QAHP;AAILC,QAAAA,GAAG,EAAE,MAJA;AAKLC,QAAAA,EAAE,EAAE,eALC;AAMLC,QAAAA,EAAE,EAAE,eANC;AAOLC,QAAAA,EAAE,EAAE,eAPC;AAQLC,QAAAA,EAAE,EAAE,eARC;AASLC,QAAAA,EAAE,EAAE,eATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,qBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,WADC;AAEPC,QAAAA,UAAU,EAAE,gBAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,WADF;AAETC,QAAAA,OAAO,EAAE,kBAFA;AAGTC,QAAAA,MAAM,EAAE,mBAHC;AAITC,QAAAA,IAAI,EAAE,oBAJG;AAKTC,QAAAA,MAAM,EAAE,SALC;AAMTC,QAAAA,KAAK,EAAE,kBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,kBADH;AAELC,QAAAA,IAAI,EAAE,cAFD;AAGLC,QAAAA,UAAU,EAAE,YAHP;AAILC,QAAAA,UAAU,EAAE,aAJP;AAKLC,QAAAA,WAAW,EAAE,aALR;AAMLC,QAAAA,cAAc,EAAE,wBANX;AAOLC,QAAAA,KAAK,EAAE,UAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,SADH;AAERC,QAAAA,KAAK,EAAE,SAFC;AAGRC,QAAAA,cAAc,EAAE,oBAHR;AAIRC,QAAAA,MAAM,EAAE,SAJA;AAKRC,QAAAA,mBAAmB,EAAE,uBALb;AAMRC,QAAAA,aAAa,EAAE,iBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ2B,QAAAA,eAAe,EAAE,oBADb;AAEJC,QAAAA,IAAI,EAAE,4BAFF;AAGJC,QAAAA,IAAI,EAAE,0BAHF;AAIJC,QAAAA,GAAG,EAAE,QAJD;AAKJC,QAAAA,KAAK,EAAE,kBALH;AAMJ5F,QAAAA,IAAI,EAAE,6BANF;AAOJC,QAAAA,MAAM,EAAE,sCAPJ;AAQJC,QAAAA,SAAS,EAAE,kBARP;AASJI,QAAAA,aAAa,EAAE,kBATX;AAUJuF,QAAAA,YAAY,EAAE,yBAVV;AAWJC,QAAAA,WAAW,EAAE,2BAXT;AAYJC,QAAAA,aAAa,EAAE,gBAZX;AAaJC,QAAAA,YAAY,EAAE,yBAbV;AAcJC,QAAAA,WAAW,EAAE,kCAdT;AAeJC,QAAAA,mBAAmB,EAAE,8BAfjB;AAgBJC,QAAAA,iBAAiB,EAAE,4BAhBf;AAiBJlC,QAAAA,OAAO,EAAE,oCAjBL;AAkBJC,QAAAA,MAAM,EAAE,0BAlBJ;AAmBJkC,QAAAA,UAAU,EAAE,oCAnBR;AAoBJC,QAAAA,QAAQ,EAAE,oCApBN;AAqBJC,QAAAA,QAAQ,EAAE,oCArBN;AAsBJC,QAAAA,QAAQ,EAAE,oCAtBN;AAuBJC,QAAAA,QAAQ,EAAE,oCAvBN;AAwBJC,QAAAA,QAAQ,EAAE,oCAxBN;AAyBJC,QAAAA,QAAQ,EAAE,oCAzBN;AA0BJC,QAAAA,oBAAoB,EAAE,6BA1BlB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IPC,MAAAA,OAAO,EAAE;AACPnB,QAAAA,IAAI,EAAE,gBADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPmB,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-lt-LV.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'lv-LV': {\n      font: {\n        bold: 'Treknraksts',\n        italic: 'Kursīvs',\n        underline: 'Pasvītrots',\n        clear: 'Noņemt formatējumu',\n        height: '<PERSON><PERSON><PERSON><PERSON> augstums',\n        name: '<PERSON>ont<PERSON>',\n        strikethrough: 'Nosvītrots',\n        superscript: 'Augšraksts',\n        subscript: 'Apakšraksts',\n        size: 'Fonta lielums',\n      },\n      image: {\n        image: 'Attēls',\n        insert: 'Ievietot attēlu',\n        resizeFull: 'Pilns izmērts',\n        resizeHalf: 'Samazināt 50%',\n        resizeQuarter: 'Samazināt 25%',\n        floatLeft: 'Līdzināt pa kreisi',\n        floatRight: 'Līdzināt pa labi',\n        floatNone: 'Nelīdzināt',\n        shapeRounded: 'Forma: apaļām malām',\n        shapeCircle: 'Forma: aplis',\n        shapeThumbnail: 'Forma: rām<PERSON>tis',\n        shapeNone: 'Forma: orģināla',\n        dragImageHere: 'Ievēlciet attēlu šeit',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Izvēlēties failu',\n        maximumFileSize: 'Maksimālais faila izmērs',\n        maximumFileSizeError: 'Faila izmērs pārāk liels!',\n        url: 'Attēla URL',\n        remove: 'Dzēst attēlu',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Video Link',\n        insert: 'Insert Video',\n        url: 'Video URL?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n      },\n      link: {\n        link: 'Saite',\n        insert: 'Ievietot saiti',\n        unlink: 'Noņemt saiti',\n        edit: 'Rediģēt',\n        textToDisplay: 'Saites saturs',\n        url: 'Koks URL adresas yra susietas?',\n        openInNewWindow: 'Atvērt jaunā logā',\n      },\n      table: {\n        table: 'Tabula',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Ievietot līniju',\n      },\n      style: {\n        style: 'Stils',\n        p: 'Parasts',\n        blockquote: 'Citāts',\n        pre: 'Kods',\n        h1: 'Virsraksts h1',\n        h2: 'Virsraksts h2',\n        h3: 'Virsraksts h3',\n        h4: 'Virsraksts h4',\n        h5: 'Virsraksts h5',\n        h6: 'Virsraksts h6',\n      },\n      lists: {\n        unordered: 'Nenumurēts saraksts',\n        ordered: 'Numurēts saraksts',\n      },\n      options: {\n        help: 'Palīdzība',\n        fullscreen: 'Pa visu ekrānu',\n        codeview: 'HTML kods',\n      },\n      paragraph: {\n        paragraph: 'Paragrāfs',\n        outdent: 'Samazināt atkāpi',\n        indent: 'Palielināt atkāpi',\n        left: 'Līdzināt pa kreisi',\n        center: 'Centrēt',\n        right: 'Līdzināt pa labi',\n        justify: 'Līdzināt gar abām malām',\n      },\n      color: {\n        recent: 'Nesen izmantotās',\n        more: 'Citas krāsas',\n        background: 'Fona krāsa',\n        foreground: 'Fonta krāsa',\n        transparent: 'Caurspīdīgs',\n        setTransparent: 'Iestatīt caurspīdīgumu',\n        reset: 'Atjaunot',\n        resetToDefault: 'Atjaunot noklusējumu',\n      },\n      shortcut: {\n        shortcuts: 'Saīsnes',\n        close: 'Aizvērt',\n        textFormatting: 'Teksta formatēšana',\n        action: 'Darbība',\n        paragraphFormatting: 'Paragrāfa formatēšana',\n        documentStyle: 'Dokumenta stils',\n        extraKeys: 'Citas taustiņu kombinācijas',\n      },\n      help: {\n        insertParagraph: 'Ievietot Paragrāfu',\n        undo: 'Atcelt iepriekšējo darbību',\n        redo: 'Atkārtot atcelto darbību',\n        tab: 'Atkāpe',\n        untab: 'Samazināt atkāpi',\n        bold: 'Pārvērst tekstu treknrakstā',\n        italic: 'Pārvērst tekstu slīprakstā (kursīvā)',\n        underline: 'Pasvītrot tekstu',\n        strikethrough: 'Nosvītrot tekstu',\n        removeFormat: 'Notīrīt stilu no teksta',\n        justifyLeft: 'Līdzīnāt saturu pa kreisi',\n        justifyCenter: 'Centrēt saturu',\n        justifyRight: 'Līdzīnāt saturu pa labi',\n        justifyFull: 'Izlīdzināt saturu gar abām malām',\n        insertUnorderedList: 'Ievietot nenumurētu sarakstu',\n        insertOrderedList: 'Ievietot numurētu sarakstu',\n        outdent: 'Samazināt/noņemt atkāpi paragrāfam',\n        indent: 'Uzlikt atkāpi paragrāfam',\n        formatPara: 'Mainīt bloka tipu uz (p) Paragrāfu',\n        formatH1: 'Mainīt bloka tipu uz virsrakstu H1',\n        formatH2: 'Mainīt bloka tipu uz virsrakstu H2',\n        formatH3: 'Mainīt bloka tipu uz virsrakstu H3',\n        formatH4: 'Mainīt bloka tipu uz virsrakstu H4',\n        formatH5: 'Mainīt bloka tipu uz virsrakstu H5',\n        formatH6: 'Mainīt bloka tipu uz virsrakstu H6',\n        insertHorizontalRule: 'Ievietot horizontālu līniju',\n        'linkDialog.show': 'Parādīt saites logu',\n      },\n      history: {\n        undo: 'Atsauks (undo)',\n        redo: 'Atkārtot (redo)',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "superscript", "subscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "insertParagraph", "undo", "redo", "tab", "untab", "removeFormat", "justifyLeft", "justifyCenter", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "formatPara", "formatH1", "formatH2", "formatH3", "formatH4", "formatH5", "formatH6", "insertHorizontalRule", "history", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}