{"version": 3, "file": "summernote-lite.css", "mappings": ";;;;;;;;;;;;AAMA;IACE;IACA;IACA;IACA;IACA;ACLF;ADSA;;IAEE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;ACPF;;ADYA;IACE;IACA;ACTF;;ADYA;IACE;IACA;IACA;ACTF;;ADYA;IACE;ACTF;;ADYA;IACE;ACTF;;ADaE;IACE;ACVJ;ADYE;IACE;ACVJ;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;ADgCA;IACE;AC7BF;;AC9QA;IC8GE,0BD7GoB;ICgHpB,sBDhHoB;IACpB;IACA,uBEKY;IFJZ;ADoRF;;AIxRA;IACE;IACA;IACA;IACA;IACA;IACA;IACA;AJ2RF;;AKlSA;IACE;IACA;IACA;ALqSF;AKnSE;IACE;ALqSJ;AKlSE;IACE;ALoSJ;AKjSE;;;;IAIE;ALmSJ;AKhSE;;IAEI;IACA;ALkSN;AK/RE;;IAEE;IACA;ALiSJ;AK7RI;IACE;AL+RN;;AMjUA;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;ICLA,WJqBmB;IIpBnB,sBJqBe;IIpBf,qBJqBmB;IIMnB;IACA,eJ3BU;II4BV,gBJxBY;IIyBZ,kBJnBmB;IDiGnB,yBItGqB;IJuGrB,sBIvGqB;IJwGrB,qBIxGqB;IJyGrB,iBIzGqB;AN4UvB;AOhVE;IAEE,WJeiB;IIdjB,yBJiBmB;IIhBnB,qBJeiB;AHkUrB;AO/UE;IACE,WJUiB;IITjB,yBJYmB;IIXnB,qBJUiB;AHuUrB;AO3UI;IAEE,sBJCW;IIAX,qBJCe;AH2UrB;AMxVE;IAGE,WHQiB;IGPjB;IACA;IACA,yBHQmB;IGPnB;IJyBF,kBIxBmB;AN0VrB;AMvVE;IAEE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;ANwVJ;AMrVE;IAGE,mBHHc;IDyBhB,qBIrBmB;IJsBnB,oBItBmB;IJuBnB,kBIvBmB;IJwBnB,aIxBmB;IJ0BnB;IACA;IAmCA,gBI7DsB;AN4VxB;AMzVE;IACE;AN2VJ;AMxVE;IACE;IACA;AN0VJ;;AMtVA;IACE;IACA;ANyVF;AMvVE;IAGE;IACA;IACA;IACA;IJrBF,kBIsBmB;ANyVrB;;AMpVA;IACE;IACA;ANuVF;;AMpVA;IACE;ANuVF;;AMhVE;;;IACE;ANqVJ;;AMjVA;IACE;IACA;IACA;IACA;IACA;ANoVF;;AMjVA;IACE;IACA;IACA;IACA;IACA;ANoVF;;AMjVA;IACE;IACC;IACE;IACE;IACI;IACD;ANoVV;;AQvcA;IACE;AR0cF;;AQtcE;IACE;IACA;ARycJ;;AQtcA;IACE;IACA;IACA;IACA;IACA;IACA,aL2ByB;IK1BzB;IACA;IACA,gBL8BkB;IK7BlB;IACA;IACA;INqFA,yCMpFoB;AR2ctB;AQzcE;IACE;AR2cJ;;AQvcA;IACE;AR0cF;;AQvcA;IACE;AR0cF;AQxcE;IACE,yBLRmB;AHkdvB;;AQtcA;;IAEE;IACA;IACA;ARycF;;ASvfA;IACE;IACA;IACA;IACA;IACA;IACA,aNyCc;IDmBd,kBO3DiB;IP4DjB,iBO5DiB;IP6DjB,eO7DiB;IP8DjB,UO9DiB;IPgEjB;IACA;IOhEA;AT+fF;AS7fE;IACE;AT+fJ;;AS3fA;IACE;IACA;IACA;IACA;IACA,gBNuDiB;IMtDjB;IACA;IACA;IPoFA,wCOnFoB;ATggBtB;;AS7fA;IACE;IACA;ATggBF;;AS7fA;IACE;IACA;ATggBF;AS7fE;IACE;IACA;IACA;IACA;IACA;IPoEF,0BADyB;IAIzB,sBAJyB;AFgc3B;;AS9fA;IACE;IACA;IACA;ATigBF;;AS7fA;IACE;IACA;ATggBF;;AS7fA;;IAEE;IACA;ATggBF;;AS7fA;IACE;ATggBF;;AS7fA;IACE;IACA;IACA;IACA;ATggBF;;AS7fA;IACE;IACA;IACA;IACA;IACA;IACA,aNpCyB;IMqCzB,gBNLkB;IDZlB,oBOkBiB;IPjBjB,mBOiBiB;IPhBjB,iBOgBiB;IPfjB,YOeiB;IPbjB;IACA;IOaA;ATqgBF;ASngBE;IACE;ATqgBJ;;AS/fA;IAEE;QACE;QACA;ITigBA;AACJ;AS9fA;IACE;QACE;ITggBA;AACJ;AS3fE;IACE;IACA;IACA;IACA;AT6fJ;AS1fE;IAEE;IAEI;IACJ;IACA;IACA;AT4fJ;ASzfE;IACE;IACA;IACA;IACA;IACA;IACA;AT2fJ;ASxfE;;IAEE;IACA;AT0fJ;ASvfE;IACE;ATyfJ;AStfE;IACE;ATwfJ;ASrfE;IACE;ATufJ;ASpfE;IACE;IACA;IACA;ATsfJ;ASnfE;;IAEE;ATqfJ;ASlfE;IACE;IACA;IACA;ATofJ;ASjfE;IACE;IACA;IACA;ATmfJ;AShfE;IACE;ATkfJ;AS/eE;;IAEE;ATifJ;AS/eE;;IAEE;ATifJ;;AU5qBA;IACE;AV+qBF;;AU5qBA;IACE;AV+qBF;;AU5qBA;IACE;IACA;IACA;IACA;IACA;IACA;AV+qBF;;AU5qBA;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IRsFA,0BADyB;IAIzB,sBAJyB;AF8lB3B;;AU9qBA;IACE,cPxBY;AHysBd;;AU9qBA;IAA+B;IAC7B,cP5BY;AH8sBd;;AU/qBA;IAAiC;IAC/B,cPhCY;AHmtBd;;AUhrBA;IACE,cPpCY;AHutBd;;AW7tBA;IACE;IACA,aR2Cc;IQ1Cd;IAGA;IAEA;IT0DA,kBSzDiB;IT0DjB,iBS1DiB;IT2DjB,eS3DiB;IT4DjB,US5DiB;IT8DjB;IACA;AFmqBF;AWhuBE;ITuDA,oBCXiB;IDYjB,mBCZiB;IDajB,iBCbiB;IDcjB,YCdiB;IDgBjB;IACA;AF2qBF;AWvuBE;IAAW;IAAmB;AX2uBhC;AW1uBE;IAAW;IAAmB;AX8uBhC;AW7uBE;IAAW;IAAmB;AXivBhC;AWhvBE;IAAW;IAAmB;AXovBhC;;AWhvBE;IACE;IACA;IACA;IACA;IACA,yBR8Be;AHqtBnB;AWhvBE;IACE;IACA;IACA;IACA;IACA,sBRsBe;AH4tBnB;AW/uBE;IACE;IACA;IACA;IACA;IACA,wBRce;AHmuBnB;AW/uBE;IACE;IACA;IACA;IACA;IACA,uBROe;AH0uBnB;;AW5uBA;IACE;IACA;IACA;IACA;IACA;AX+uBF;;AW5uBA;IACE,gBRTiB;IQUjB,uBRtDY;IQuDZ;IACA,WRXiB;IQYjB;IACA,sBRZiB;AH2vBnB;;AYjzBA;IACE;IACA,aT0Cc;ISzCd;IAGA;IACA,uBTCY;ISCZ;IACA,mBTiDc;IShDd;IACA;AZizBF;AY/yBE;IAAW;AZkzBb;AYjzBE;IAAW;IAAoB;AZqzBjC;AYpzBE;IAAW;IAAoB;AZwzBjC;AYvzBE;IAAW;IAAoB;AZ2zBjC;AY1zBE;IAAW;IAAoB;AZ8zBjC;;AY1zBE;IACE;IACA;IACA;IACA;IACA,4BT0CkC;ISzClC,wCTwCuB;AHqxB3B;AY3zBI;IACE;IACA;IACA;IACA;IACA;AZ6zBN;AYzzBE;IACE;IACA;IACA;IACA;IACA,yBTyBkC;ISxBlC,qCTuBuB;AHoyB3B;AYzzBI;IACE;IACA;IACA;IACA;IACA;AZ2zBN;AYvzBE;IACE;IACA;IACA;IACA;IACA,2BTQkC;ISPlC,uCTMuB;AHmzB3B;AYvzBI;IACE;IACA;IACA;IACA;IACA;AZyzBN;AYrzBE;IACE;IACA;IACA;IACA;IACA,0BTTkC;ISUlC,sCTXuB;AHk0B3B;AYrzBI;IACE;IACA;IACA;IACA;IACA;AZuzBN;;AYjzBA;IACE;IACA;IACA;IACA;AZozBF;AYlzBE;IACE;IACA;IACA;IACA;IACA;IACA;IAGA;IACA;AZkzBJ;;AY7yBA;IACE;IACA;IACA,WTxDc;ISyDd;IACA,yBT3Dc;IS4Dd;IACA;AZgzBF;;Aat6BA;6CAAA;AAQA;6CAAA;AAEA;IACE;Abm6BF;Aa95BE;IACE;IACA;IACA;IACA,mBANe;IAOf;IACA;Abg6BJ;Aa95BI;IACE;IACA;IACA;IACA;IACA;Abg6BN;Aa75BI;IACE,cAlBoB;Abi7B1B;Aa35BE;IACE;Ab65BJ;Aa15BE;IACE;Ab45BJ;Aa15BI;IACE;Ab45BN;Aa15BM;IACE;Ab45BR;Aaz5BM;IACE;Ab25BR;Aax5BM;IACE,kBAlDW;Ab48BnB;Aav5BM;IACE,iBAvDU;Abg9BlB;;Aan5BA;6CAAA;AAEA;;IAEE;Abs5BF;Aaj5BM;;IACE;Abo5BR;Aal5BM;;IACE;Abq5BR;Aah5BE;;IACE;Abm5BJ;Aah5BI;;IACE;IACA;IACA;Abm5BN;Aaj5BM;;IACE,2BA3FW;Ab++BnB;Aa/4BI;;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IXFJ,0BWKwB;IXFxB,sBWEwB;IXnExB,gBWoEqB;IACjB;Abq5BN;Aah5BE;;IACE;IACA;IACA;IACA;IACA;Abm5BJ;Aal5BI;;IACE;Abq5BN;Aah5BE;;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;Abm5BJ;Aah5BE;;IACE;IACA;Abm5BJ;Aah5BE;;IACE;Abm5BJ;Aah5BE;;IACE;Abm5BJ;Aah5BE;;IACE;Abm5BJ;Aah5BE;;IACE;Abm5BJ;Aah5BE;;IACE;Abm5BJ;Aah5BE;;IACE;Abm5BJ;Aah5BE;;IACE;Abm5BJ;Aah5BE;;IACE;IACA;IACA;IACA;IACA;Abm5BJ;Aah5BE;;IACE;Abm5BJ;Aah5BE;;IACE;IACA;Abm5BJ;Aah5BE;;IACE;IACA;Abm5BJ;Aah5BE;;IACE;IACA;Abm5BJ;Aah5BE;;IACE;IACA;Abm5BJ;Aa/4BE;;IACE,2BAhNe;IAiNf;IACA;IACA;Abk5BJ;Aaj5BI;;IACE;IACA;IACA;IACA;Abo5BN;Aan5BM;;IACE;IACA;IACA;Abs5BR;Aaj5BM;;IACE;Abo5BR;Aan5BQ;;IACE;Abs5BV;Aaj5BE;;IACE;Abo5BJ;;Aah5BA;IACE;Abm5BF;Aah5BI;IACE;Abk5BN;;Aa54BA;6CAAA;AAEA;IACE;IACA;Ab+4BF;Aa54BI;IACE;IACA;IACA;IACA;IACA;IACA;Ab84BN;Aa34BE;IACE;Ab64BJ;;Aaz4BA;6CAAA;AAEA;IACE;Ab44BF;;Aaz4BA;IACE;IACA;Ab44BF;Aa14BE;IACE;IACA;IACA;Ab44BJ;Aax4BI;IACE;IACA;Ab04BN;Aaz4BM;IACE;Ab24BR;Aa14BQ;IACE;IACA;IACA;IACA;IACA;Ab44BV;Aa14BQ;IACE;IACA;IACA;IACA;IACA;Ab44BV;Aa14BQ;IACE;IACA;IACA;IACA;IACA;Ab44BV;Aap4BM;IACE;IACA;Abs4BR;Aap4BM;IACE;IACA;Abs4BR;Aah4BI;IACE;Abk4BN;Aa73BI;IACE;IACA;Ab+3BN;Aa53BM;IACE;IACA;IACA;Ab83BR;Aa73BQ;IACE;Ab+3BV;Aa53BQ;IACE;IACA;IACA;IACA;Ab83BV;Aa33BQ;;;IAEE;IACA;IACA;IACA;IACA;IXhUR,kBWiUyB;Abg4B3B;Aa93BU;;;IACE;Abk4BZ;Aa93BQ;IACE;Abg4BV;Aa73BQ;IACE;Ab+3BV;Aa33BU;IACE;Ab63BZ;Aar3BI;IACE;IACA;Abu3BN;Aat3BM;IACE;Abw3BR;Aal3BE;IACE;Abo3BJ;Aah3BI;IACE;IACA;Abk3BN;Aaj3BM;IACE;IACA;Abm3BR;Aaj3BM;IACE;IACA;Abm3BR;Aa92BM;IACE;IACA;Abg3BR;Aa92BM;IACE;Abg3BR;Aa32BE;IACE;Ab62BJ;Aaz2BE;IACE;Ab22BJ;Aaz2BM;IACE;IACA;IACA;IACA;IACA;IACA;Ab22BR;Aaz2BM;IACE;IACA;Ab22BR;;Aar2BA;6CAAA;AAGE;IACE;IACA;IXnWF,wCWoWsB;Aby2BxB;Aav2BE;IACE;IACA;Aby2BJ;Aav2BE;IACE;Aby2BJ;Aat2BI;IACE;IACA;IACA;IACA;IACA;IACA;IACA;Abw2BN;Aan2BE;IACE;QACE;Ibq2BF;AACJ;;Aaj2BA;6CAAA;AAEA;IACE;IACA;IACA;Abo2BF;;Aaj2BA;6CAAA;AAIE;IACE;IACA;IACA;Abk2BJ;Aaj2BI;IACE;Abm2BN;Aah2BI;IACE;IACA;IACA;IXjcJ,oBWkcqB;IXjcrB,mBWicqB;IXhcrB,iBWgcqB;IX/brB,YW+bqB;IX7brB;IACA;AFmyCF;Aap2BI;IACE;IACA;IACA;Abs2BN;Aa/1BI;IAEE;Abg2BN;Aa71BI;IACE;IACA;IACA;IACA;Ab+1BN;Aa51BI;IACE;IACA;IACA;IACA;Ab81BN;Aa31BI;IACE;IACA;IACA;IACA;Ab61BN;Aa11BI;IACE;IACA;IACA;Ab41BN;Aaz1BI;IACE;IACA;IACA;Ab21BN;Aax1BI;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IX9gBJ,kBW+gBqB;IX7frB,oBW8fqB;IX7frB,mBW6fqB;IX5frB,iBW4fqB;IX3frB,YW2fqB;IXzfrB;IACA;AFy1CF;;Aa51BA;IACE;IACA;Ab+1BF;Aa71BE;IACE;IACA;IACA;Ab+1BJ;Aa51BM;IACE;IACA;Ab81BR;Aa51BQ;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;Ab81BV;;Aav1BA;6CAAA;AAGE;IACE;Aby1BJ;;AA56CM;IACE;IACA;AA+6CR;AA76CS;IACC;IACA;AA+6CV;AA36CM;IACE;IACA;IACA;IACA;IACA;AA66CR;AA16CM;;IAEE;IACA;IACA;AA46CR;AAz6CM;IACE;AA26CR;;AAr6CA;4CAAA;AAII;IACE;IACA;IACA;AAs6CN;AAn6CK;IACC;AAq6CN;AAh6CE;IACE;QACE;IAk6CF;AACJ;;AA55CE;IACE;IACA;AA+5CJ", "sources": ["webpack:///./src/styles/summernote/font.scss", "webpack:///./src/styles/lite/summernote-lite.scss", "webpack:///./src/styles/lite/scss/common.scss", "webpack:///./src/styles/summernote/elements.scss", "webpack:///./src/styles/lite/scss/variables.scss", "webpack:///./src/styles/lite/scss/toolbar.scss", "webpack:///./src/styles/lite/scss/btn-group.scss", "webpack:///./src/styles/lite/scss/buttons.scss", "webpack:///./src/styles/lite/scss/mixins/buttons.scss", "webpack:///./src/styles/lite/scss/dropdown.scss", "webpack:///./src/styles/lite/scss/modal.scss", "webpack:///./src/styles/lite/scss/form.scss", "webpack:///./src/styles/lite/scss/tooltip.scss", "webpack:///./src/styles/lite/scss/popover.scss", "webpack:///./src/styles/summernote/common.scss"], "sourcesContent": ["// Variables\n\n$sni-css-prefix: note-icon !default;\n\n// Path\n\n@font-face {\n  font-family: \"summernote\";\n  font-style: normal;\n  font-weight: 400;\n  font-display: auto;\n  src: url(\"./font/summernote.eot?#iefix\") format(\"embedded-opentype\"), url(\"./font/summernote.woff2\") format(\"woff2\"), url(\"./font/summernote.woff\") format(\"woff\"), url(\"./font/summernote.ttf\") format(\"truetype\");}\n\n// Core\n\n[class^=\"#{$sni-css-prefix}\"]:before,\n[class*=\" #{$sni-css-prefix}\"]:before {\n  display: inline-block;\n  font-family: \"summernote\";\n  font-style: normal;\n  font-size: inherit;\n  text-decoration: inherit;\n  text-rendering: auto;\n  text-transform: none;\n  vertical-align: middle;\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n  speak: none;\n}\n\n// Extras\n\n.#{$sni-css-prefix}-fw {\n  text-align: center;\n  width: 1.25em;\n}\n\n.#{$sni-css-prefix}-border {\n  border: solid 0.08em #eee;\n  border-radius: 0.1em;\n  padding: 0.2em 0.25em 0.15em;\n}\n\n.#{$sni-css-prefix}-pull-left {\n  float: left;\n}\n\n.#{$sni-css-prefix}-pull-right {\n  float: right;\n}\n\n.#{$sni-css-prefix} {\n  &.#{$sni-css-prefix}-pull-left {\n    margin-right: 0.3em;\n  }\n  &.#{$sni-css-prefix}-pull-right {\n    margin-left: 0.3em;\n  }\n}\n\n// Functions\n\n@function char($character-code) {\n  @if function-exists(\"selector-append\") {\n    @return unquote(\"\\\"\\\\#{$character-code}\\\"\");\n  }\n\n  @if \"\\\\#{'x'}\" == \"\\\\x\" {\n    @return str-slice(\"\\x\", 1, 1) + $character-code;\n  }\n  @else {\n    @return #{\"\\\"\\\\\"}#{$character-code + \"\\\"\"};\n  }\n}\n\n// Icons\n\n\n.note-icon-align::before {\n  content: \"\\ea01\";\n}\n\n.note-icon-align-center::before {\n  content: \"\\ea02\";\n}\n\n.note-icon-align-indent::before {\n  content: \"\\ea03\";\n}\n\n.note-icon-align-justify::before {\n  content: \"\\ea04\";\n}\n\n.note-icon-align-left::before {\n  content: \"\\ea05\";\n}\n\n.note-icon-align-outdent::before {\n  content: \"\\ea06\";\n}\n\n.note-icon-align-right::before {\n  content: \"\\ea07\";\n}\n\n.note-icon-arrow-circle-down::before {\n  content: \"\\ea08\";\n}\n\n.note-icon-arrow-circle-left::before {\n  content: \"\\ea09\";\n}\n\n.note-icon-arrow-circle-right::before {\n  content: \"\\ea0a\";\n}\n\n.note-icon-arrow-circle-up::before {\n  content: \"\\ea0b\";\n}\n\n.note-icon-arrows-alt::before {\n  content: \"\\ea0c\";\n}\n\n.note-icon-arrows-h::before {\n  content: \"\\ea0d\";\n}\n\n.note-icon-arrows-v::before {\n  content: \"\\ea0e\";\n}\n\n.note-icon-bold::before {\n  content: \"\\ea0f\";\n}\n\n.note-icon-caret::before {\n  content: \"\\ea10\";\n}\n\n.note-icon-chain-broken::before {\n  content: \"\\ea11\";\n}\n\n.note-icon-circle::before {\n  content: \"\\ea12\";\n}\n\n.note-icon-close::before {\n  content: \"\\ea13\";\n}\n\n.note-icon-code::before {\n  content: \"\\ea14\";\n}\n\n.note-icon-col-after::before {\n  content: \"\\ea15\";\n}\n\n.note-icon-col-before::before {\n  content: \"\\ea16\";\n}\n\n.note-icon-col-remove::before {\n  content: \"\\ea17\";\n}\n\n.note-icon-eraser::before {\n  content: \"\\ea18\";\n}\n\n.note-icon-float-left::before {\n  content: \"\\ea19\";\n}\n\n.note-icon-float-none::before {\n  content: \"\\ea1a\";\n}\n\n.note-icon-float-right::before {\n  content: \"\\ea1b\";\n}\n\n.note-icon-font::before {\n  content: \"\\ea1c\";\n}\n\n.note-icon-frame::before {\n  content: \"\\ea1d\";\n}\n\n.note-icon-italic::before {\n  content: \"\\ea1e\";\n}\n\n.note-icon-link::before {\n  content: \"\\ea1f\";\n}\n\n.note-icon-magic::before {\n  content: \"\\ea20\";\n}\n\n.note-icon-menu-check::before {\n  content: \"\\ea21\";\n}\n\n.note-icon-minus::before {\n  content: \"\\ea22\";\n}\n\n.note-icon-orderedlist::before {\n  content: \"\\ea23\";\n}\n\n.note-icon-pencil::before {\n  content: \"\\ea24\";\n}\n\n.note-icon-picture::before {\n  content: \"\\ea25\";\n}\n\n.note-icon-question::before {\n  content: \"\\ea26\";\n}\n\n.note-icon-redo::before {\n  content: \"\\ea27\";\n}\n\n.note-icon-rollback::before {\n  content: \"\\ea28\";\n}\n\n.note-icon-row-above::before {\n  content: \"\\ea29\";\n}\n\n.note-icon-row-below::before {\n  content: \"\\ea2a\";\n}\n\n.note-icon-row-remove::before {\n  content: \"\\ea2b\";\n}\n\n.note-icon-special-character::before {\n  content: \"\\ea2c\";\n}\n\n.note-icon-square::before {\n  content: \"\\ea2d\";\n}\n\n.note-icon-strikethrough::before {\n  content: \"\\ea2e\";\n}\n\n.note-icon-subscript::before {\n  content: \"\\ea2f\";\n}\n\n.note-icon-summernote::before {\n  content: \"\\ea30\";\n}\n\n.note-icon-superscript::before {\n  content: \"\\ea31\";\n}\n\n.note-icon-table::before {\n  content: \"\\ea32\";\n}\n\n.note-icon-text-height::before {\n  content: \"\\ea33\";\n}\n\n.note-icon-trash::before {\n  content: \"\\ea34\";\n}\n\n.note-icon-underline::before {\n  content: \"\\ea35\";\n}\n\n.note-icon-undo::before {\n  content: \"\\ea36\";\n}\n\n.note-icon-unorderedlist::before {\n  content: \"\\ea37\";\n}\n\n.note-icon-video::before {\n  content: \"\\ea38\";\n}\n\n", "// Core variables and mixins\n@import '../summernote/font.scss';\n@import '../summernote/elements.scss';\n@import \"scss/variables.scss\";\n@import \"scss/mixins.scss\";\n\n@import \"scss/common.scss\";\n@import \"scss/toolbar.scss\";\n@import \"scss/btn-group.scss\";\n@import \"scss/buttons.scss\";\n@import \"scss/dropdown.scss\";\n@import \"scss/modal.scss\";\n@import \"scss/form.scss\";\n@import \"scss/tooltip.scss\";\n@import \"scss/popover.scss\";\n\n@import '../summernote/common.scss';\n\n.note-editor {\n  .note-editing-area {\n    .note-editable {\n      table {\n        width: 100%;\n        border-collapse: collapse;\n\n         td, th {\n          border: 1px solid #ececec;\n          padding: 5px 3px;\n        }\n      }\n\n      a {\n        background-color: inherit;\n        text-decoration: inherit;\n        font-family: inherit;\n        font-weight: inherit;\n        color: #337ab7;\n      }\n\n      a:hover,\n      a:focus {\n        color: #23527c;\n        text-decoration: underline;\n        outline: 0;\n      }\n\n      figure {\n        margin: 0;\n      }\n    }\n  }\n}\n\n/* Dialog\n ------------------------------------------*/\n.note-modal {\n  .note-modal-body {\n    label {\n      margin-bottom: 2px;\n      padding: 2px 5px;\n      display: inline-block;\n    }\n\n     .help-list-item:hover {\n      background-color: #e0e0e0;\n    }\n  }\n\n   // [workaround] firefox fileinput\n  @-moz-document url-prefix() {\n    .note-image-input {\n      height: auto;\n    }\n  }\n}\n\n\n.help-list-item {\n  label {\n    margin-bottom:5px;\n    display:inline-block;\n  }\n}\n", ".note-frame {\n  @include box-sizing(border-box);\n  color: #000;\n  font-family: $font-family;\n  border-radius: 4px;\n}\n", "@mixin gradient($color: #F5F5F5, $start: #EEE, $stop: #FFF) {\n  background: $color;\n  background: -webkit-gradient(linear,\n                               left bottom,\n                               left top,\n                               color-stop(0, $start),\n                               color-stop(1, $stop));\n  background: -ms-linear-gradient(bottom,\n                                  $start,\n                                  $stop);\n  background: -moz-linear-gradient(center bottom,\n                                   $start 0%,\n                                   $stop 100%);\n  background: -o-linear-gradient($stop,\n                                 $start);\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#{ie-hex-str($stop)}', endColorstr='#{ie-hex-str($start)}', GradientType=0);\n}\n@mixin bw-gradient($color: #F5F5F5, $start: 0, $stop: 255) {\n  background: $color;\n  background: -webkit-gradient(linear,\n                               left bottom,\n                               left top,\n                               color-stop(0, rgb($start,$start,$start)),\n                               color-stop(1, rgb($stop,$stop,$stop)));\n  background: -ms-linear-gradient(bottom,\n                                  rgb($start,$start,$start) 0%,\n                                  rgb($stop,$stop,$stop) 100%);\n  background: -moz-linear-gradient(center bottom,\n                                   rgb($start,$start,$start) 0%,\n                                   rgb($stop,$stop,$stop) 100%);\n  background: -o-linear-gradient(rgb($stop,$stop,$stop),\n                                 rgb($start,$start,$start));\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#{ie-hex-str(rgb($stop,$stop,$stop))}', endColorstr='#{ie-hex-str(rgb($start,$start,$start))}', GradientType=0);\n}\n@mixin bordered($top-color: #EEE, $right-color: #EEE, $bottom-color: #EEE, $left-color: #EEE) {\n  border-top: solid 1px $top-color;\n  border-left: solid 1px $left-color;\n  border-right: solid 1px $right-color;\n  border-bottom: solid 1px $bottom-color;\n}\n@mixin drop-shadow($x-axis: 0, $y-axis: 1px, $blur: 2px, $alpha: 0.1) {\n  -webkit-box-shadow: $x-axis $y-axis $blur rgba(0, 0, 0, $alpha);\n  -moz-box-shadow: $x-axis $y-axis $blur rgba(0, 0, 0, $alpha);\n  box-shadow: $x-axis $y-axis $blur rgba(0, 0, 0, $alpha);\n}\n@mixin rounded($radius: 2px) {\n  -webkit-border-radius: $radius;\n  -moz-border-radius: $radius;\n  border-radius: $radius;\n}\n@mixin border-radius($topright: 0, $bottomright: 0, $bottomleft: 0, $topleft: 0) {\n  -webkit-border-top-right-radius: $topright;\n  -webkit-border-bottom-right-radius: $bottomright;\n  -webkit-border-bottom-left-radius: $bottomleft;\n  -webkit-border-top-left-radius: $topleft;\n  -moz-border-radius-topright: $topright;\n  -moz-border-radius-bottomright: $bottomright;\n  -moz-border-radius-bottomleft: $bottomleft;\n  -moz-border-radius-topleft: $topleft;\n  border-top-right-radius: $topright;\n  border-bottom-right-radius: $bottomright;\n  border-bottom-left-radius: $bottomleft;\n  border-top-left-radius: $topleft;\n  @include background-clip(padding-box);\n}\n@mixin opacity($opacity: 0.5) {\n  -webkit-opacity: $opacity;\n  -khtml-opacity: $opacity;\n  -moz-opacity: $opacity;\n  opacity: $opacity;\n  $opperc: $opacity * 100;\n  -ms-filter: progid:DXImageTransform.Microsoft.Alpha(opacity=#{$opperc});\n  filter: alpha(opacity=$opperc);\n}\n@mixin transition-duration($duration: 0.2s) {\n  -moz-transition-duration: $duration;\n  -webkit-transition-duration: $duration;\n  -o-transition-duration: $duration;\n  transition-duration: $duration;\n}\n@mixin transform($arguments...) {\n  -webkit-transform: $arguments;\n  -moz-transform: $arguments;\n  -o-transform: $arguments;\n  -ms-transform: $arguments;\n  transform: $arguments;\n}\n@mixin rotation($deg:5deg) {\n  @include transform(rotate($deg));\n}\n@mixin scale($ratio:1.5) {\n  @include transform(scale($ratio));\n}\n@mixin transition($duration:0.2s, $ease:ease-out) {\n  -webkit-transition: all $duration $ease;\n  -moz-transition: all $duration $ease;\n  -o-transition: all $duration $ease;\n  transition: all $duration $ease;\n}\n@mixin inner-shadow($horizontal:0, $vertical:1px, $blur:2px, $alpha: 0.4) {\n  -webkit-box-shadow: inset $horizontal $vertical $blur rgba(0, 0, 0, $alpha);\n  -moz-box-shadow: inset $horizontal $vertical $blur rgba(0, 0, 0, $alpha);\n  box-shadow: inset $horizontal $vertical $blur rgba(0, 0, 0, $alpha);\n}\n@mixin box-shadow($arguments) {\n  -webkit-box-shadow: $arguments;\n  -moz-box-shadow: $arguments;\n  box-shadow: $arguments;\n}\n@mixin box-sizing($sizing: border-box) {\n  -ms-box-sizing: $sizing;\n  -moz-box-sizing: $sizing;\n  -webkit-box-sizing: $sizing;\n  box-sizing: $sizing;\n}\n@mixin user-select($argument: none) {\n  -webkit-user-select: $argument;\n  -moz-user-select: $argument;\n  -ms-user-select: $argument;\n  user-select: $argument;\n}\n@mixin columns($colwidth: 250px, $colcount: 0, $colgap: 50px, $columnRuleColor: #EEE, $columnRuleStyle: solid, $columnRuleWidth: 1px) {\n  -moz-column-width: $colwidth;\n  -moz-column-count: $colcount;\n  -moz-column-gap: $colgap;\n  -moz-column-rule-color: $columnRuleColor;\n  -moz-column-rule-style: $columnRuleStyle;\n  -moz-column-rule-width: $columnRuleWidth;\n  -webkit-column-width: $colwidth;\n  -webkit-column-count: $colcount;\n  -webkit-column-gap: $colgap;\n  -webkit-column-rule-color: $columnRuleColor;\n  -webkit-column-rule-style: $columnRuleStyle;\n  -webkit-column-rule-width: $columnRuleWidth;\n  column-width: $colwidth;\n  column-count: $colcount;\n  column-gap: $colgap;\n  column-rule-color: $columnRuleColor;\n  column-rule-style: $columnRuleStyle;\n  column-rule-width: $columnRuleWidth;\n}\n@mixin translate($x:0, $y:0) {\n  @include transform(translate($x, $y));\n}\n@mixin background-clip($argument: padding-box) {\n  -moz-background-clip: $argument;\n  -webkit-background-clip: $argument;\n  background-clip: $argument;\n}\n", "\n$gray-base:   #000;\n$gray-darker: lighten($gray-base, 13.5%); // #222\n$gray-dark:   lighten($gray-base, 20%);   // #333\n$gray:        lighten($gray-base, 33.5%); // #555\n$gray-light:  lighten($gray-base, 46.7%); // #777\n$gray-lighter:lighten($gray-base, 93.5%); // #eee\n\n$font-family: sans-serif;\n$font-size: 14px;\n$font-size-large: ceil(($font-size * 1.25));\n$font-size-small: ceil(($font-size * 0.85));\n\n$line-height: 1.4;\n$line-height-computed: floor(($line-height * $font-size));\n\n$padding-base-vertical: 5px;\n$padding-base-horizontal: 10px;\n\n$border-radius-base: 3px;\n$btn-border-radius-base: $border-radius-base;\n\n$icon-font-path: \"../fonts/\";\n$icon-font-name: \"summernote\";\n$icon-font-svg-id: \"summernote\";\n\n$btn-font-weight: normal;\n$btn-default-color : #333;\n$btn-default-bg: #fff;\n$btn-default-border: #dae0e5;\n$btn-default-hover-bg: #ebebeb;\n$btn-default-active-bg: #f0f0f0;\n\n$input-bg: #fff;\n$input-bg-disabled: $gray-lighter;\n$input-color: $gray;\n$input-border: #ccc;\n\n$input-border-focus: #66afe9;\n$input-color-placeholder: #999;\n\n$cursor-disabled: not-allowed;\n\n$zindex-dropdown:          1000;\n$zindex-popover:1060;\n$zindex-tooltip:1070;\n$zindex-modal-background:  1040;\n$zindex-modal:  1050;\n\n$dropdown-color-bg: #fff;\n$dropdown-color-border: #e2e2e2;\n\n$tooltip-max-width:200px;\n$tooltip-color:    #fff;\n$tooltip-bg:       #000;\n$tooltip-opacity:  .9;\n$tooltip-arrow-width:         5px;\n$tooltip-arrow-color:         $tooltip-bg;\n\n$popover-bg:    #ffffff;\n$popover-color: #000;\n$popover-max-width:        276px;\n$popover-border-color:     rgba(0,0,0,.2);\n$popover-fallback-border-color:       #ccc;\n$popover-title-bg:         darken($popover-bg, 3%);\n$popover-arrow-width:      10px;\n$popover-arrow-color:      $popover-bg;\n$popover-arrow-outer-width:($popover-arrow-width + 1);\n$popover-arrow-outer-color:fade-in($popover-border-color, .05);\n$popover-arrow-outer-fallback-color:  darken($popover-fallback-border-color, 20%);\n\n\n$modal-inner-padding: 15px;\n$modal-title-padding: 15px;\n$modal-title-line-height: $line-height;\n$modal-content-bg: #fff;\n$modal-content-border-color: rgba(0,0,0,.2);\n$modal-content-fallback-border-color: #999;\n$modal-backdrop-bg: #000;\n$modal-backdrop-opacity: .5;\n$modal-header-border-color: #e5e5e5;\n$modal-footer-border-color: $modal-header-border-color;\n", ".note-toolbar {\n  padding: 10px 5px;\n  border-bottom: 1px solid #e2e2e2;\n  color: #333;\n  background-color: #f5f5f5;\n  border-color: #ddd;\n  border-top-left-radius: 3px;\n  border-top-right-radius: 3px;\n}\n", ".note-btn-group {\n  position: relative;\n  display: inline-block;\n  margin-right: 8px;\n\n  > .note-btn-group {\n    margin-right: 0;\n  }\n\n  > .note-btn:first-child {\n    margin-left: 0;\n  }\n\n  .note-btn + .note-btn,\n  .note-btn + .note-btn-group,\n  .note-btn-group + .note-btn,\n  .note-btn-group + .note-btn-group {\n    margin-left: -1px;\n  }\n\n  > .note-btn:not(:first-child),\n  > .note-btn-group:not(:first-child) > .note-btn {\n      border-top-left-radius: 0;\n      border-bottom-left-radius: 0;\n  }\n\n  > .note-btn:not(:last-child):not(.dropdown-toggle),\n  > .note-btn-group:not(:last-child) > .note-btn {\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n  }\n\n  &.open {\n    > .note-dropdown {\n      display: block;\n    }\n  }\n}\n", ".note-btn {\n  display: inline-block;\n  font-weight: 400;\n  margin-bottom: 0;\n  text-align: center;\n  vertical-align: middle;\n  touch-action: manipulation;\n  cursor: pointer;\n  background-image: none;\n  border: 1px solid $btn-default-border;\n  white-space: nowrap;\n  outline: 0;\n  @include button-maker($btn-default-color, $btn-default-bg, $btn-default-hover-bg,  $btn-default-active-bg, $btn-default-border);\n  @include button-size($padding-base-vertical, $padding-base-horizontal, $font-size, $line-height, $btn-border-radius-base);\n  @include user-select(none);\n\n  &:hover,\n  &:focus,\n  &.focus {\n    color: $btn-default-color;\n    text-decoration: none;\n    border: 1px solid $btn-default-border;\n    background-color: $btn-default-hover-bg;\n    outline: 0;\n    @include rounded(1px);\n  }\n\n  &:active,\n  &.active {\n    outline: 0;\n    background-image: none;\n    color: #333;\n    text-decoration: none;\n    border: 1px solid #dae0e5;\n    background-color: #ebebeb;\n    outline: 0;\n    border-radius: 1px;\n    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);\n    box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);\n  }\n\n  &.disabled,\n  &[disabled],\n  fieldset[disabled] & {\n    cursor: $cursor-disabled;\n    @include opacity(.65);\n    @include box-shadow(none);\n  }\n\n  & > span.note-icon-caret:first-child {\n    margin-left: -1px;\n  }\n\n  & > span.note-icon-caret:nth-child(2) {\n    padding-left: 3px;\n    margin-right: -5px;\n  }\n}\n\n.note-btn-primary {\n  background: #fa6362;\n  color: #fff;\n\n  &:hover,\n  &:focus,\n  &.focus {\n    color: #fff;\n    text-decoration: none;\n    border: 1px solid $btn-default-border;\n    background-color: #fa6362;\n    @include rounded(1px);\n  }\n\n}\n\n.note-btn-block {\n  display: block;\n  width: 100%;\n}\n\n.note-btn-block + .note-btn-block {\n  margin-top:5px;\n}\n\n// Specificity overrides\ninput[type=\"submit\"],\ninput[type=\"reset\"],\ninput[type=\"button\"] {\n  &.note-btn-block {\n    width: 100%;\n  }\n}\n\nbutton.close {\n  padding: 0;\n  cursor: pointer;\n  background: transparent;\n  border: 0;\n  -webkit-appearance: none;\n}\n\n.close {\n  float: right;\n  font-size: 21px;\n  line-height: 1;\n  color: #000;\n  opacity: .2;\n}\n\n.close:hover {\n  -webkit-opacity: 1;\n   -khtml-opacity: 1;\n     -moz-opacity: 1;\n       -ms-filter: alpha(opacity=100);\n           filter: alpha(opacity=100);\n          opacity: 1\n}\n", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n@mixin button-maker($color, $background, $focusBackground, $activeBackground, $border) {\n  color: $color;\n  background-color: $background;\n  border-color: $border;\n\n  &:focus,\n  &.focus {\n    color: $color;\n    background-color: $focusBackground;\n    border-color: $border;\n  }\n  &:hover {\n    color: $color;\n    background-color: $focusBackground;\n    border-color: $border;\n  }\n\n  &.disabled,\n  &[disabled],\n  fieldset[disabled] & {\n    &:focus,\n    &.focus {\n      background-color: $background;\n      border-color: $border;\n    }\n  }\n}\n\n// Button sizes\n@mixin button-size($padding-vertical, $padding-horizontal, $font-size, $line-height, $border-radius) {\n  padding: $padding-vertical $padding-horizontal;\n  font-size: $font-size;\n  line-height: $line-height;\n  border-radius: $border-radius;\n}\n", ".note-dropdown {\n  position: relative;\n\n}\n.note-color {\n  .dropdown-toggle {\n    width: 30px;\n    padding-left: 5px;\n  }\n}\n.note-dropdown-menu {\n  display: none;\n  min-width: 100px;\n  position: absolute;\n  top: 100%;\n  left: 0;\n  z-index : $zindex-dropdown;\n  float: left;\n  text-align:left;\n  background: $dropdown-color-bg;\n  border: 1px solid $dropdown-color-border;\n  padding: 5px;\n  background-clip: padding-box;\n  @include box-shadow(0 1px 1px rgba(0,0,0,.06));\n\n  > *:last-child {\n    margin-right: 0;\n  }\n}\n\n.note-btn-group.open .note-dropdown-menu {\n  display: block;\n}\n\n.note-dropdown-item {\n  display: block;\n\n  &:hover {\n    background-color: $btn-default-hover-bg;\n  }\n}\n\na.note-dropdown-item,\na.note-dropdown-item:hover {\n  margin: 5px 0;\n  color: #000;\n  text-decoration: none;\n}\n", ".note-modal {\n  position: fixed;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  z-index: $zindex-modal;\n  @include opacity(1);\n  display: none;\n\n  &.open {\n    display:block;\n  }\n}\n\n.note-modal-content {\n  position: relative;\n  width: auto;\n  margin: 30px 20px;\n  border: 1px solid $modal-content-border-color;\n  background: $modal-content-bg;\n  background-clip: border-box;\n  outline: 0;\n  border-radius: 5px;\n  @include box-shadow(0 3px 9px rgba(0,0,0,.5));\n}\n\n.note-modal-header {\n  padding: 10px 20px;\n  border: 1px solid #ededef;\n}\n\n.note-modal-body {\n  position: relative;\n  padding: 20px 30px;\n\n  // shortcut text style\n  kbd {\n    border-radius: 2px;\n    background-color: #000;\n    color: #fff;\n    padding: 3px 5px;\n    font-weight: 700;\n    @include box-sizing();\n  }\n}\n\n.note-modal-footer {\n  height: 40px;\n  padding: 10px;\n  text-align: center;\n\n}\n\n.note-modal-footer a {\n  color: #337ab7;\n  text-decoration: none\n}\n\n.note-modal-footer a:hover,\n.note-modal-footer a:focus {\n  color: #23527c;\n  text-decoration: underline\n}\n\n.note-modal-footer .note-btn {\n  float: right\n}\n\n.note-modal-title {\n  font-size: 20px;\n  color: #42515f;\n  margin: 0;\n  line-height: 1.4;\n}\n\n.note-modal-backdrop {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  top: 0;\n  z-index: $zindex-modal-background;\n  background: $modal-backdrop-bg;\n  @include opacity(0.5);\n  display: none;\n\n  &.open {\n    display: block;\n  }\n}\n\n\n// Scale up the modal\n@media (min-width: 768px) {\n  // Automatically set modal's width for larger viewports\n  .note-modal-content {\n    width: 600px;\n    margin: 30px auto;\n  }\n}\n\n@media (min-width: 992px) {\n  .note-modal-content-large {\n    width: 900px;\n  }\n}\n\n.note-modal {\n\n  .note-help-block {\n    display: block;\n    margin-top: 5px;\n    margin-bottom: 10px;\n    color: #737373;\n  }\n\n  .note-nav {\n    display: -ms-flexbox;\n    display: flex;\n    -ms-flex-wrap: wrap;\n        flex-wrap: wrap;\n    padding-left: 0;\n    margin-bottom: 0;\n    list-style: none;\n  }\n\n  .note-nav-link {\n    display: block;\n    padding: 0.5rem 1rem;\n    color: #007bff;\n    text-decoration: none;\n    background-color: transparent;\n    -webkit-text-decoration-skip: objects;\n  }\n\n  .note-nav-link:focus,\n  .note-nav-link:hover {\n    color: #0056b3;\n    text-decoration: none;\n  }\n\n  .note-nav-link.disabled {\n    color: #868e96;\n  }\n\n  .note-nav-tabs {\n    border-bottom: 1px solid #ddd;\n  }\n\n  .note-nav-tabs .note-nav-item {\n    margin-bottom: -1px;\n  }\n\n  .note-nav-tabs .note-nav-link {\n    border: 1px solid transparent;\n    border-top-left-radius: 0.25rem;\n    border-top-right-radius: 0.25rem;\n  }\n\n  .note-nav-tabs .note-nav-link:focus,\n  .note-nav-tabs .note-nav-link:hover {\n    border-color: #e9ecef #e9ecef #ddd;\n  }\n\n  .note-nav-tabs .note-nav-link.disabled {\n    color: #868e96;\n    background-color: transparent;\n    border-color: transparent;\n  }\n\n  .note-nav-tabs .note-nav-item.show .note-nav-link {\n    color: #495057;\n    background-color: #fff;\n    border-color: #ddd #ddd #fff;\n  }\n\n  .note-tab-content {\n    margin: 15px auto;\n  }\n\n  .note-tab-content > .note-tab-pane:target ~ .note-tab-pane:last-child,\n  .note-tab-content > .note-tab-pane {\n    display: none;\n  }\n  .note-tab-content > :last-child,\n  .note-tab-content > .note-tab-pane:target {\n    display: block;\n  }\n}\n", ".note-form-group {\n  padding-bottom: 20px;\n}\n\n.note-form-group:last-child {\n  padding-bottom: 0;\n}\n\n.note-form-label {\n  display: block;\n  width: 100%;\n  font-size: 16px;\n  color: #42515f;\n  margin-bottom: 10px;\n  font-weight: 700;\n}\n\n.note-input {\n  width: 100%;\n  display: block;\n  border: 1px solid #ededef;\n  background: #fff;\n  outline: 0;\n  padding: 6px 4px;\n  font-size: 14px;\n  @include box-sizing();\n}\n\n\n.note-input::-webkit-input-placeholder {\n  color: $gray-lighter;\n}\n\n.note-input:-moz-placeholder { /* Firefox 18- */\n  color: $gray-lighter;\n}\n\n.note-input::-moz-placeholder {  /* Firefox 19+ */\n  color: $gray-lighter;\n}\n\n.note-input:-ms-input-placeholder {\n  color: $gray-lighter;\n}\n", ".note-tooltip {\n  position: absolute;\n  z-index: $zindex-tooltip;\n  display: block;\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  font-size: 13px;\n\n  transition: opacity .15s;\n  @include opacity(0);\n\n  &.in     { @include opacity($tooltip-opacity); }\n  &.top    { margin-top:  -3px; padding: $tooltip-arrow-width 0; }\n  &.right  { margin-left:  3px; padding: 0 $tooltip-arrow-width; }\n  &.bottom { margin-top:   3px; padding: $tooltip-arrow-width 0; }\n  &.left   { margin-left: -3px; padding: 0 $tooltip-arrow-width; }\n}\n\n.note-tooltip {\n  &.bottom .note-tooltip-arrow {\n    top: 0;\n    left: 50%;\n    margin-left: -$tooltip-arrow-width;\n    border-width: 0 $tooltip-arrow-width $tooltip-arrow-width;\n    border-bottom-color: $tooltip-arrow-color;\n  }\n\n  &.top .note-tooltip-arrow {\n    bottom: 0;\n    left: 50%;\n    margin-left: -$tooltip-arrow-width;\n    border-width: $tooltip-arrow-width $tooltip-arrow-width 0;\n    border-top-color: $tooltip-arrow-color;\n  }\n\n  &.right .note-tooltip-arrow {\n    top: 50%;\n    left: 0;\n    margin-top: -$tooltip-arrow-width;\n    border-width: $tooltip-arrow-width $tooltip-arrow-width $tooltip-arrow-width 0;\n    border-right-color: $tooltip-arrow-color;\n  }\n  &.left .note-tooltip-arrow {\n    top: 50%;\n    right: 0;\n    margin-top: -$tooltip-arrow-width;\n    border-width: $tooltip-arrow-width 0 $tooltip-arrow-width $tooltip-arrow-width;\n    border-left-color: $tooltip-arrow-color;\n  }\n}\n\n\n.note-tooltip-arrow {\n  position: absolute;\n  width: 0;\n  height: 0;\n  border-color: transparent;\n  border-style: solid;\n}\n\n.note-tooltip-content {\n  max-width: $tooltip-max-width;\n  font-family: $font-family;\n  padding: 3px 8px;\n  color: $tooltip-color;\n  text-align: center;\n  background-color: $tooltip-bg;\n}\n", ".note-popover {\n  position: absolute;\n  z-index: $zindex-popover;\n  display: block;\n  // Our parent element can be arbitrary since popovers are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  font-size: 13px;\n  font-family: $font-family;\n\n  display: none;\n  background: $popover-bg;\n  border: 1px solid $popover-border-color;\n  border: 1px solid $popover-fallback-border-color;\n\n  &.in     { display: block; }\n  &.top    { margin-top:  -10px; padding: $tooltip-arrow-width 0; }\n  &.right  { margin-left:  10px; padding: 0 $tooltip-arrow-width; }\n  &.bottom { margin-top:   10px; padding: $tooltip-arrow-width 0; }\n  &.left   { margin-left: -10px; padding: 0 $tooltip-arrow-width; }\n}\n\n.note-popover {\n  &.bottom .note-popover-arrow {\n    top: -11px;\n    left: 20px;\n    margin-left: -$popover-arrow-width;\n    border-top-width: 0;\n    border-bottom-color: $popover-arrow-outer-fallback-color;\n    border-bottom-color: $popover-arrow-outer-color;\n\n    &::after {\n      top: 1px;\n      margin-left: -10px;\n      content: \" \";\n      border-top-width: 0;\n      border-bottom-color: #fff;\n    }\n  }\n\n  &.top .note-popover-arrow {\n    bottom: -11px;\n    left: 20px;\n    margin-left: -$popover-arrow-width;\n    border-bottom-width: 0;\n    border-top-color: $popover-arrow-outer-fallback-color;\n    border-top-color: $popover-arrow-outer-color;\n\n    &::after {\n      bottom: 1px;\n      margin-left: -10px;\n      content: \" \";\n      border-bottom-width: 0;\n      border-top-color: #fff;\n    }\n  }\n\n  &.right .note-popover-arrow {\n    top: 50%;\n    left: -11px;\n    margin-top: -$popover-arrow-width;\n    border-left-width: 0;\n    border-right-color: $popover-arrow-outer-fallback-color;\n    border-right-color: $popover-arrow-outer-color;\n\n    &::after {\n      left: 1px;\n      margin-top: -10px;\n      content: \" \";\n      border-left-width: 0;\n      border-right-color: #fff;\n    }\n\n  }\n  &.left .note-popover-arrow {\n    top: 50%;\n    right: -11px;\n    margin-top: -$popover-arrow-width;\n    border-right-width: 0;\n    border-left-color: $popover-arrow-outer-fallback-color;\n    border-left-color: $popover-arrow-outer-color;\n\n    &::after {\n      right: 1px;\n      margin-top: -10px;\n      content: \" \";\n      border-right-width: 0;\n      border-left-color: #fff;\n    }\n  }\n}\n\n\n.note-popover-arrow {\n  position: absolute;\n  width: 0;\n  height: 0;\n  border: 11px solid transparent;\n\n  &::after {\n    position: absolute;\n    display: block;\n    width: 0;\n    height: 0;\n    border-color: transparent;\n    border-style: solid;\n\n\n    content :  \" \";\n    border-width: 10px;\n  }\n\n}\n\n.note-popover-content {\n  /*max-width: $popover-max-width;*/\n  padding: 3px 8px;\n  color: $popover-color;\n  text-align: center;\n  background-color: $popover-bg;\n  min-width: 100px;\n  min-height: 30px;\n}\n", "@import \"elements.scss\";\n\n/* Theme Variables\n ------------------------------------------ */\n$border-color: #00000032;\n$background-color: #8080801d;\n\n$img-margin-left: 10px;\n$img-margin-right: 10px;\n\n/* Layout\n ------------------------------------------ */\n.note-editor {\n  position: relative;\n\n  // dropzone\n  $dropzone-color: lightskyblue;\n  $dropzone-active-color: darken($dropzone-color, 30);\n  .note-dropzone {\n    position: absolute;\n    display: none;\n    z-index: 100;\n    color: $dropzone-color;\n    background-color: #fff;\n    opacity: 0.95;\n\n    .note-dropzone-message {\n      display: table-cell;\n      vertical-align: middle;\n      text-align: center;\n      font-size: 28px;\n      font-weight: 700;\n    }\n\n    &.hover {\n      color: $dropzone-active-color;\n    }\n  }\n\n  &.dragover .note-dropzone {\n    display: table;\n  }\n\n  .note-editing-area {\n    position: relative;\n\n    .note-editable {\n      outline: none;\n\n      sup {\n        vertical-align: super;\n      }\n\n      sub {\n        vertical-align: sub;\n      }\n\n      img.note-float-left {\n        margin-right: $img-margin-right;\n      }\n\n      img.note-float-right {\n        margin-left: $img-margin-left;\n      }\n    }\n  }\n}\n\n/* Frame mode layout\n ------------------------------------------ */\n.note-editor.note-frame,\n.note-editor.note-airframe {\n  border: 1px solid $border-color;\n\n  // codeview mode\n  &.codeview {\n    .note-editing-area {\n      .note-editable {\n        display: none;\n      }\n      .note-codable {\n        display: block;\n      }\n    }\n  }\n\n  .note-editing-area {\n    overflow: hidden;\n\n    // editable\n    .note-editable {\n      padding: 10px;\n      overflow: auto;\n      word-wrap: break-word;\n\n      &[contenteditable=\"false\"] {\n        background-color: $background-color;\n      }\n    }\n\n    // codeable\n    .note-codable {\n      display: none;\n      width: 100%;\n      padding: 10px;\n      border: none;\n      box-shadow: none;\n      font-family: Menlo, Monaco, monospace, sans-serif;\n      font-size: 14px;\n      color: #ccc;\n      background-color: #222;\n      resize: none;\n      outline: none;\n\n      // override BS2 default style\n      @include box-sizing(border-box);\n      @include rounded(0);\n      margin-bottom: 0;\n    }\n  }\n\n  // fullscreen mode\n  &.fullscreen {\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100% !important;\n    z-index: 1050; // bs3 modal-backdrop: 1030, bs2: 1040\n    .note-resizebar {\n      display: none;\n    }\n  }\n\n  // Notifications\n  .note-status-output {\n    display: block;\n    width: 100%;\n    font-size: 14px;\n    line-height: 1.42857143;\n    height: 20px;\n    margin-bottom: 0;\n    color: #000;\n    border: 0;\n    border-top: 1px solid #e2e2e2;\n  }\n\n  .note-status-output:empty {\n    height: 0;\n    border-top: 0 solid transparent;\n  }\n\n  .note-status-output .pull-right {\n    float: right !important;\n  }\n\n  .note-status-output .text-muted {\n    color: #777;\n  }\n\n  .note-status-output .text-primary {\n    color: #286090;\n  }\n\n  .note-status-output .text-success {\n    color: #3c763d;\n  }\n\n  .note-status-output .text-info {\n    color: #31708f;\n  }\n\n  .note-status-output .text-warning {\n    color: #8a6d3b;\n  }\n\n  .note-status-output .text-danger {\n    color: #a94442;\n  }\n\n  .note-status-output .alert {\n    margin: -7px 0 0 0;\n    padding: 7px 10px 2px 10px;\n    border-radius: 0;\n    color: #000;\n    background-color: #f5f5f5;\n  }\n\n  .note-status-output .alert .note-icon {\n    margin-right: 5px;\n  }\n\n  .note-status-output .alert-success {\n    color: #3c763d !important;\n    background-color: #dff0d8 !important;\n  }\n\n  .note-status-output .alert-info {\n    color: #31708f !important;\n    background-color: #d9edf7 !important;\n  }\n\n  .note-status-output .alert-warning {\n    color: #8a6d3b !important;\n    background-color: #fcf8e3 !important;\n  }\n\n  .note-status-output .alert-danger {\n    color: #a94442 !important;\n    background-color: #f2dede !important;\n  }\n\n  // statusbar\n  .note-statusbar {\n    background-color: $background-color;\n    border-bottom-left-radius: 4px;\n    border-bottom-right-radius: 4px;\n    border-top: 1px solid $border-color;\n    .note-resizebar {\n      padding-top: 1px;\n      height: 9px;\n      width: 100%;\n      cursor: ns-resize;\n      .note-icon-bar {\n        width: 20px;\n        margin: 1px auto;\n        border-top: 1px solid $border-color;\n      }\n    }\n\n    &.locked {\n      .note-resizebar {\n        cursor: default;\n        .note-icon-bar {\n          display: none;\n        }\n      }\n    }\n  }\n  .note-placeholder {\n    padding: 10px;\n  }\n}\n\n.note-editor.note-airframe {\n  border: 0;\n\n  .note-editing-area {\n    .note-editable {\n      padding: 0;\n    }\n  }\n}\n\n\n/* Popover\n ------------------------------------------ */\n.note-popover.popover {\n  display: none;\n  max-width: none;\n\n  .popover-content {\n    a {\n      display: inline-block;\n      max-width: 200px;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap; // for FF\n      vertical-align: middle; // for FF\n    }\n  }\n  .arrow {\n    left: 20px !important;\n  }\n}\n\n/* Popover and Toolbar (Button container)\n ------------------------------------------ */\n.note-toolbar {\n  position: relative;\n}\n\n.note-popover .popover-content, .note-editor .note-toolbar {\n  margin: 0;\n  padding: 0 0 5px 5px;\n\n  & > .note-btn-group {\n    margin-top: 5px;\n    margin-left: 0;\n    margin-right: 5px;\n  }\n\n  .note-btn-group {\n    .note-table {\n      min-width: 0;\n      padding: 5px;\n      .note-dimension-picker {\n        font-size: 18px;\n        .note-dimension-picker-mousecatcher {\n          position: absolute !important;\n          z-index: 3;\n          width: 10em;\n          height: 10em;\n          cursor: pointer;\n        }\n        .note-dimension-picker-unhighlighted {\n          position: relative !important;\n          z-index: 1;\n          width: 5em;\n          height: 5em;\n          background: url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIj4+Pjp6ekKlAqjAAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKhmnaJzPAAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC\") repeat;\n        }\n        .note-dimension-picker-highlighted {\n          position: absolute !important;\n          z-index: 2;\n          width: 1em;\n          height: 1em;\n          background: url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIjd6vvD2f9LKLW+AAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKwNDEVT0AAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC\") repeat;\n        }\n      }\n    }\n  }\n\n  .note-style {\n    .dropdown-style {\n      blockquote, pre {\n        margin: 0;\n        padding: 5px 10px;\n      }\n      h1, h2, h3, h4, h5, h6, p {\n        margin: 0;\n        padding: 0;\n      }\n    }\n  }\n\n  .note-color-all {\n    .note-dropdown-menu {\n      min-width: 337px;\n    }\n  }\n\n  .note-color {\n    .dropdown-toggle {\n      width: 20px;\n      padding-left: 5px;\n    }\n    .note-dropdown-menu {\n      .note-palette {\n        display: inline-block;\n        margin: 0;\n        width: 160px;\n        &:first-child {\n          margin: 0 5px;\n        }\n\n        .note-palette-title {\n          font-size: 12px;\n          margin: 2px 7px;\n          text-align: center;\n          border-bottom: 1px solid #eee;\n        }\n\n        .note-color-reset,\n        .note-color-select {\n          font-size: 11px;\n          margin: 3px;\n          padding: 0 3px;\n          cursor: pointer;\n          width: 100%;\n          @include rounded(5px);\n\n          &:hover {\n            background: #eee;\n          }\n        }\n\n        .note-color-row {\n          height: 20px;\n        }\n\n        .note-color-select-btn {\n          display: none;\n        }\n\n        .note-holder-custom {\n          .note-color-btn {\n            border: 1px solid #eee;\n          }\n        }\n      }\n    }\n  }\n\n  .note-para {\n    .note-dropdown-menu {\n      min-width: 228px;\n      padding: 5px;\n      & > div + div {\n        margin-left: 5px;\n      }\n    }\n  }\n\n  // dropdown-menu for toolbar and popover\n  .note-dropdown-menu {\n    min-width: 160px;\n\n    // dropdown-menu right position\n    // http://forrst.com/posts/Bootstrap_right_positioned_dropdown-2KB\n    &.right {\n      right: 0;\n      left: auto;\n      &::before {\n        right: 9px;\n        left: auto !important;\n      }\n      &::after {\n        right: 10px;\n        left: auto !important;\n      }\n    }\n    // dropdown-menu for selectbox\n    &.note-check {\n      a i {\n        color: deepskyblue;\n        visibility: hidden;\n      }\n      a.checked i {\n        visibility: visible;\n      }\n    }\n  }\n\n  .note-fontsize-10 {\n    font-size: 10px;\n  }\n\n  // color palette for toolbar and popover\n  .note-color-palette {\n    line-height: 1;\n    div {\n      .note-color-btn {\n        width: 20px;\n        height: 20px;\n        padding: 0;\n        margin: 0;\n        border: 0;\n        border-radius: 0;\n      }\n      .note-color-btn:hover {\n        transform: scale(1.2);\n        transition: all 0.2s;\n      }\n    }\n  }\n}\n\n/* Dialog\n ------------------------------------------ */\n.note-modal {\n  .modal-dialog {\n    outline: 0;\n    border-radius: 5px;\n    @include box-shadow(0 3px 9px rgba(0,0,0,.5));\n  }\n  .form-group { // overwrite BS's form-horizontal minus margins\n    margin-left: 0;\n    margin-right: 0;\n  }\n  .note-modal-form {\n    margin: 0; // overwrite BS2's form margin bottom\n  }\n  .note-image-dialog {\n    .note-dropzone {\n      min-height: 100px;\n      font-size: 30px;\n      line-height: 4; // vertical-align\n      color: lightgray;\n      text-align: center;\n      border: 4px dashed lightgray;\n      margin-bottom: 10px;\n    }\n  }\n\n  // [workaround] firefox fileinput\n  @-moz-document url-prefix() {\n    .note-image-input {\n      height: auto;\n    }\n  }\n}\n\n/* Placeholder\n ------------------------------------------ */\n.note-placeholder {\n  position: absolute;\n  display: none;\n  color: gray;\n}\n\n/* Handle\n ------------------------------------------ */\n.note-handle {\n  // control selection\n  .note-control-selection {\n    position: absolute;\n    display: none;\n    border: 1px solid #000;\n    & > div {\n      position: absolute;\n    }\n\n    .note-control-selection-bg {\n      width: 100%;\n      height: 100%;\n      background-color: #000;\n      @include opacity(0.3);\n    }\n\n    .note-control-handle {\n      width: 7px;\n      height: 7px;\n      border: 1px solid #000;\n    }\n\n    .note-control-holder {\n      @extend .note-control-handle;\n    }\n\n    .note-control-sizing {\n      @extend .note-control-handle;\n      background-color: #000;\n    }\n\n    .note-control-nw {\n      top: -5px;\n      left: -5px;\n      border-right: none;\n      border-bottom: none;\n    }\n\n    .note-control-ne {\n      top: -5px;\n      right: -5px;\n      border-bottom: none;\n      border-left: none;\n    }\n\n    .note-control-sw {\n      bottom: -5px;\n      left: -5px;\n      border-top: none;\n      border-right: none;\n    }\n\n    .note-control-se {\n      right: -5px;\n      bottom: -5px;\n      cursor: se-resize;\n    }\n\n    .note-control-se.note-control-holder {\n      cursor: default;\n      border-top: none;\n      border-left: none;\n    }\n\n    .note-control-selection-info {\n      right: 0;\n      bottom: 0;\n      padding: 5px;\n      margin: 5px;\n      color: #fff;\n      background-color: #000;\n      font-size: 12px;\n      @include rounded(5px);\n      @include opacity(0.7);\n    }\n  }\n}\n\n.note-hint-popover {\n  min-width: 100px;\n  padding: 2px;\n\n  .popover-content {\n    padding: 3px;\n    max-height: 150px;\n    overflow: auto;\n\n    .note-hint-group {\n      .note-hint-item {\n        display: block !important;\n        padding: 3px;\n\n        &.active, &:hover {\n          display: block;\n          clear: both;\n          font-weight: 400;\n          line-height: 1.4;\n          color: white;\n          white-space: nowrap;\n          text-decoration: none;\n          background-color: #428bca;\n          outline: 0;\n          cursor: pointer;\n        }\n      }\n    }\n  }\n}\n\n/* Handle\n ------------------------------------------ */\nhtml, body {\n  .note-fullscreen-body {\n    overflow: hidden !important;\n  }\n}\n"], "names": [], "sourceRoot": ""}