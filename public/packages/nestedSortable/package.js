Package.describe({name:"ilikenwf:nested-sortable",version:"0.0.1",summary:"A jQuery plugin that extends Sortable UI functionalities to nested lists.",git:"https://github.com/ilikenwf/nestedSortable",documentation:"README.md"}),Package.onUse((function(e){e.versionsFrom("1.1.0.2"),e.use("jquery","client"),e.use("mizzao:jquery-ui","client"),e.imply("jquery","client"),e.addFiles("jquery.mjs.nestedSortable.js","client")})),Package.onTest((function(e){e.use("tinytest"),e.use("ilikenwf:nested-sortable"),e.addFiles("meteor/nested-sortable-tests.js")}));
