!function(t,e,i){function n(i,n,o){var r=e.createElement(i);return n&&(r.id=Y+n),o&&(r.style.cssText=o),t(r)}function o(){return i.innerHeight?i.innerHeight:t(i).height()}function r(e,i){i!==Object(i)&&(i={}),this.cache={},this.el=e,this.value=function(e){var n;return void 0===this.cache[e]&&(void 0!==(n=t(this.el).attr("data-cbox-"+e))?this.cache[e]=n:void 0!==i[e]?this.cache[e]=i[e]:void 0!==V[e]&&(this.cache[e]=V[e])),this.cache[e]},this.get=function(e){var i=this.value(e);return t.isFunction(i)?i.call(this.el,this):i}}function h(t){var e=k.length,i=(z+t)%e;return 0>i?e+i:i}function a(t,e){return Math.round((/%/.test(t)?("x"===e?W.width():o())/100:1)*parseInt(t,10))}function s(t,e){return t.get("photo")||t.get("photoRegex").test(e)}function l(t,e){return t.get("retinaUrl")&&i.devicePixelRatio>1?e.replace(t.get("photoRegex"),t.get("retinaSuffix")):e}function d(t){"contains"in v[0]&&!v[0].contains(t.target)&&t.target!==w[0]&&(t.stopPropagation(),v.focus())}function c(t){c.str!==t&&(v.add(w).removeClass(c.str).addClass(t),c.str=t)}function g(i){t(e).trigger(i),ht.triggerHandler(i)}function u(i){var o;if(!$){if(o=t(i).data(X),function(e){z=0,e&&!1!==e&&"nofollow"!==e?(k=t("."+Z).filter((function(){return new r(this,t.data(this,X)).get("rel")===e})),-1===(z=k.index(O.el))&&(k=k.add(O.el),z=k.length-1)):k=t(O.el)}((O=new r(i,o)).get("rel")),!q){q=U=!0,c(O.get("className")),v.css({visibility:"hidden",display:"block",opacity:""}),E=n(at,"LoadedContent","width:0; height:0; overflow:hidden; visibility:hidden"),y.css({width:"",height:""}).append(E),_=b.height()+H.height()+y.outerHeight(!0)-y.height(),j=T.width()+C.width()+y.outerWidth(!0)-y.width(),D=E.outerHeight(!0),N=E.outerWidth(!0);var h=a(O.get("initialWidth"),"x"),s=a(O.get("initialHeight"),"y"),l=O.get("maxWidth"),u=O.get("maxHeight");O.w=Math.max((!1!==l?Math.min(h,a(l,"x")):h)-N-j,0),O.h=Math.max((!1!==u?Math.min(s,a(u,"y")):s)-D-_,0),E.css({width:"",height:O.h}),Q.position(),g(tt),O.get("onOpen"),B.add(L).hide(),v.focus(),O.get("trapFocus")&&e.addEventListener&&(e.addEventListener("focus",d,!0),ht.one(ot,(function(){e.removeEventListener("focus",d,!0)}))),O.get("returnFocus")&&ht.one(ot,(function(){t(O.el).focus()}))}var f=parseFloat(O.get("opacity"));w.css({opacity:f==f?f:"",cursor:O.get("overlayClose")?"pointer":"",visibility:"visible"}).show(),O.get("closeButton")?P.html(O.get("close")).appendTo(y):P.appendTo("<div/>"),m()}}function f(){v||(J=!1,W=t(i),v=n(at).attr({id:X,class:!1===t.support.opacity?Y+"IE":"",role:"dialog",tabindex:"-1"}).hide(),w=n(at,"Overlay").hide(),M=t([n(at,"LoadingOverlay")[0],n(at,"LoadingGraphic")[0]]),x=n(at,"Wrapper"),y=n(at,"Content").append(L=n(at,"Title"),F=n(at,"Current"),K=t('<button type="button"/>').attr({id:Y+"Previous"}),S=t('<button type="button"/>').attr({id:Y+"Next"}),R=t('<button type="button"/>').attr({id:Y+"Slideshow"}),M),P=t('<button type="button"/>').attr({id:Y+"Close"}),x.append(n(at).append(n(at,"TopLeft"),b=n(at,"TopCenter"),n(at,"TopRight")),n(at,!1,"clear:left").append(T=n(at,"MiddleLeft"),y,C=n(at,"MiddleRight")),n(at,!1,"clear:left").append(n(at,"BottomLeft"),H=n(at,"BottomCenter"),n(at,"BottomRight"))).find("div div").css({float:"left"}),I=n(at,!1,"position:absolute; width:9999px; visibility:hidden; display:none; max-width:none;"),B=S.add(K).add(F).add(R)),e.body&&!v.parent().length&&t(e.body).append(w,v.append(x,I))}function p(){function i(t){t.which>1||t.shiftKey||t.altKey||t.metaKey||t.ctrlKey||(t.preventDefault(),u(this))}return!!v&&(J||(J=!0,S.click((function(){Q.next()})),K.click((function(){Q.prev()})),P.click((function(){Q.close()})),w.click((function(){O.get("overlayClose")&&Q.close()})),t(e).bind("keydown."+Y,(function(t){var e=t.keyCode;q&&O.get("escKey")&&27===e&&(t.preventDefault(),Q.close()),q&&O.get("arrowKey")&&k[1]&&!t.altKey&&(37===e?(t.preventDefault(),K.click()):39===e&&(t.preventDefault(),S.click()))})),t.isFunction(t.fn.on)?t(e).on("click."+Y,"."+Z,i):t("."+Z).live("click."+Y,i)),!0)}function m(){var e,o,r,h=Q.prep,d=++st;if(U=!0,A=!1,g(rt),g(et),O.get("onLoad"),O.h=O.get("height")?a(O.get("height"),"y")-D-_:O.get("innerHeight")&&a(O.get("innerHeight"),"y"),O.w=O.get("width")?a(O.get("width"),"x")-N-j:O.get("innerWidth")&&a(O.get("innerWidth"),"x"),O.mw=O.w,O.mh=O.h,O.get("maxWidth")&&(O.mw=a(O.get("maxWidth"),"x")-N-j,O.mw=O.w&&O.w<O.mw?O.w:O.mw),O.get("maxHeight")&&(O.mh=a(O.get("maxHeight"),"y")-D-_,O.mh=O.h&&O.h<O.mh?O.h:O.mh),e=O.get("href"),G=setTimeout((function(){M.show()}),100),O.get("inline")){var c=t(e).eq(0);r=t("<div>").hide().insertBefore(c),ht.one(rt,(function(){r.replaceWith(c)})),h(c)}else O.get("iframe")?h(" "):O.get("html")?h(O.get("html")):s(O,e)?(e=l(O,e),A=O.get("createImg"),t(A).addClass(Y+"Photo").bind("error."+Y,(function(){h(n(at,"Error").html(O.get("imgError")))})).one("load",(function(){d===st&&setTimeout((function(){var e;O.get("retinaImage")&&i.devicePixelRatio>1&&(A.height=A.height/i.devicePixelRatio,A.width=A.width/i.devicePixelRatio),O.get("scalePhotos")&&(o=function(){A.height-=A.height*e,A.width-=A.width*e},O.mw&&A.width>O.mw&&(e=(A.width-O.mw)/A.width,o()),O.mh&&A.height>O.mh&&(e=(A.height-O.mh)/A.height,o())),O.h&&(A.style.marginTop=Math.max(O.mh-A.height,0)/2+"px"),k[1]&&(O.get("loop")||k[z+1])&&(A.style.cursor="pointer",t(A).bind("click."+Y,(function(){Q.next()}))),A.style.width=A.width+"px",A.style.height=A.height+"px",h(A)}),1)})),A.src=e):e&&I.load(e,O.get("data"),(function(e,i){d===st&&h("error"===i?n(at,"Error").html(O.get("xhrError")):t(this).contents())}))}var w,v,x,y,b,T,C,H,k,W,E,I,M,L,F,R,S,K,P,B,O,_,j,D,N,z,A,q,U,$,G,Q,J,V={html:!1,photo:!1,iframe:!1,inline:!1,transition:"elastic",speed:300,fadeOut:300,width:!1,initialWidth:"600",innerWidth:!1,maxWidth:!1,height:!1,initialHeight:"450",innerHeight:!1,maxHeight:!1,scalePhotos:!0,scrolling:!0,opacity:.9,preloading:!0,className:!1,overlayClose:!0,escKey:!0,arrowKey:!0,top:!1,bottom:!1,left:!1,right:!1,fixed:!1,data:void 0,closeButton:!0,fastIframe:!0,open:!1,reposition:!0,loop:!0,slideshow:!1,slideshowAuto:!0,slideshowSpeed:2500,slideshowStart:"start slideshow",slideshowStop:"stop slideshow",photoRegex:/\.(gif|png|jp(e|g|eg)|bmp|ico|webp|jxr|svg)((#|\?).*)?$/i,retinaImage:!1,retinaUrl:!1,retinaSuffix:"@2x.$1",current:"image {current} of {total}",previous:"previous",next:"next",close:"close",xhrError:"This content failed to load.",imgError:"This image failed to load.",returnFocus:!0,trapFocus:!0,onOpen:!1,onLoad:!1,onComplete:!1,onCleanup:!1,onClosed:!1,rel:function(){return this.rel},href:function(){return t(this).attr("href")},title:function(){return this.title},createImg:function(){var e=new Image,i=t(this).data("cbox-img-attrs");return"object"==typeof i&&t.each(i,(function(t,i){e[t]=i})),e},createIframe:function(){var i=e.createElement("iframe"),n=t(this).data("cbox-iframe-attrs");return"object"==typeof n&&t.each(n,(function(t,e){i[t]=e})),"frameBorder"in i&&(i.frameBorder=0),"allowTransparency"in i&&(i.allowTransparency="true"),i.name=(new Date).getTime(),i.allowFullscreen=!0,i}},X="colorbox",Y="cbox",Z=Y+"Element",tt=Y+"_open",et=Y+"_load",it=Y+"_complete",nt=Y+"_cleanup",ot=Y+"_closed",rt=Y+"_purge",ht=t("<a/>"),at="div",st=0,lt={},dt=function(){function t(){clearTimeout(h)}function e(){(O.get("loop")||k[z+1])&&(t(),h=setTimeout(Q.next,O.get("slideshowSpeed")))}function i(){R.html(O.get("slideshowStop")).unbind(s).one(s,n),ht.bind(it,e).bind(et,t),v.removeClass(a+"off").addClass(a+"on")}function n(){t(),ht.unbind(it,e).unbind(et,t),R.html(O.get("slideshowStart")).unbind(s).one(s,(function(){Q.next(),i()})),v.removeClass(a+"on").addClass(a+"off")}function o(){r=!1,R.hide(),t(),ht.unbind(it,e).unbind(et,t),v.removeClass(a+"off "+a+"on")}var r,h,a=Y+"Slideshow_",s="click."+Y;return function(){r?O.get("slideshow")||(ht.unbind(nt,o),o()):O.get("slideshow")&&k[1]&&(r=!0,ht.one(nt,o),O.get("slideshowAuto")?i():n(),R.show())}}();t[X]||(t(f),Q=t.fn[X]=t[X]=function(e,i){var n=this;return e=e||{},t.isFunction(n)&&(n=t("<a/>"),e.open=!0),n[0]?(f(),p()&&(i&&(e.onComplete=i),n.each((function(){var i=t.data(this,X)||{};t.data(this,X,t.extend(i,e))})).addClass(Z),new r(n[0],e).get("open")&&u(n[0])),n):n},Q.position=function(e,i){function n(){b[0].style.width=H[0].style.width=y[0].style.width=parseInt(v[0].style.width,10)-j+"px",y[0].style.height=T[0].style.height=C[0].style.height=parseInt(v[0].style.height,10)-_+"px"}var r,h,s,l=0,d=0,c=v.offset();if(W.unbind("resize."+Y),v.css({top:-9e4,left:-9e4}),h=W.scrollTop(),s=W.scrollLeft(),O.get("fixed")?(c.top-=h,c.left-=s,v.css({position:"fixed"})):(l=h,d=s,v.css({position:"absolute"})),d+=!1!==O.get("right")?Math.max(W.width()-O.w-N-j-a(O.get("right"),"x"),0):!1!==O.get("left")?a(O.get("left"),"x"):Math.round(Math.max(W.width()-O.w-N-j,0)/2),l+=!1!==O.get("bottom")?Math.max(o()-O.h-D-_-a(O.get("bottom"),"y"),0):!1!==O.get("top")?a(O.get("top"),"y"):Math.round(Math.max(o()-O.h-D-_,0)/2),v.css({top:c.top,left:c.left,visibility:"visible"}),x[0].style.width=x[0].style.height="9999px",r={width:O.w+N+j,height:O.h+D+_,top:l,left:d},e){var g=0;t.each(r,(function(t){return r[t]!==lt[t]?void(g=e):void 0})),e=g}lt=r,e||v.css(r),v.dequeue().animate(r,{duration:e||0,complete:function(){n(),U=!1,x[0].style.width=O.w+N+j+"px",x[0].style.height=O.h+D+_+"px",O.get("reposition")&&setTimeout((function(){W.bind("resize."+Y,Q.position)}),1),t.isFunction(i)&&i()},step:n})},Q.resize=function(t){var e;q&&((t=t||{}).width&&(O.w=a(t.width,"x")-N-j),t.innerWidth&&(O.w=a(t.innerWidth,"x")),E.css({width:O.w}),t.height&&(O.h=a(t.height,"y")-D-_),t.innerHeight&&(O.h=a(t.innerHeight,"y")),t.innerHeight||t.height||(e=E.scrollTop(),E.css({height:"auto"}),O.h=E.height()),E.css({height:O.h}),e&&E.scrollTop(e),Q.position("none"===O.get("transition")?0:O.get("speed")))},Q.prep=function(i){if(q){var o,a="none"===O.get("transition")?0:O.get("speed");E.remove(),(E=n(at,"LoadedContent").append(i)).hide().appendTo(I.show()).css({width:(O.w=O.w||E.width(),O.w=O.mw&&O.mw<O.w?O.mw:O.w,O.w),overflow:O.get("scrolling")?"auto":"hidden"}).css({height:(O.h=O.h||E.height(),O.h=O.mh&&O.mh<O.h?O.mh:O.h,O.h)}).prependTo(y),I.hide(),t(A).css({float:"none"}),c(O.get("className")),o=function(){function i(){!1===t.support.opacity&&v[0].style.removeAttribute("filter")}var n,o,d=k.length;q&&(o=function(){clearTimeout(G),M.hide(),g(it),O.get("onComplete")},L.html(O.get("title")).show(),E.show(),d>1?("string"==typeof O.get("current")&&F.html(O.get("current").replace("{current}",z+1).replace("{total}",d)).show(),S[O.get("loop")||d-1>z?"show":"hide"]().html(O.get("next")),K[O.get("loop")||z?"show":"hide"]().html(O.get("previous")),dt(),O.get("preloading")&&t.each([h(-1),h(1)],(function(){var i=k[this],n=new r(i,t.data(i,X)),o=n.get("href");o&&s(n,o)&&(o=l(n,o),e.createElement("img").src=o)}))):B.hide(),O.get("iframe")?(n=O.get("createIframe"),O.get("scrolling")||(n.scrolling="no"),t(n).attr({src:O.get("href"),class:Y+"Iframe"}).one("load",o).appendTo(E),ht.one(rt,(function(){n.src="//about:blank"})),O.get("fastIframe")&&t(n).trigger("load")):o(),"fade"===O.get("transition")?v.fadeTo(a,1,i):i())},"fade"===O.get("transition")?v.fadeTo(a,0,(function(){Q.position(0,o)})):Q.position(a,o)}},Q.next=function(){!U&&k[1]&&(O.get("loop")||k[z+1])&&(z=h(1),u(k[z]))},Q.prev=function(){!U&&k[1]&&(O.get("loop")||z)&&(z=h(-1),u(k[z]))},Q.close=function(){q&&!$&&($=!0,q=!1,g(nt),O.get("onCleanup"),W.unbind("."+Y),w.fadeTo(O.get("fadeOut")||0,0),v.stop().fadeTo(O.get("fadeOut")||0,0,(function(){v.hide(),w.hide(),g(rt),E.remove(),setTimeout((function(){$=!1,g(ot),O.get("onClosed")}),1)})))},Q.remove=function(){v&&(v.stop(),t[X].close(),v.stop(!1,!0).remove(),w.remove(),$=!1,v=null,t("."+Z).removeData(X).removeClass(Z),t(e).unbind("click."+Y).unbind("keydown."+Y))},Q.element=function(){return t(O.el)},Q.settings=V)}(jQuery,document,window);
