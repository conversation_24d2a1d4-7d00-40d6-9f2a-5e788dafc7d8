<?php

use App\Http\Controllers\Admin\MovieCrudController;
use App\Http\Controllers\Admin\MovieIndexController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\ReplaceDomainController;

// --------------------------
// Custom Backpack Routes
// --------------------------
// This route file is loaded automatically by Backpack\Base.
// Routes you generate using Backpack\Generators will be placed here.

Route::group([
    'prefix' => config('backpack.base.route_prefix', 'admin'),
    'middleware' => array_merge(
        (array)config('backpack.base.web_middleware', 'web'),
        (array)config('backpack.base.middleware_key', 'admin')
    ),
    'namespace' => 'App\Http\Controllers\Admin',
], function () {

    Route::post('movie/gen-content', [MovieCrudController::class, 'genContentMovie']);
    Route::post('movie/gen-content/all', [MovieCrudController::class, 'genContentForAllMovie']);

    Route::get('movie/index-url', [MovieIndexController::class, 'index'])->name('movie.url.index');
    Route::post('movie/index-url/create', [MovieIndexController::class, 'create'])->name('movie.url.create');

    Route::get('movie/replace-url', [ReplaceDomainController::class, 'index'])->name('movie.replace-url.index');
    Route::post('movie/replace-url/action', [ReplaceDomainController::class, 'action'])->name('movie.replace-url.action');
}); // this should be the absolute last line of this file
Route::group([
    'prefix' => config('backpack.base.route_prefix', 'admin'),
    'middleware' => array_merge(
        (array)config('backpack.base.web_middleware', 'web'),
        (array)config('backpack.base.middleware_key', 'admin')
    ),
    'namespace' => 'App\Http\Controllers\NguoncCrawler',
], function () {
    Route::get('plugin/nguonc-crawler', 'NguonCCrawlController@showCrawlPage');
    Route::get('plugin/nguonc-crawler/fetch', 'NguonCCrawlController@fetch');
    Route::post('plugin/nguonc-crawler/crawl', 'NguonCCrawlController@crawl');
    Route::post('plugin/nguonc-crawler/get-movies', 'NguonCCrawlController@getMoviesFromParams');
});
