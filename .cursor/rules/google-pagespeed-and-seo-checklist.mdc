---
description: 
globs: 
alwaysApply: true
---
# Google PageSpeed & SEO Checklist - Rules for AI Development

## 🚀 Google PageSpeed Optimization Rules

### Core Web Vitals Requirements

#### Largest Contentful Paint (LCP)
- **Target**: ≤ 2.5 seconds for good performance [1][2]
- **Action**: Optimize images, prioritize above-the-fold content, implement caching strategies [3]
- **Rule**: Always compress images and use modern formats (WebP, AVIF) [4][5]
- **Requirement**: Preload critical resources and optimize server response time [6][7]

#### Interaction to Next Paint (INP)
- **Target**: ≤ 200 milliseconds for optimal responsiveness [8][2]
- **Action**: Minimize JavaScript execution time and optimize long tasks [7]
- **Rule**: Defer non-essential JavaScript and implement code splitting [3]
- **Requirement**: Use browser preloading for anticipated user interactions [3]

#### Cumulative Layout Shift (CLS)
- **Target**: ≤ 0.1 for stable visual experience [1][2]
- **Action**: Specify image and video dimensions to prevent layout shifts [3]
- **Rule**: Reserve space for dynamically loaded content [3]
- **Requirement**: Optimize Critical CSS loading to prevent unexpected shifts [3]

### Performance Optimization Rules

#### Image Optimization
- **Must**: Use descriptive filenames with target keywords [4][5]
- **Must**: Add meaningful alt text for all images [4][5]
- **Must**: Compress images without quality loss [6][9]
- **Must**: Implement lazy loading for non-critical images [8]
- **Rule**: Use appropriate image formats (WebP for modern browsers) [5]

#### JavaScript & CSS Optimization
- **Must**: Minify all CSS and JavaScript files [6][10]
- **Must**: Remove unused code and dependencies [7]
- **Must**: Implement browser caching for static resources [6][10]
- **Rule**: Avoid render-blocking JavaScript in critical path [6][10]
- **Action**: Use CSS media queries for responsive loading [9]

#### Server & Network Optimization
- **Must**: Enable compression (Gzip/Brotli) [6][10]
- **Must**: Leverage browser caching effectively [6][10]
- **Must**: Minimize server response time [6][10]
- **Rule**: Avoid landing page redirects [6][10]
- **Action**: Use Content Delivery Network (CDN) for global performance [9]

## 🎯 Google SEO Optimization Rules

### Technical SEO Requirements

#### Crawling & Indexing
- **Must**: Ensure Google can find and index all pages [11]
- **Must**: Create and submit XML sitemap [12][13]
- **Must**: Use robots.txt appropriately [13]
- **Rule**: Monitor indexing status via Google Search Console [12][11]
- **Action**: Fix crawlability issues immediately [11]

#### URL Structure
- **Must**: Use descriptive, keyword-rich URLs [14][15]
- **Must**: Implement clean URL structure without parameters [15]
- **Rule**: Keep URLs under 60 characters when possible [15]
- **Action**: Use hyphens to separate words in URLs [14]

### On-Page SEO Rules

#### Title Tags
- **Must**: Write unique title tags for each page [15][16]
- **Must**: Keep titles between 50-60 characters [17][16]
- **Must**: Include primary keyword near the beginning [15][17]
- **Rule**: Make titles descriptive and click-worthy [17][16]
- **Action**: Avoid keyword stuffing in titles [15][17]

#### Meta Descriptions
- **Must**: Write unique meta descriptions for each page [18][17]
- **Must**: Keep descriptions between 150-160 characters [17][16]
- **Must**: Include primary and secondary keywords naturally [17]
- **Rule**: Use action-oriented language to encourage clicks [17]
- **Action**: Make descriptions compelling and relevant [18][17]

#### Header Structure
- **Must**: Use proper heading hierarchy (H1, H2, H3) [18][15]
- **Must**: Include only one H1 per page [18][19]
- **Must**: Use keywords in headings appropriately [14][15]
- **Rule**: Make headings descriptive and logical [18][19]
- **Action**: Don't skip heading levels [19]

#### Content Optimization
- **Must**: Create original, high-quality content [15][20]
- **Must**: Use primary keyword in first 150 words [14][15]
- **Must**: Write for user intent, not just keywords [21][15]
- **Rule**: Maintain appropriate keyword density without stuffing [15][20]
- **Action**: Update content regularly to keep it fresh [13]

### Structured Data & Schema
- **Must**: Implement relevant schema markup [22]
- **Must**: Use Schema.org vocabulary [22]
- **Rule**: Test structured data with Google's tools [22]
- **Action**: Implement schema for products, articles, and local business [22]

### Mobile SEO Requirements
- **Must**: Ensure mobile-first responsive design [23]
- **Must**: Optimize for mobile page speed [23]
- **Must**: Use appropriate font sizes for mobile [23]
- **Rule**: Test mobile usability regularly [23]
- **Action**: Optimize touch elements for mobile interaction [23]

### Link Optimization
- **Must**: Use descriptive anchor text for internal links [18][15]
- **Must**: Implement internal linking strategy [15][20]
- **Rule**: Link to relevant, authoritative external sources [18]
- **Action**: Use nofollow for untrusted external links [18]

## 🔧 Accessibility & User Experience Rules

### Web Accessibility Standards
- **Must**: Provide alt text for all meaningful images [24][19]
- **Must**: Ensure sufficient color contrast (4.5:1 ratio) [24][25]
- **Must**: Enable keyboard navigation for all interactive elements [24][26]
- **Rule**: Use semantic HTML elements properly [19][27]
- **Action**: Test with screen readers and accessibility tools [24][26]

### Form Optimization
- **Must**: Label all form inputs clearly [19][27]
- **Must**: Provide clear error messages and validation [27]
- **Rule**: Use appropriate input types (email, tel, date) [27]
- **Action**: Implement form accessibility features [19][27]

## 📊 Monitoring & Testing Rules

### Performance Monitoring
- **Must**: Use Google PageSpeed Insights regularly [28][29]
- **Must**: Monitor Core Web Vitals in Google Search Console [8][2]
- **Must**: Test on multiple devices and connections [29][9]
- **Rule**: Set up continuous performance monitoring [3]
- **Action**: Track performance metrics over time [29][9]

### SEO Monitoring
- **Must**: Monitor rankings and traffic in Google Search Console [12][13]
- **Must**: Track keyword performance regularly [21][11]
- **Must**: Monitor for technical SEO issues [11][8]
- **Rule**: Conduct regular SEO audits [13][8]
- **Action**: Fix issues promptly when identified [11][8]

## ⚡ Implementation Priority

### High Priority (Immediate Action)
1. Fix Core Web Vitals issues [1][2]
2. Optimize title tags and meta descriptions [17][16]
3. Ensure mobile responsiveness [23]
4. Implement proper heading structure [18][19]
5. Add alt text to all images [4][24]

### Medium Priority (Within 2 Weeks)
1. Optimize images and implement lazy loading [3][5]
2. Clean up JavaScript and CSS [7][9]
3. Implement structured data [22]
4. Improve internal linking [15][20]
5. Set up Google Search Console monitoring [12][13]

### Low Priority (Ongoing Optimization)
1. Enhance accessibility features [24][26]
2. Optimize for voice search [8]
3. Improve content quality and freshness [13][15]
4. Build quality backlinks [15]
5. Monitor and iterate based on performance data [29][3]

---


**Note**: This checklist must be followed for every website development and optimization task to ensure compliance with Google's PageSpeed and SEO requirements. Regular audits and updates are essential for maintaining optimal performance.