---
description: 
globs: 
alwaysApply: true
---
---
description:
globs:
alwaysApply: true
---
# OphimCMS Theme Development Rules

## Project Structure
```
theme-name/
├── resources/
│   ├── views/
│   │   ├── layouts/
│   │   │   ├── app.blade.php
│   │   │   ├── admin.blade.php
│   │   │   └── components/
│   │   ├── pages/
│   │   │   ├── home.blade.php
│   │   │   ├── movie/
│   │   │   │   ├── show.blade.php
│   │   │   │   └── player.blade.php
│   │   │   ├── category/
│   │   │   │   └── show.blade.php
│   │   │   ├── region/
│   │   │   │   └── show.blade.php
│   │   │   ├── actor/
│   │   │   │   └── show.blade.php
│   │   │   ├── director/
│   │   │   │   └── show.blade.php
│   │   │   ├── tag/
│   │   │   │   └── show.blade.php
│   │   │   └── search.blade.php
│   │   └── components/
│   │       ├── movie-card.blade.php
│   │       ├── pagination.blade.php
│   │       └── player/
│   │           ├── default.blade.php
│   │           └── embed.blade.php
│   ├── assets/
│   │   ├── css/
│   │   │   ├── app.css
│   │   │   └── admin.css
│   │   ├── js/
│   │   │   ├── app.js
│   │   │   └── admin.js
│   │   └── images/
│   └── lang/
│       ├── en/
│       └── vi/
├── routes/
│   └── web.php
├── src/
│   ├── Config/
│   │   └── theme.php
│   ├── Controllers/
│   │   ├── HomeController.php
│   │   ├── MovieController.php
│   │   ├── CategoryController.php
│   │   ├── RegionController.php
│   │   ├── ActorController.php
│   │   ├── DirectorController.php
│   │   ├── TagController.php
│   │   └── SearchController.php
│   ├── Models/
│   │   └── Theme.php
│   └── ThemeNameServiceProvider.php
└── composer.json
```

## Required Dependencies
```json
{
    "require": {
        "laravel/framework": "^6|^7|^8",
        "ckfinder/ckfinder-laravel-package": "v3.5.2.1",
        "hacoidev/ophim-core": "^1.0.0",
        "jenssegers/agent": "^2.6"
    }
}
```

## Development Rules

### 1. Namespace Convention
- Use namespace: `Ophim\ThemeName\`
- Service Provider: `Ophim\ThemeName\ThemeNameServiceProvider`
- Models should extend core models when needed

### 2. Required Components
- **Service Provider**: Must register routes, views, and assets
- **Controllers**: Must extend core controllers or implement required interfaces
- **Views**: Must include all required blade templates
- **Config**: Theme-specific configuration files

### 3. View Structure
- Must support responsive design
- Include layouts for:
  - Desktop
  - Tablet
  - Mobile
- Required pages:
  - Home
  - Movie detail
  - Movie player
  - Category
  - Region
  - Actor
  - Director
  - Tag
  - Search

### 4. Asset Management
- CSS must be organized by component
- JavaScript should be modular
- Images should be optimized
- Use Laravel Mix for asset compilation
- Must include admin assets

### 5. Routing
- All routes must be prefixed with theme name
- Must include routes for all required pages
- Support for custom routes
- Must not override core routes

### 6. Database Integration
- Use core database schema
- Follow Laravel naming conventions
- No direct database modifications
- Use core models and relationships

### 7. Core Integration
- Must implement core interfaces
- Use core traits where applicable
- Follow core naming conventions
- Use core helpers and utilities
- Implement core events and listeners

### 8. Required Features
- Movie listing and filtering
- Category/Region browsing
- Actor/Director pages
- Tag system
- Search functionality
- Episode player
- Responsive design
- Admin interface integration

### 9. Performance Requirements
- Optimize asset loading
- Implement caching where appropriate
- Follow Laravel best practices
- Use core caching mechanisms

### 10. Security
- Validate all user inputs
- Use Laravel's built-in security features
- Follow OWASP guidelines
- Use core security features

### 11. Documentation
- Include README.md with:
  - Installation instructions
  - Configuration options
  - Customization guide
  - Dependencies list
  - Core integration guide

### 12. Testing
- Write unit tests for custom functionality
- Include feature tests for critical paths
- Document test coverage
- Test core integration

## Build Process
1. Create new theme directory
2. Initialize composer.json
3. Set up basic directory structure
4. Create Service Provider
5. Implement required views
6. Add controllers and routes
7. Configure assets
8. Test all functionality
9. Document the theme
10. Package for distribution

## Best Practices
- Follow PSR-4 autoloading
- Use Laravel's blade templating
- Implement proper error handling
- Write clean, documented code
- Use version control
- Regular updates and maintenance
- Follow core coding standards
- Use core utilities and helpers
- Implement core events
- Use core middleware

## Core Integration Points
- Movie management
- Category management
- Region management
- Actor management
- Director management
- Tag management
- Episode management
- User management
- Settings management
- Theme management
- Search functionality
- Player integration
- Admin interface
- API endpoints
- Events and listeners
- Middleware
- Helpers and utilities

