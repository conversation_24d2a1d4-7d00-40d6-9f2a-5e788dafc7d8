.banner {
    width: 100%;
}

.banner {
    width: calc(100% - (100% - 1808px) / 2);
    margin: 0px 0px 0px calc((100% - 1808px) / 2);
}

/* .app .content .container {
  max-width: unset;
  width:  calc((100% - 1808px) / 2);
  margin: 0px calc((100% - 1808px) / 2);
} */

.banner > .wrap-banner,
.banner > .wrap-banner > .row {
    margin: unset;
    width: 100%;
    align-items: center;
}

.banner .col__left {
    /* margin-top: 25%; */
}

.banner .col__left {
    width: 40%;
    margin-top: 140px;
}


.banner > .wrap-banner, .banner > .wrap-banner > .row {
    position: relative;
}

.banner .col__right {
    position: absolute;
    right: 0;
    top: 0;
    z-index: -1;
}


.banner .col__right {
    width: 60%;
}

.banner-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.banner-content__title > h1 {
    font-size: 40px;
    line-height: 1.2;
    color: rgb(255, 255, 255);
    text-align: left;
    font-weight: 800;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    white-space: normal;
    text-shadow: rgba(0, 0, 0, 0.7) 1px 1px 0px;
}

.banner-info-tag {
    margin: 24px 0;
}

.key {
    color: var(--grey-color);
}

.banner-content a:hover {
    color: var(--main-color);
}

.banner-content__desc {
    position: relative;
}

.banner-content__desc .more-info {
    position: absolute;
    right: 0;
    bottom: 0;
    font-size: 14px;
    font-weight: 600;
    color: rgb(28, 199, 73);
    cursor: pointer;
    text-align: right;
    text-shadow: rgba(0, 0, 0, 0.7) 1px 1px 0px;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    background: rgb(17, 19, 25);
    padding-left: 20px;
}

.banner-content__desc .more-info .text {
    margin-right: 2px;
}

.banner .group-btn {
    margin: 24px 0;
}

.banner .group-btn .btn-item {
    display: inline-block;
    min-width: 80px;
    margin-right: 14px;
    background: rgba(255, 255, 255, 0.2);
    text-align: center;
    border-radius: 4px;
    padding: 0px 16px;
    align-items: center;
    font-weight: 600;
    height: 42px;
    line-height: 42px;
}

.banner .group-btn .btn-item.btn-play {
    background-color: var(--main-color);
}

.banner .group-btn .btn-item.btn-trailer {
    background-color: var(--main-color);
}

.banner .group-btn .btn-item.btn-trailer:hover {
    color: #fff;
    background-color: #f31d1d;
}

.banner .group-btn .btn-item.btn-play:hover {
    color: #fff;
    background-color: rgb(73, 210, 109);
}

.banner .group-btn .btn-item.btn-facebook:hover {
    color: #fff;
    background-color: #4267B2;
}

.banner .group-btn .btn-item.btn-twitter:hover {
    color: #fff;
    background-color: #1DA1F2;
}

.banner .group-btn .btn-item:hover {
    color: #fff;
    background-color: rgb(86, 87, 91);
}

.wrap-banner-img {
    position: relative;
}

.left-layer {
    width: 26%;
    height: 100%;
    background-image: linear-gradient(270deg, rgba(17, 19, 25, 0) 0%, rgba(17, 19, 25, 0.05) 16%, rgba(17, 19, 25, 0.2) 30%, rgba(17, 19, 25, 0.39) 43%, rgba(17, 19, 25, 0.61) 55%, rgba(17, 19, 25, 0.8) 68%, rgba(17, 19, 25, 0.95) 82%, rgb(17, 19, 25) 98%);
    border-radius: 1px;
    z-index: 100;
    position: absolute;
    bottom: 0px;
}

.bottom-layer {
    width: 100%;
    height: 36%;
    background-image: linear-gradient(179deg, rgba(17, 19, 25, 0) 1%, rgba(17, 19, 25, 0.05) 17%, rgba(17, 19, 25, 0.2) 31%, rgba(17, 19, 25, 0.39) 44%, rgba(17, 19, 25, 0.61) 56%, rgba(17, 19, 25, 0.8) 69%, rgba(17, 19, 25, 0.95) 83%, rgb(17, 19, 25) 99%);
    border-radius: 1px;
    z-index: 100;
    position: absolute;
    bottom: 0px;
}

.container {
    padding: 0;
}

.tab-content-ul .nav-link {
    padding: 16px 0;
    color: #fff;
    border-radius: unset;
    margin-right: 30px;
}

.tab-content-ul .nav-link.active {
    background-color: unset;
    border-bottom: 4px solid var(--main-color);
}

.tab-content-ul {
    border-bottom: 1px solid var(--grey-color);
    width: 100%;
}

.episodes-page {
    position: relative;
    margin: 24px 0;
}

.col {
    width: 100%;
}

.video-list-wrapper {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
}

.video-list-wrapper .video-item {
    margin: 0px 8px 36px 8px;
    /* flex-basis: 21%; */
    /* flex-grow: 1; */
    display: block;
    width: calc(100% * (1 / 4) - 16px);
    transition: all 0.3s ease 0s;

}

.video-item-img .desc-img {
    width: 100%;
    height: 100%;

    object-fit: fill;
}

.video-item .video-item-name {
    font-weight: 500;
}

.video-item:hover {
    transform: scale(1.05);
    color: var(--main-color);
}

.video-item:hover .wrap {
    display: block;
}

.video-item-img {
    position: relative;
}

.video-item .wrap {
    position: absolute;
    display: none;
    right: 10px;
    bottom: 16px;
}

.video-item .wrap .play-button,
.firm-related .wrap .play-button {
    width: 32px;
    height: 32px;
}

.video-item-img-layer {
    background-image: linear-gradient(0deg, rgba(10, 12, 15, 0.8) 0%, rgba(10, 12, 15, 0.74) 4%, rgba(10, 12, 15, 0.59) 17%, rgba(10, 12, 15, 0.4) 34%, rgba(10, 12, 15, 0.21) 55%, rgba(10, 12, 15, 0.06) 78%, rgba(10, 12, 15, 0) 100%);
    height: 60px;
    position: absolute;
    left: 0px;
    right: 0px;
    bottom: 0px;
}

.page-tab-content {
    display: inline-flex;
    width: 170px;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #fff;
    border-radius: 2px;
    font-size: 14px;
    cursor: pointer;
}

.page-tab-content .num-episode {
    padding: 6px 12px;
}

.page-tab-content .icon-down {
    padding: 6px 8px;
}

.paginate-eposode-list {
    display: none;
    position: absolute;
    width: 178px;
    bottom: -80px;
    font-size: 14px;
    background: rgb(26, 28, 34);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 4px;
    z-index: 10;
}

.paginate-eposode-tiem .page {
    display: inline-block;
    /* height: 16px; */
    width: 100%;
    font-size: 12px;
    padding: 8px 16px;
    color: var(--grey-color);
}

.paginate-eposode-tiem .page.active {
    color: var(--main-color) !important;
}

.paginate-eposode-tiem:hover .page {
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
}


.firm-propose.video-list-wrapper .video-item {
    width: calc(100% * (1 / 6) - 16px);
    margin: 0px 8px 32px 8px;
}

.firm-propose .video-item-img .desc-img {
    height: 260px;
    object-fit: cover;
}

.firm-propose .video-item .wrap,
.firm-related .wrap {
    position: absolute;
    right: 50%;
    bottom: 50%;
    transform: translateY(50%) translateX(50%);
}

.firm-related .wrap {
    display: none;
}

.firm-propose .video-item-img-layer .update-info-mask {
    font-size: 14px;
    color: rgb(255, 255, 255);
    letter-spacing: 0px;
    font-weight: 500;
    position: absolute;
    left: 8px;
    right: 10px;
    bottom: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.wrap-banner-img {
    height: 600px;
}

.banner .col__right,
.wrap-banner-img {
    transition: all 0.5s;
}


.actor-list-wrapper {
    display: flex;
    flex-wrap: wrap;
}

.actor-item {
    display: flex;
    flex-direction: column;
    width: calc(100% * (1 / 4) - 16px);
    padding: 12px;
    margin: 0px 8px 16px 8px;
    background-color: rgb(26, 28, 34);
}

.actor-item .actor-info {
    display: flex;
    padding-bottom: 12px;
    border-bottom: 1px solid rgb(45, 47, 52);
}

.actor-item .connect-info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 80px;
    padding: 10px 0px 10px 10px;
    width: calc(100% - 80px);
}

.actor-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}

.connect-opera {
    display: flex;
}

.connect-name {
    font-size: 16px;
}

.connect-opera {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: var(--grey-color);
}

.connect-opera .connect-more {
    color: var(--main-color);
}

.firm-by-actor {
    display: flex;
    margin-top: 16px;
}

.firm-related {
    width: 50%;
    transition: all 0.3s;
}

.firm-related:hover {
    transform: scale(1.02);
}

.firm-related:hover .firm-related-title {
    color: var(--main-color);
}

.firm-related:hover .wrap {
    display: block;
}

.firm-related:nth-child(1) {
    margin-right: 4px;
}

.firm-related:nth-child(2) {
    margin-left: 4px;
}

.firm-related-img {
    position: relative;
    width: 100%;
}

.firm-related-img img {
    max-height: 270px;
    width: 100%;
}

.firm-related-img .update-info-mask {
    position: absolute;
    left: 8px;
    right: 10px;
    bottom: 10px;
    font-size: 14px;
}

.actor-list-wrapper .firm-related-title {
    padding-top: 10px;
}

@media screen and (max-width: 1920px) {
    .banner {
        width: 100%;
        width: calc(100% - 56px);
        margin: 0px 0px 0px 56px;
    }

    .app .content .container {
        /* width: calc(100% - 112px); */
        max-width: unset;
        width: calc(100% - 56 * 2px);
        margin: 0px 56px;
    }
}

@media screen and (max-width: 1679px) {
    .banner .col__left {
        width: 500px;
    }

    .banner .col__right {
        width: 58%;
    }

    .video-item-img-layer .upload-info-mask {
        font-size: 12px;
    }

    .firm-propose .video-item-img .desc-img {
        height: 240px;
    }

    .actor-item {
        width: calc(100% * (1 / 3) - 16px);
    }

    .connect-opera,
    .firm-related-img .update-info-mask {
        font-size: 12px;
    }

    .actor-list-wrapper .firm-related-title {
        font-size: 14px;
    }

}

@media (max-width: 1199px) {
    .banner {
        width: calc(100% - 50 * 2px);
        margin: 0px 50px;
    }

    .app .content .container {
        width: calc(100% - 50 * 2px);
        margin: 0px 50px;
    }

    .banner .col__right {
        position: relative;
        width: calc(100% + 50 * 2px);
        left: -50px;
    }

    .wrap-banner-img {
        width: 100%;
    }

    .left-layer {
        display: none;
    }

    .banner > .wrap-banner, .banner > .wrap-banner > .row {
        align-items: flex-start;
    }

    .wrap-banner .row {
        display: flex;
        flex-direction: column-reverse;
    }

    .banner .col__left {
        margin: 0;
        width: 100%;
    }

    body {
        font-size: 14px;
    }

    .banner-content__title > h1 {
        font-size: 34px;
    }

    .firm-propose .video-item-img .desc-img {
        height: 200px;
    }

    .wrap-banner-img {
        height: 550px;
    }

    .firm-propose.video-list-wrapper .video-item {
        width: calc(100% * (1 / 5) - 16px);
    }

    .video-list-wrapper .video-item {
        width: calc(100% * (1 / 3) - 16px);
    }
}

@media (max-width: 1024px) {
    .actor-item {
        width: calc(100% * (1 / 2) - 16px);
    }

    .actor-avatar {
        width: 60px;
        height: 60px;
    }

    .actor-item .connect-info {
        height: 60px;
    }
}

@media (max-width: 991px) {
    .banner {
        width: calc(100% - 30 * 2px);
        margin: 0px 30px;
    }

    .app .content .container {
        width: calc(100% - 30 * 2px);
        margin: 0px 30px;
    }

    .banner .col__right {
        position: relative;
        width: calc(100% + 30 * 2px);
        left: -30px;
    }

    .wrap-banner-img {
        height: 500px;
    }

    .firm-propose.video-list-wrapper .video-item {
        width: calc(100% * (1 / 4) - 12px);
        margin: 0px 6px 28px 6px;
    }
}

@media (max-width: 800px) {
    .wrap-banner-img {
        height: 460px;
    }

    .wrap-banner-img {
        height: 420px;
    }
}

@media (max-width: 768px) {
    .banner {
        width: calc(100% - 15 * 2px);
        margin: 0px 15px;
        margin-top: 88px;
    }

    .app .content .container {
        width: calc(100% - 15 * 2px);
        margin: 0px 15px;
    }

    .banner .col__right {
        position: relative;
        width: calc(100% + 15 * 2px);
        left: -15px;
    }

    .firm-propose.video-list-wrapper .video-item {
        width: calc(100% * (1 / 3) - 8px);
        margin: 0px 4px 24px 4px;
    }

    .firm-propose .video-item-img .desc-img {
        height: 260px;
    }

    .video-list-wrapper .video-item {
        width: calc(100% * (1 / 2) - 8px);
        margin: 0px 4px 24px 4px;
    }

    .wrap-banner-img {
        height: 400px;
    }

    .banner-content__title > h1 {
        font-size: 30px;
    }

    .actor-item {
        width: calc(100% * (1 / 1) - 16px);
    }

}

@media (max-width: 640px) {
    .wrap-banner-img {
        height: 360px;
    }

    body {
        font-size: 13px;
    }

    .firm-propose .video-item-img .desc-img {
        height: 220px;
    }

    .banner-content__top,
    .banner-content__category span,
    .banner-content__infor {
        font-size: 13px;
    }

    .wrap-banner-img {
        height: 340px;
    }

    .banner-content__top,
    .banner-content__top .top {
        height: 20px;
        line-height: 20px;
    }

    .page-tab-content {
        font-size: 12px;
        width: 140px;
    }

    .tab-content-ul .nav-link {
        padding: 8px 0;
    }

    .episodes-page {
        margin: 16px 0;
    }

    .banner .group-btn {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
    }

    .banner .group-btn .btn-item {
        height: 40px;
        line-height: 40px;
        margin-bottom: 10px;
    }
}

@media (max-width: 585px) {
    .wrap-banner-img {
        height: 300px;
    }

    .banner-content__title > h1 {
        font-size: 26px;
    }

    .firm-propose .video-item-img .desc-img {
        height: 200px;
    }
}

@media (max-width: 500px) {
    .wrap-banner-img {
        height: 260px;
    }

    .firm-propose .video-item-img .desc-img {
        height: 180px;
    }

    .tab-content-ul .nav-link {
        margin-right: 20px;
    }

}

@media (max-width: 480px) {
    .wrap-banner-img {
        height: 220px;
    }

    .firm-propose .video-item-img .desc-img {
        height: 160px;
    }
}

@media (max-width: 385px) {
    .firm-propose .video-item-img .desc-img {
        height: 140px;
    }
}
.focus-info-wrapper .focus-promotion {
    margin: 12px 0;
}
