<svg width="25px" height="41px" viewBox="0 0 25 41" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="轮播图元素-2" transform="translate(-324.000000, -2225.000000)" fill="#FFFFFF" fill-rule="nonzero"><g id="06-箭头" transform="translate(100.000000, 2102.000000)"><g id="hover" transform="translate(200.000000, 78.000000)"><path d="M44.1666667,52 L44.1666667,56.8372093 C44.1667695,57.3894941 43.7190542,57.8372093 43.1667695,57.8372093 C43.1667352,57.8372093 43.1667009,57.8372093 43.1666667,57.8371065 L22,57.8349302 L22,57.8349302 L22,78.4883721 C22,79.0406568 21.5522847,79.4883721 21,79.4883721 L16,79.4883721 C15.4477153,79.4883721 15,79.0406568 15,78.4883721 L15,55.5581395 L15,55.5581395 C15,53.1551754 16.9037148,51.1865074 19.3183879,51.0125024 L19.6666667,51 L43.1666667,51 C43.7189514,51 44.1666667,51.4477153 44.1666667,52 Z" id="箭头/右-" transform="translate(29.583333, 65.244186) scale(-1, 1) rotate(-45.000000) translate(-29.583333, -65.244186) "></path></g></g></g></g></svg>
