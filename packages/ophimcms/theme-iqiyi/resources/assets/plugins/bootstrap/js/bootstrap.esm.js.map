{"version": 3, "file": "bootstrap.esm.js", "sources": ["../../js/src/dom/selector-engine.js", "../../js/src/util/index.js", "../../js/src/dom/event-handler.js", "../../js/src/dom/data.js", "../../js/src/base-component.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/util/scrollbar.js", "../../js/src/util/backdrop.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children)\n      .filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (ancestor.matches(selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "import SelectorEngine from '../dom/selector-engine'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.2): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLower<PERSON>ase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return SelectorEngine.findOne(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst customEventsRegex = /^(mouseenter|mouseleave)/i\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            // eslint-disable-next-line unicorn/consistent-destructuring\n            EventHandler.off(element, event.type, selector, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  let typeEvent = getTypeEvent(originalTypeEvent)\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (customEventsRegex.test(originalTypeEvent)) {\n    const wrapFn = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    if (delegationFn) {\n      delegationFn = wrapFn(delegationFn)\n    } else {\n      handler = wrapFn(handler)\n    }\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\nimport {\n  executeAfterTransition,\n  getElement\n} from './util/index'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.0.2'\n\nclass BaseComponent {\n  constructor(element) {\n    element = getElement(element)\n\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    Object.getOwnPropertyNames(this).forEach(propertyName => {\n      this[propertyName] = null\n    })\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.get(element, this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-bs-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_ALERT = 'alert'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASS_NAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(element), element, isAnimated)\n  }\n\n  _destroyElement(element) {\n    element.remove()\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toL<PERSON>er<PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n\n    Object.keys(element.dataset)\n      .filter(key => key.startsWith('bs'))\n      .forEach(key => {\n        let pureKey = key.replace(/^bs/, '')\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n        attributes[pureKey] = normalizeData(element.dataset[key])\n      })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isRTL,\n  isVisible,\n  getNextActiveElement,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_INDICATOR = '[data-bs-target]'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const order = index > activeIndex ?\n      ORDER_NEXT :\n      ORDER_PREV\n\n    this._slide(order, this._items[index])\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    this._slide(direction > 0 ? DIRECTION_RIGHT : DIRECTION_LEFT)\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      this.touchDeltaX = event.touches && event.touches.length > 1 ?\n        0 :\n        event.touches[0].clientX - this.touchStartX\n    }\n\n    const end = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(direction)\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByOrder(order, activeElement) {\n    const isNext = order === ORDER_NEXT\n    return getNextActiveElement(this._items, activeElement, isNext, this._config.wrap)\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n      activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n      activeIndicator.removeAttribute('aria-current')\n\n      const indicators = SelectorEngine.find(SELECTOR_INDICATOR, this._indicatorsElement)\n\n      for (let i = 0; i < indicators.length; i++) {\n        if (Number.parseInt(indicators[i].getAttribute('data-bs-slide-to'), 10) === this._getItemIndex(element)) {\n          indicators[i].classList.add(CLASS_NAME_ACTIVE)\n          indicators[i].setAttribute('aria-current', 'true')\n          break\n        }\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(directionOrOrder, element) {\n    const order = this._directionToOrder(directionOrOrder)\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || this._getItemByOrder(order, activeElement)\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    const isNext = order === ORDER_NEXT\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n    const eventDirectionName = this._orderToDirection(order)\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    if (this._isSliding) {\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    const triggerSlidEvent = () => {\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const completeCallBack = () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(triggerSlidEvent, 0)\n      }\n\n      this._queueCallback(completeCallBack, activeElement, true)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      triggerSlidEvent()\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (![DIRECTION_RIGHT, DIRECTION_LEFT].includes(direction)) {\n      return direction\n    }\n\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (![ORDER_NEXT, ORDER_PREV].includes(order)) {\n      return order\n    }\n\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    const data = Carousel.getOrCreateInstance(element, config)\n\n    let { _config } = data\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-bs-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Carousel.getInstance(target).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Carousel.getInstance(carousels[i]))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getSelectorFromElement,\n  getElementFromSelector,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${this._element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-bs-target=\"#${this._element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-bs-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Collapse.getInstance(tempActiveData) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.set(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    parent = getElement(parent)\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-bs-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Collapse.getInstance(element)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Collapse.getInstance(element)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getElementFromSelector,\n  isDisabled,\n  isElement,\n  isVisible,\n  isRTL,\n  noop,\n  getNextActiveElement,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null,\n  autoClose: true\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)',\n  autoClose: '(boolean|string)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    if (isDisabled(this._element)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    if (isActive) {\n      this.hide()\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (this._inNavbar) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'none')\n    } else {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = getElement(this._config.reference)\n      } else if (typeof this._config.reference === 'object') {\n        referenceElement = this._config.reference\n      }\n\n      const popperConfig = this._getPopperConfig()\n      const isDisplayStatic = popperConfig.modifiers.find(modifier => modifier.name === 'applyStyles' && modifier.enabled === false)\n\n      this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n\n      if (isDisplayStatic) {\n        Manipulator.setDataAttribute(this._menu, 'popper', 'static')\n      }\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', noop))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      this.toggle()\n    })\n  }\n\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.off(elem, 'mouseover', noop))\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    const data = Dropdown.getOrCreateInstance(element, config)\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const context = Dropdown.getInstance(toggles[i])\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      if (!context._element.classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      const relatedTarget = {\n        relatedTarget: context._element\n      }\n\n      if (event) {\n        const composedPath = event.composedPath()\n        const isMenuTarget = composedPath.includes(context._menu)\n        if (\n          composedPath.includes(context._element) ||\n          (context._config.autoClose === 'inside' && !isMenuTarget) ||\n          (context._config.autoClose === 'outside' && isMenuTarget)\n        ) {\n          continue\n        }\n\n        // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n        if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n          continue\n        }\n\n        if (event.type === 'click') {\n          relatedTarget.clickEvent = event\n        }\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (!isActive && event.key === ESCAPE_KEY) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const getToggleButton = () => this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n\n    if (event.key === ESCAPE_KEY) {\n      getToggleButton().focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (event.key === ARROW_UP_KEY || event.key === ARROW_DOWN_KEY) {\n      if (!isActive) {\n        getToggleButton().click()\n      }\n\n      Dropdown.getInstance(getToggleButton())._selectMenuItem(event)\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.dropdownInterface(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\nimport { isElement } from './index'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, 'paddingRight', calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n  }\n\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProp, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProp)\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      element.style[styleProp] = `${callback(Number.parseFloat(calculatedValue))}px`\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n  }\n\n  _saveInitialAttribute(element, styleProp) {\n    const actualValue = element.style[styleProp]\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProp) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProp)\n      if (typeof value === 'undefined') {\n        element.style.removeProperty(styleProp)\n      } else {\n        Manipulator.removeDataAttribute(element, styleProp)\n        element.style[styleProp] = value\n      }\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n    } else {\n      SelectorEngine.find(selector, this._element).forEach(callBack)\n    }\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { execute, executeAfterTransition, getElement, reflow, typeCheckConfig } from './index'\n\nconst Default = {\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  isAnimated: false,\n  rootElement: 'body', // give the choice to place backdrop under different elements\n  clickCallback: null\n}\n\nconst DefaultType = {\n  isVisible: 'boolean',\n  isAnimated: 'boolean',\n  rootElement: '(element|string)',\n  clickCallback: '(function|null)'\n}\nconst NAME = 'backdrop'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nclass Backdrop {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    if (this._config.isAnimated) {\n      reflow(this._getElement())\n    }\n\n    this._getElement().classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  // Private\n\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = CLASS_NAME_BACKDROP\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    this._config.rootElement.appendChild(this._getElement())\n\n    EventHandler.on(this._getElement(), EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isRTL,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport ScrollBarHelper from './util/scrollbar'\nimport BaseComponent from './base-component'\nimport Backdrop from './util/backdrop'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"modal\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._isShown = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    if (this._isAnimated()) {\n      this._isTransitioning = true\n    }\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, event => this.hide(event))\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event && ['A', 'AREA'].includes(event.target.tagName)) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const isAnimated = this._isAnimated()\n\n    if (isAnimated) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    this._queueCallback(() => this._hideModal(), this._element, isAnimated)\n  }\n\n  dispose() {\n    [window, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    this._backdrop.dispose()\n    super.dispose()\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const isAnimated = this._isAnimated()\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (isAnimated) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, isAnimated)\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _showBackdrop(callback) {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n      if (this._ignoreBackdropClick) {\n        this._ignoreBackdropClick = false\n        return\n      }\n\n      if (event.target !== event.currentTarget) {\n        return\n      }\n\n      if (this._config.backdrop === true) {\n        this.hide()\n      } else if (this._config.backdrop === 'static') {\n        this._triggerBackdropTransition()\n      }\n    })\n\n    this._backdrop.show(callback)\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const { classList, scrollHeight, style } = this._element\n    const isModalOverflowing = scrollHeight > document.documentElement.clientHeight\n\n    // return if the following background transition hasn't yet completed\n    if ((!isModalOverflowing && style.overflowY === 'hidden') || classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      style.overflowY = 'hidden'\n    }\n\n    classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        this._queueCallback(() => {\n          style.overflowY = ''\n        }, this._dialog)\n      }\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if ((!isBodyOverflowing && isModalOverflowing && !isRTL()) || (isBodyOverflowing && !isModalOverflowing && isRTL())) {\n      this._element.style.paddingLeft = `${scrollbarWidth}px`\n    }\n\n    if ((isBodyOverflowing && !isModalOverflowing && !isRTL()) || (!isBodyOverflowing && isModalOverflowing && isRTL())) {\n      this._element.style.paddingRight = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  isVisible,\n  typeCheckConfig\n} from './util/index'\nimport ScrollBarHelper from './util/scrollbar'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Manipulator from './dom/manipulator'\nimport Backdrop from './util/backdrop'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: 'boolean',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\nconst CLASS_NAME_SHOW = 'show'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"offcanvas\"]'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._element.style.visibility = 'visible'\n\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n      this._enforceFocusOnElement(this._element)\n    }\n\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const completeCallBack = () => {\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.setAttribute('aria-hidden', true)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n      this._element.style.visibility = 'hidden'\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    super.dispose()\n    EventHandler.off(document, EVENT_FOCUSIN)\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: this._config.backdrop,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: () => this.hide()\n    })\n  }\n\n  _enforceFocusOnElement(element) {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n        element !== event.target &&\n        !element.contains(event.target)) {\n        element.focus()\n      }\n    })\n    element.focus()\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (this._config.keyboard && event.key === ESCAPE_KEY) {\n        this.hide()\n      }\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const allReadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (allReadyOpen && allReadyOpen !== target) {\n    Offcanvas.getInstance(allReadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () =>\n  SelectorEngine.find(OPEN_SELECTOR).forEach(el => Offcanvas.getOrCreateInstance(el).show())\n)\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attrName)) {\n    if (uriAttrs.has(attrName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attr.nodeValue) || DATA_URL_PATTERN.test(attr.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attrName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (!allowlistKeys.includes(elName)) {\n      el.remove()\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  findShadowRoot,\n  getElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this._config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.remove()\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this.isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = shadowRoot === null ?\n      this._element.ownerDocument.documentElement.contains(this._element) :\n      shadowRoot.contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const tipId = getUID(this.constructor.NAME)\n\n    tip.setAttribute('id', tipId)\n    this._element.setAttribute('aria-describedby', tipId)\n\n    this.setContent()\n\n    if (this._config.animation) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    const placement = typeof this._config.placement === 'function' ?\n      this._config.placement.call(this, tip, this._element) :\n      this._config.placement\n\n    const attachment = this._getAttachment(placement)\n    this._addAttachmentClass(attachment)\n\n    const { container } = this._config\n    Data.set(tip, this.constructor.DATA_KEY, this)\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.appendChild(tip)\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    const customClass = typeof this._config.customClass === 'function' ? this._config.customClass() : this._config.customClass\n    if (customClass) {\n      tip.classList.add(...customClass.split(' '))\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children).forEach(element => {\n        EventHandler.on(element, 'mouseover', noop)\n      })\n    }\n\n    const complete = () => {\n      const prevHoverState = this._hoverState\n\n      this._hoverState = null\n      EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n      if (prevHoverState === HOVER_STATE_OUT) {\n        this._leave(null, this)\n      }\n    }\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (this._hoverState !== HOVER_STATE_SHOW) {\n        tip.remove()\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      if (this._popper) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this._config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (isElement(content)) {\n      content = getElement(content)\n\n      // content is a DOM node or a jQuery\n      if (this._config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this._config.html) {\n      if (this._config.sanitize) {\n        content = sanitizeHtml(content, this._config.allowList, this._config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this._element.getAttribute('data-bs-original-title')\n\n    if (!title) {\n      title = typeof this._config.title === 'function' ?\n        this._config.title.call(this._element) :\n        this._config.title\n    }\n\n    return title\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _initializeOnDelegatedTarget(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.get(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(event.delegateTarget, this._getDelegateConfig())\n      Data.set(event.delegateTarget, dataKey, context)\n    }\n\n    return context\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this._config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this._config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this._config.selector) {\n      this._config = {\n        ...this._config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context._config.delay || !context._config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context._config.delay.show)\n  }\n\n  _leave(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = context._element.contains(event.relatedTarget)\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context._config.delay || !context._config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context._config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this._config) {\n      for (const key in this._config) {\n        if (this.constructor.Default[key] !== this._config[key]) {\n          config[key] = this._config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    this.tip = super.getTipElement()\n\n    if (!this.getTitle()) {\n      SelectorEngine.findOne(SELECTOR_TITLE, this.tip).remove()\n    }\n\n    if (!this._getContent()) {\n      SelectorEngine.findOne(SELECTOR_CONTENT, this.tip).remove()\n    }\n\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this._element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContent() {\n    return this._element.getAttribute('data-bs-content') || this._config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = this._element.tagName === 'BODY' ? window : this._element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, () => this._process())\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  reflow\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE))) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = previous ?\n      EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      }) :\n      null\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      active.classList.remove(CLASS_NAME_SHOW)\n      this._queueCallback(complete, element, true)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    let parent = element.parentNode\n    if (parent && parent.nodeName === 'LI') {\n      parent = parent.parentNode\n    }\n\n    if (parent && parent.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE, dropdownElement)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  const data = Tab.getOrCreateInstance(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout':\n        this._hasMouseInteraction = isInteracting\n        break\n      case 'focusin':\n      case 'focusout':\n        this._hasKeyboardInteraction = isInteracting\n        break\n      default:\n        break\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n"], "names": ["NODE_TEXT", "SelectorEngine", "find", "selector", "element", "document", "documentElement", "concat", "Element", "prototype", "querySelectorAll", "call", "findOne", "querySelector", "children", "filter", "child", "matches", "parents", "ancestor", "parentNode", "nodeType", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "MAX_UID", "MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "undefined", "toString", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "getElementById", "getSelector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "getElementFromSelector", "getTransitionDurationFromElement", "transitionDuration", "transitionDelay", "window", "getComputedStyle", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "j<PERSON>y", "getElement", "length", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "getClientRects", "getPropertyValue", "isDisabled", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "addEventListener", "isRTL", "dir", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "execute", "executeAfterTransition", "transitionElement", "waitForTransition", "durationPadding", "emulatedDuration", "called", "handler", "target", "removeEventListener", "setTimeout", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "index", "indexOf", "listLength", "max", "min", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "customEventsRegex", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "bootstrapHandler", "event", "<PERSON><PERSON><PERSON><PERSON>", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "dom<PERSON><PERSON>s", "i", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "len", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "getTypeEvent", "isNative", "has", "add<PERSON><PERSON><PERSON>", "wrapFn", "relatedTarget", "handlers", "previousFn", "replace", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "on", "one", "inNamespace", "isNamespace", "elementEvent", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "key", "defineProperty", "get", "preventDefault", "elementMap", "Map", "set", "instance", "instanceMap", "size", "console", "error", "Array", "from", "remove", "delete", "VERSION", "BaseComponent", "constructor", "_element", "Data", "DATA_KEY", "dispose", "EVENT_KEY", "getOwnPropertyNames", "propertyName", "_queueCallback", "isAnimated", "getInstance", "getOrCreateInstance", "Error", "DATA_API_KEY", "SELECTOR_DISMISS", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "CLASS_NAME_ALERT", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "<PERSON><PERSON>", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "closest", "_destroyElement", "each", "data", "handle<PERSON><PERSON><PERSON>", "alertInstance", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "setAttribute", "button", "normalizeData", "val", "normalizeDataKey", "chr", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "pureKey", "char<PERSON>t", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "KEY_TO_DIRECTION", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_SLIDE", "CLASS_NAME_END", "CLASS_NAME_START", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "CLASS_NAME_POINTER_EVENT", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_NEXT_PREV", "SELECTOR_INDICATORS", "SELECTOR_INDICATOR", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "POINTER_TYPE_TOUCH", "POINTER_TYPE_PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "activeIndex", "_getItemIndex", "order", "_handleSwipe", "absDeltax", "abs", "direction", "_keydown", "_addTouchEventListeners", "start", "pointerType", "clientX", "touches", "move", "end", "clearTimeout", "itemImg", "e", "add", "tagName", "_getItemByOrder", "isNext", "_triggerSlideEvent", "eventDirectionName", "targetIndex", "fromIndex", "_setActiveIndicatorElement", "activeIndicator", "indicators", "parseInt", "elementInterval", "defaultInterval", "directionOrOrder", "_directionToOrder", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "_orderToDirection", "slideEvent", "triggerSlidEvent", "completeCallBack", "carouselInterface", "action", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "Collapse", "_isTransitioning", "_triggerArray", "id", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "startEvent", "elemActive", "collapseInterface", "dimension", "_getDimension", "style", "setTransitioning", "complete", "capitalizedDimension", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "selectorElements", "ESCAPE_KEY", "SPACE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "REGEXP_KEYDOWN", "EVENT_CLICK", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPEND", "CLASS_NAME_DROPSTART", "CLASS_NAME_NAVBAR", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "boundary", "reference", "display", "popperConfig", "autoClose", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "isActive", "getParentFromElement", "showEvent", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "isDisplayStatic", "modifiers", "modifier", "enabled", "createPopper", "focus", "_completeHide", "destroy", "update", "hideEvent", "_getPlacement", "parentDropdown", "isEnd", "_getOffset", "map", "popperData", "defaultBsPopperConfig", "placement", "options", "_selectMenuItem", "items", "dropdownInterface", "clearMenus", "toggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "clickEvent", "dataApiKeydownHandler", "stopPropagation", "getToggleButton", "click", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "ScrollBarHelper", "getWidth", "documentWidth", "clientWidth", "innerWidth", "width", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "_saveInitialAttribute", "overflow", "styleProp", "scrollbarWidth", "manipulationCallBack", "_applyManipulationCallback", "reset", "_resetElementAttributes", "actualValue", "removeProperty", "callBack", "isOverflowing", "clickCallback", "CLASS_NAME_BACKDROP", "EVENT_MOUSEDOWN", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "className", "append<PERSON><PERSON><PERSON>", "EVENT_HIDE_PREVENTED", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_DISMISS", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_isShown", "_ignoreBackdropClick", "_scrollBar", "_isAnimated", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "_enforceFocus", "transitionComplete", "_triggerBackdropTransition", "_resetAdjustments", "currentTarget", "scrollHeight", "isModalOverflowing", "clientHeight", "overflowY", "isBodyOverflowing", "paddingLeft", "paddingRight", "scroll", "OPEN_SELECTOR", "<PERSON><PERSON><PERSON>", "visibility", "_enforceFocusOnElement", "blur", "completeCallback", "allReadyOpen", "el", "uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "nodeValue", "regExp", "attrRegex", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "elements", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacements", "customClass", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_MODAL", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "_initializeOnDelegatedTarget", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "updateAttachment", "dataKey", "_getDelegateConfig", "phase", "_handlePopperPlacementChange", "onFirstUpdate", "triggers", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "dataAttributes", "dataAttr", "tabClass", "token", "tClass", "state", "popper", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "method", "EVENT_ACTIVATE", "EVENT_SCROLL", "CLASS_NAME_DROPDOWN_ITEM", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "METHOD_OFFSET", "METHOD_POSITION", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "isActiveTarget", "queries", "link", "join", "listGroup", "navItem", "node", "spy", "CLASS_NAME_DROPDOWN_MENU", "SELECTOR_ACTIVE_UL", "SELECTOR_DROPDOWN_ACTIVE_CHILD", "Tab", "listElement", "itemSelector", "activeElements", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdown", "EVENT_MOUSEOVER", "EVENT_MOUSEOUT", "EVENT_FOCUSOUT", "CLASS_NAME_HIDE", "CLASS_NAME_SHOWING", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "_onInteraction", "isInteracting"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAEA,MAAMA,SAAS,GAAG,CAAlB;AAEA,MAAMC,cAAc,GAAG;AACrBC,EAAAA,IAAI,CAACC,QAAD,EAAWC,OAAO,GAAGC,QAAQ,CAACC,eAA9B,EAA+C;AACjD,WAAO,GAAGC,MAAH,CAAU,GAAGC,OAAO,CAACC,SAAR,CAAkBC,gBAAlB,CAAmCC,IAAnC,CAAwCP,OAAxC,EAAiDD,QAAjD,CAAb,CAAP;AACD,GAHoB;;AAKrBS,EAAAA,OAAO,CAACT,QAAD,EAAWC,OAAO,GAAGC,QAAQ,CAACC,eAA9B,EAA+C;AACpD,WAAOE,OAAO,CAACC,SAAR,CAAkBI,aAAlB,CAAgCF,IAAhC,CAAqCP,OAArC,EAA8CD,QAA9C,CAAP;AACD,GAPoB;;AASrBW,EAAAA,QAAQ,CAACV,OAAD,EAAUD,QAAV,EAAoB;AAC1B,WAAO,GAAGI,MAAH,CAAU,GAAGH,OAAO,CAACU,QAArB,EACJC,MADI,CACGC,KAAK,IAAIA,KAAK,CAACC,OAAN,CAAcd,QAAd,CADZ,CAAP;AAED,GAZoB;;AAcrBe,EAAAA,OAAO,CAACd,OAAD,EAAUD,QAAV,EAAoB;AACzB,UAAMe,OAAO,GAAG,EAAhB;AAEA,QAAIC,QAAQ,GAAGf,OAAO,CAACgB,UAAvB;;AAEA,WAAOD,QAAQ,IAAIA,QAAQ,CAACE,QAAT,KAAsBC,IAAI,CAACC,YAAvC,IAAuDJ,QAAQ,CAACE,QAAT,KAAsBrB,SAApF,EAA+F;AAC7F,UAAImB,QAAQ,CAACF,OAAT,CAAiBd,QAAjB,CAAJ,EAAgC;AAC9Be,QAAAA,OAAO,CAACM,IAAR,CAAaL,QAAb;AACD;;AAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACC,UAApB;AACD;;AAED,WAAOF,OAAP;AACD,GA5BoB;;AA8BrBO,EAAAA,IAAI,CAACrB,OAAD,EAAUD,QAAV,EAAoB;AACtB,QAAIuB,QAAQ,GAAGtB,OAAO,CAACuB,sBAAvB;;AAEA,WAAOD,QAAP,EAAiB;AACf,UAAIA,QAAQ,CAACT,OAAT,CAAiBd,QAAjB,CAAJ,EAAgC;AAC9B,eAAO,CAACuB,QAAD,CAAP;AACD;;AAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACC,sBAApB;AACD;;AAED,WAAO,EAAP;AACD,GA1CoB;;AA4CrBC,EAAAA,IAAI,CAACxB,OAAD,EAAUD,QAAV,EAAoB;AACtB,QAAIyB,IAAI,GAAGxB,OAAO,CAACyB,kBAAnB;;AAEA,WAAOD,IAAP,EAAa;AACX,UAAIA,IAAI,CAACX,OAAL,CAAad,QAAb,CAAJ,EAA4B;AAC1B,eAAO,CAACyB,IAAD,CAAP;AACD;;AAEDA,MAAAA,IAAI,GAAGA,IAAI,CAACC,kBAAZ;AACD;;AAED,WAAO,EAAP;AACD;;AAxDoB,CAAvB;;ACbA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,OAAO,GAAG,OAAhB;AACA,MAAMC,uBAAuB,GAAG,IAAhC;AACA,MAAMC,cAAc,GAAG,eAAvB;;AAGA,MAAMC,MAAM,GAAGC,GAAG,IAAI;AACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;AACrC,WAAQ,GAAED,GAAI,EAAd;AACD;;AAED,SAAO,GAAGE,QAAH,CAAYzB,IAAZ,CAAiBuB,GAAjB,EAAsBG,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;AACD,CAND;AAQA;AACA;AACA;AACA;AACA;;;AAEA,MAAMC,MAAM,GAAGC,MAAM,IAAI;AACvB,KAAG;AACDA,IAAAA,MAAM,IAAIC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBb,OAA3B,CAAV;AACD,GAFD,QAESzB,QAAQ,CAACuC,cAAT,CAAwBJ,MAAxB,CAFT;;AAIA,SAAOA,MAAP;AACD,CAND;;AAQA,MAAMK,WAAW,GAAGzC,OAAO,IAAI;AAC7B,MAAID,QAAQ,GAAGC,OAAO,CAAC0C,YAAR,CAAqB,gBAArB,CAAf;;AAEA,MAAI,CAAC3C,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;AACjC,QAAI4C,QAAQ,GAAG3C,OAAO,CAAC0C,YAAR,CAAqB,MAArB,CAAf,CADiC;AAIjC;AACA;AACA;;AACA,QAAI,CAACC,QAAD,IAAc,CAACA,QAAQ,CAACC,QAAT,CAAkB,GAAlB,CAAD,IAA2B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA9C,EAAyE;AACvE,aAAO,IAAP;AACD,KATgC;;;AAYjC,QAAIF,QAAQ,CAACC,QAAT,CAAkB,GAAlB,KAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA/B,EAAyD;AACvDF,MAAAA,QAAQ,GAAI,IAAGA,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAuB,EAAtC;AACD;;AAED/C,IAAAA,QAAQ,GAAG4C,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACI,IAAT,EAA/B,GAAiD,IAA5D;AACD;;AAED,SAAOhD,QAAP;AACD,CAvBD;;AAyBA,MAAMiD,sBAAsB,GAAGhD,OAAO,IAAI;AACxC,QAAMD,QAAQ,GAAG0C,WAAW,CAACzC,OAAD,CAA5B;;AAEA,MAAID,QAAJ,EAAc;AACZ,WAAOE,QAAQ,CAACQ,aAAT,CAAuBV,QAAvB,IAAmCA,QAAnC,GAA8C,IAArD;AACD;;AAED,SAAO,IAAP;AACD,CARD;;AAUA,MAAMkD,sBAAsB,GAAGjD,OAAO,IAAI;AACxC,QAAMD,QAAQ,GAAG0C,WAAW,CAACzC,OAAD,CAA5B;AAEA,SAAOD,QAAQ,GAAGE,QAAQ,CAACQ,aAAT,CAAuBV,QAAvB,CAAH,GAAsC,IAArD;AACD,CAJD;;AAMA,MAAMmD,gCAAgC,GAAGlD,OAAO,IAAI;AAClD,MAAI,CAACA,OAAL,EAAc;AACZ,WAAO,CAAP;AACD,GAHiD;;;AAMlD,MAAI;AAAEmD,IAAAA,kBAAF;AAAsBC,IAAAA;AAAtB,MAA0CC,MAAM,CAACC,gBAAP,CAAwBtD,OAAxB,CAA9C;AAEA,QAAMuD,uBAAuB,GAAGC,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,CAAhC;AACA,QAAMO,oBAAoB,GAAGF,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAA7B,CATkD;;AAYlD,MAAI,CAACG,uBAAD,IAA4B,CAACG,oBAAjC,EAAuD;AACrD,WAAO,CAAP;AACD,GAdiD;;;AAiBlDP,EAAAA,kBAAkB,GAAGA,kBAAkB,CAACL,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAArB;AACAM,EAAAA,eAAe,GAAGA,eAAe,CAACN,KAAhB,CAAsB,GAAtB,EAA2B,CAA3B,CAAlB;AAEA,SAAO,CAACU,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,IAAwCK,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAAzC,IAA+EzB,uBAAtF;AACD,CArBD;;AAuBA,MAAMgC,oBAAoB,GAAG3D,OAAO,IAAI;AACtCA,EAAAA,OAAO,CAAC4D,aAAR,CAAsB,IAAIC,KAAJ,CAAUjC,cAAV,CAAtB;AACD,CAFD;;AAIA,MAAMkC,SAAS,GAAGhC,GAAG,IAAI;AACvB,MAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;AACnC,WAAO,KAAP;AACD;;AAED,MAAI,OAAOA,GAAG,CAACiC,MAAX,KAAsB,WAA1B,EAAuC;AACrCjC,IAAAA,GAAG,GAAGA,GAAG,CAAC,CAAD,CAAT;AACD;;AAED,SAAO,OAAOA,GAAG,CAACb,QAAX,KAAwB,WAA/B;AACD,CAVD;;AAYA,MAAM+C,UAAU,GAAGlC,GAAG,IAAI;AACxB,MAAIgC,SAAS,CAAChC,GAAD,CAAb,EAAoB;AAAE;AACpB,WAAOA,GAAG,CAACiC,MAAJ,GAAajC,GAAG,CAAC,CAAD,CAAhB,GAAsBA,GAA7B;AACD;;AAED,MAAI,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,CAACmC,MAAJ,GAAa,CAA5C,EAA+C;AAC7C,WAAOpE,cAAc,CAACW,OAAf,CAAuBsB,GAAvB,CAAP;AACD;;AAED,SAAO,IAAP;AACD,CAVD;;AAYA,MAAMoC,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;AAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;AAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;AACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;AACA,UAAMG,SAAS,GAAGD,KAAK,IAAIb,SAAS,CAACa,KAAD,CAAlB,GAA4B,SAA5B,GAAwC9C,MAAM,CAAC8C,KAAD,CAAhE;;AAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;AAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,aAAYP,QAAS,oBAAmBG,SAAU,wBAAuBF,aAAc,IADlH,CAAN;AAGD;AACF,GAVD;AAWD,CAZD;;AAcA,MAAMO,SAAS,GAAGjF,OAAO,IAAI;AAC3B,MAAI,CAAC8D,SAAS,CAAC9D,OAAD,CAAV,IAAuBA,OAAO,CAACkF,cAAR,GAAyBjB,MAAzB,KAAoC,CAA/D,EAAkE;AAChE,WAAO,KAAP;AACD;;AAED,SAAOX,gBAAgB,CAACtD,OAAD,CAAhB,CAA0BmF,gBAA1B,CAA2C,YAA3C,MAA6D,SAApE;AACD,CAND;;AAQA,MAAMC,UAAU,GAAGpF,OAAO,IAAI;AAC5B,MAAI,CAACA,OAAD,IAAYA,OAAO,CAACiB,QAAR,KAAqBC,IAAI,CAACC,YAA1C,EAAwD;AACtD,WAAO,IAAP;AACD;;AAED,MAAInB,OAAO,CAACqF,SAAR,CAAkBC,QAAlB,CAA2B,UAA3B,CAAJ,EAA4C;AAC1C,WAAO,IAAP;AACD;;AAED,MAAI,OAAOtF,OAAO,CAACuF,QAAf,KAA4B,WAAhC,EAA6C;AAC3C,WAAOvF,OAAO,CAACuF,QAAf;AACD;;AAED,SAAOvF,OAAO,CAACwF,YAAR,CAAqB,UAArB,KAAoCxF,OAAO,CAAC0C,YAAR,CAAqB,UAArB,MAAqC,OAAhF;AACD,CAdD;;AAgBA,MAAM+C,cAAc,GAAGzF,OAAO,IAAI;AAChC,MAAI,CAACC,QAAQ,CAACC,eAAT,CAAyBwF,YAA9B,EAA4C;AAC1C,WAAO,IAAP;AACD,GAH+B;;;AAMhC,MAAI,OAAO1F,OAAO,CAAC2F,WAAf,KAA+B,UAAnC,EAA+C;AAC7C,UAAMC,IAAI,GAAG5F,OAAO,CAAC2F,WAAR,EAAb;AACA,WAAOC,IAAI,YAAYC,UAAhB,GAA6BD,IAA7B,GAAoC,IAA3C;AACD;;AAED,MAAI5F,OAAO,YAAY6F,UAAvB,EAAmC;AACjC,WAAO7F,OAAP;AACD,GAb+B;;;AAgBhC,MAAI,CAACA,OAAO,CAACgB,UAAb,EAAyB;AACvB,WAAO,IAAP;AACD;;AAED,SAAOyE,cAAc,CAACzF,OAAO,CAACgB,UAAT,CAArB;AACD,CArBD;;AAuBA,MAAM8E,IAAI,GAAG,MAAM,EAAnB;;AAEA,MAAMC,MAAM,GAAG/F,OAAO,IAAIA,OAAO,CAACgG,YAAlC;;AAEA,MAAMC,SAAS,GAAG,MAAM;AACtB,QAAM;AAAEC,IAAAA;AAAF,MAAa7C,MAAnB;;AAEA,MAAI6C,MAAM,IAAI,CAACjG,QAAQ,CAACkG,IAAT,CAAcX,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;AAC9D,WAAOU,MAAP;AACD;;AAED,SAAO,IAAP;AACD,CARD;;AAUA,MAAME,yBAAyB,GAAG,EAAlC;;AAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;AACrC,MAAIrG,QAAQ,CAACsG,UAAT,KAAwB,SAA5B,EAAuC;AACrC;AACA,QAAI,CAACH,yBAAyB,CAACnC,MAA/B,EAAuC;AACrChE,MAAAA,QAAQ,CAACuG,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;AAClDJ,QAAAA,yBAAyB,CAAC5B,OAA1B,CAAkC8B,QAAQ,IAAIA,QAAQ,EAAtD;AACD,OAFD;AAGD;;AAEDF,IAAAA,yBAAyB,CAAChF,IAA1B,CAA+BkF,QAA/B;AACD,GATD,MASO;AACLA,IAAAA,QAAQ;AACT;AACF,CAbD;;AAeA,MAAMG,KAAK,GAAG,MAAMxG,QAAQ,CAACC,eAAT,CAAyBwG,GAAzB,KAAiC,KAArD;;AAEA,MAAMC,kBAAkB,GAAGC,MAAM,IAAI;AACnCP,EAAAA,kBAAkB,CAAC,MAAM;AACvB,UAAMQ,CAAC,GAAGZ,SAAS,EAAnB;AACA;;AACA,QAAIY,CAAJ,EAAO;AACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;AACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;AACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;AACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;AACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;AAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;AACA,eAAOJ,MAAM,CAACM,eAAd;AACD,OAHD;AAID;AACF,GAbiB,CAAlB;AAcD,CAfD;;AAiBA,MAAMG,OAAO,GAAGf,QAAQ,IAAI;AAC1B,MAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;AAClCA,IAAAA,QAAQ;AACT;AACF,CAJD;;AAMA,MAAMgB,sBAAsB,GAAG,CAAChB,QAAD,EAAWiB,iBAAX,EAA8BC,iBAAiB,GAAG,IAAlD,KAA2D;AACxF,MAAI,CAACA,iBAAL,EAAwB;AACtBH,IAAAA,OAAO,CAACf,QAAD,CAAP;AACA;AACD;;AAED,QAAMmB,eAAe,GAAG,CAAxB;AACA,QAAMC,gBAAgB,GAAGxE,gCAAgC,CAACqE,iBAAD,CAAhC,GAAsDE,eAA/E;AAEA,MAAIE,MAAM,GAAG,KAAb;;AAEA,QAAMC,OAAO,GAAG,CAAC;AAAEC,IAAAA;AAAF,GAAD,KAAgB;AAC9B,QAAIA,MAAM,KAAKN,iBAAf,EAAkC;AAChC;AACD;;AAEDI,IAAAA,MAAM,GAAG,IAAT;AACAJ,IAAAA,iBAAiB,CAACO,mBAAlB,CAAsClG,cAAtC,EAAsDgG,OAAtD;AACAP,IAAAA,OAAO,CAACf,QAAD,CAAP;AACD,GARD;;AAUAiB,EAAAA,iBAAiB,CAACf,gBAAlB,CAAmC5E,cAAnC,EAAmDgG,OAAnD;AACAG,EAAAA,UAAU,CAAC,MAAM;AACf,QAAI,CAACJ,MAAL,EAAa;AACXhE,MAAAA,oBAAoB,CAAC4D,iBAAD,CAApB;AACD;AACF,GAJS,EAIPG,gBAJO,CAAV;AAKD,CA3BD;AA6BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMM,oBAAoB,GAAG,CAACC,IAAD,EAAOC,aAAP,EAAsBC,aAAtB,EAAqCC,cAArC,KAAwD;AACnF,MAAIC,KAAK,GAAGJ,IAAI,CAACK,OAAL,CAAaJ,aAAb,CAAZ,CADmF;;AAInF,MAAIG,KAAK,KAAK,CAAC,CAAf,EAAkB;AAChB,WAAOJ,IAAI,CAAC,CAACE,aAAD,IAAkBC,cAAlB,GAAmCH,IAAI,CAAChE,MAAL,GAAc,CAAjD,GAAqD,CAAtD,CAAX;AACD;;AAED,QAAMsE,UAAU,GAAGN,IAAI,CAAChE,MAAxB;AAEAoE,EAAAA,KAAK,IAAIF,aAAa,GAAG,CAAH,GAAO,CAAC,CAA9B;;AAEA,MAAIC,cAAJ,EAAoB;AAClBC,IAAAA,KAAK,GAAG,CAACA,KAAK,GAAGE,UAAT,IAAuBA,UAA/B;AACD;;AAED,SAAON,IAAI,CAAC5F,IAAI,CAACmG,GAAL,CAAS,CAAT,EAAYnG,IAAI,CAACoG,GAAL,CAASJ,KAAT,EAAgBE,UAAU,GAAG,CAA7B,CAAZ,CAAD,CAAX;AACD,CAjBD;;AC3RA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;;AAEA,MAAMG,cAAc,GAAG,oBAAvB;AACA,MAAMC,cAAc,GAAG,MAAvB;AACA,MAAMC,aAAa,GAAG,QAAtB;AACA,MAAMC,aAAa,GAAG,EAAtB;;AACA,IAAIC,QAAQ,GAAG,CAAf;AACA,MAAMC,YAAY,GAAG;AACnBC,EAAAA,UAAU,EAAE,WADO;AAEnBC,EAAAA,UAAU,EAAE;AAFO,CAArB;AAIA,MAAMC,iBAAiB,GAAG,2BAA1B;AACA,MAAMC,YAAY,GAAG,IAAIC,GAAJ,CAAQ,CAC3B,OAD2B,EAE3B,UAF2B,EAG3B,SAH2B,EAI3B,WAJ2B,EAK3B,aAL2B,EAM3B,YAN2B,EAO3B,gBAP2B,EAQ3B,WAR2B,EAS3B,UAT2B,EAU3B,WAV2B,EAW3B,aAX2B,EAY3B,WAZ2B,EAa3B,SAb2B,EAc3B,UAd2B,EAe3B,OAf2B,EAgB3B,mBAhB2B,EAiB3B,YAjB2B,EAkB3B,WAlB2B,EAmB3B,UAnB2B,EAoB3B,aApB2B,EAqB3B,aArB2B,EAsB3B,aAtB2B,EAuB3B,WAvB2B,EAwB3B,cAxB2B,EAyB3B,eAzB2B,EA0B3B,cA1B2B,EA2B3B,eA3B2B,EA4B3B,YA5B2B,EA6B3B,OA7B2B,EA8B3B,MA9B2B,EA+B3B,QA/B2B,EAgC3B,OAhC2B,EAiC3B,QAjC2B,EAkC3B,QAlC2B,EAmC3B,SAnC2B,EAoC3B,UApC2B,EAqC3B,MArC2B,EAsC3B,QAtC2B,EAuC3B,cAvC2B,EAwC3B,QAxC2B,EAyC3B,MAzC2B,EA0C3B,kBA1C2B,EA2C3B,kBA3C2B,EA4C3B,OA5C2B,EA6C3B,OA7C2B,EA8C3B,QA9C2B,CAAR,CAArB;AAiDA;AACA;AACA;AACA;AACA;;AAEA,SAASC,WAAT,CAAqBrJ,OAArB,EAA8BsJ,GAA9B,EAAmC;AACjC,SAAQA,GAAG,IAAK,GAAEA,GAAI,KAAIR,QAAQ,EAAG,EAA9B,IAAoC9I,OAAO,CAAC8I,QAA5C,IAAwDA,QAAQ,EAAvE;AACD;;AAED,SAASS,QAAT,CAAkBvJ,OAAlB,EAA2B;AACzB,QAAMsJ,GAAG,GAAGD,WAAW,CAACrJ,OAAD,CAAvB;AAEAA,EAAAA,OAAO,CAAC8I,QAAR,GAAmBQ,GAAnB;AACAT,EAAAA,aAAa,CAACS,GAAD,CAAb,GAAqBT,aAAa,CAACS,GAAD,CAAb,IAAsB,EAA3C;AAEA,SAAOT,aAAa,CAACS,GAAD,CAApB;AACD;;AAED,SAASE,gBAAT,CAA0BxJ,OAA1B,EAAmCiH,EAAnC,EAAuC;AACrC,SAAO,SAASW,OAAT,CAAiB6B,KAAjB,EAAwB;AAC7BA,IAAAA,KAAK,CAACC,cAAN,GAAuB1J,OAAvB;;AAEA,QAAI4H,OAAO,CAAC+B,MAAZ,EAAoB;AAClBC,MAAAA,YAAY,CAACC,GAAb,CAAiB7J,OAAjB,EAA0ByJ,KAAK,CAACK,IAAhC,EAAsC7C,EAAtC;AACD;;AAED,WAAOA,EAAE,CAAC8C,KAAH,CAAS/J,OAAT,EAAkB,CAACyJ,KAAD,CAAlB,CAAP;AACD,GARD;AASD;;AAED,SAASO,0BAAT,CAAoChK,OAApC,EAA6CD,QAA7C,EAAuDkH,EAAvD,EAA2D;AACzD,SAAO,SAASW,OAAT,CAAiB6B,KAAjB,EAAwB;AAC7B,UAAMQ,WAAW,GAAGjK,OAAO,CAACM,gBAAR,CAAyBP,QAAzB,CAApB;;AAEA,SAAK,IAAI;AAAE8H,MAAAA;AAAF,QAAa4B,KAAtB,EAA6B5B,MAAM,IAAIA,MAAM,KAAK,IAAlD,EAAwDA,MAAM,GAAGA,MAAM,CAAC7G,UAAxE,EAAoF;AAClF,WAAK,IAAIkJ,CAAC,GAAGD,WAAW,CAAChG,MAAzB,EAAiCiG,CAAC,EAAlC,GAAuC;AACrC,YAAID,WAAW,CAACC,CAAD,CAAX,KAAmBrC,MAAvB,EAA+B;AAC7B4B,UAAAA,KAAK,CAACC,cAAN,GAAuB7B,MAAvB;;AAEA,cAAID,OAAO,CAAC+B,MAAZ,EAAoB;AAClB;AACAC,YAAAA,YAAY,CAACC,GAAb,CAAiB7J,OAAjB,EAA0ByJ,KAAK,CAACK,IAAhC,EAAsC/J,QAAtC,EAAgDkH,EAAhD;AACD;;AAED,iBAAOA,EAAE,CAAC8C,KAAH,CAASlC,MAAT,EAAiB,CAAC4B,KAAD,CAAjB,CAAP;AACD;AACF;AACF,KAhB4B;;;AAmB7B,WAAO,IAAP;AACD,GApBD;AAqBD;;AAED,SAASU,WAAT,CAAqBC,MAArB,EAA6BxC,OAA7B,EAAsCyC,kBAAkB,GAAG,IAA3D,EAAiE;AAC/D,QAAMC,YAAY,GAAGhG,MAAM,CAACC,IAAP,CAAY6F,MAAZ,CAArB;;AAEA,OAAK,IAAIF,CAAC,GAAG,CAAR,EAAWK,GAAG,GAAGD,YAAY,CAACrG,MAAnC,EAA2CiG,CAAC,GAAGK,GAA/C,EAAoDL,CAAC,EAArD,EAAyD;AACvD,UAAMT,KAAK,GAAGW,MAAM,CAACE,YAAY,CAACJ,CAAD,CAAb,CAApB;;AAEA,QAAIT,KAAK,CAACe,eAAN,KAA0B5C,OAA1B,IAAqC6B,KAAK,CAACY,kBAAN,KAA6BA,kBAAtE,EAA0F;AACxF,aAAOZ,KAAP;AACD;AACF;;AAED,SAAO,IAAP;AACD;;AAED,SAASgB,eAAT,CAAyBC,iBAAzB,EAA4C9C,OAA5C,EAAqD+C,YAArD,EAAmE;AACjE,QAAMC,UAAU,GAAG,OAAOhD,OAAP,KAAmB,QAAtC;AACA,QAAM4C,eAAe,GAAGI,UAAU,GAAGD,YAAH,GAAkB/C,OAApD;AAEA,MAAIiD,SAAS,GAAGC,YAAY,CAACJ,iBAAD,CAA5B;AACA,QAAMK,QAAQ,GAAG5B,YAAY,CAAC6B,GAAb,CAAiBH,SAAjB,CAAjB;;AAEA,MAAI,CAACE,QAAL,EAAe;AACbF,IAAAA,SAAS,GAAGH,iBAAZ;AACD;;AAED,SAAO,CAACE,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,CAAP;AACD;;AAED,SAASI,UAAT,CAAoBjL,OAApB,EAA6B0K,iBAA7B,EAAgD9C,OAAhD,EAAyD+C,YAAzD,EAAuEhB,MAAvE,EAA+E;AAC7E,MAAI,OAAOe,iBAAP,KAA6B,QAA7B,IAAyC,CAAC1K,OAA9C,EAAuD;AACrD;AACD;;AAED,MAAI,CAAC4H,OAAL,EAAc;AACZA,IAAAA,OAAO,GAAG+C,YAAV;AACAA,IAAAA,YAAY,GAAG,IAAf;AACD,GAR4E;AAW7E;;;AACA,MAAIzB,iBAAiB,CAACpE,IAAlB,CAAuB4F,iBAAvB,CAAJ,EAA+C;AAC7C,UAAMQ,MAAM,GAAGjE,EAAE,IAAI;AACnB,aAAO,UAAUwC,KAAV,EAAiB;AACtB,YAAI,CAACA,KAAK,CAAC0B,aAAP,IAAyB1B,KAAK,CAAC0B,aAAN,KAAwB1B,KAAK,CAACC,cAA9B,IAAgD,CAACD,KAAK,CAACC,cAAN,CAAqBpE,QAArB,CAA8BmE,KAAK,CAAC0B,aAApC,CAA9E,EAAmI;AACjI,iBAAOlE,EAAE,CAAC1G,IAAH,CAAQ,IAAR,EAAckJ,KAAd,CAAP;AACD;AACF,OAJD;AAKD,KAND;;AAQA,QAAIkB,YAAJ,EAAkB;AAChBA,MAAAA,YAAY,GAAGO,MAAM,CAACP,YAAD,CAArB;AACD,KAFD,MAEO;AACL/C,MAAAA,OAAO,GAAGsD,MAAM,CAACtD,OAAD,CAAhB;AACD;AACF;;AAED,QAAM,CAACgD,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,IAA2CJ,eAAe,CAACC,iBAAD,EAAoB9C,OAApB,EAA6B+C,YAA7B,CAAhE;AACA,QAAMP,MAAM,GAAGb,QAAQ,CAACvJ,OAAD,CAAvB;AACA,QAAMoL,QAAQ,GAAGhB,MAAM,CAACS,SAAD,CAAN,KAAsBT,MAAM,CAACS,SAAD,CAAN,GAAoB,EAA1C,CAAjB;AACA,QAAMQ,UAAU,GAAGlB,WAAW,CAACiB,QAAD,EAAWZ,eAAX,EAA4BI,UAAU,GAAGhD,OAAH,GAAa,IAAnD,CAA9B;;AAEA,MAAIyD,UAAJ,EAAgB;AACdA,IAAAA,UAAU,CAAC1B,MAAX,GAAoB0B,UAAU,CAAC1B,MAAX,IAAqBA,MAAzC;AAEA;AACD;;AAED,QAAML,GAAG,GAAGD,WAAW,CAACmB,eAAD,EAAkBE,iBAAiB,CAACY,OAAlB,CAA0B5C,cAA1B,EAA0C,EAA1C,CAAlB,CAAvB;AACA,QAAMzB,EAAE,GAAG2D,UAAU,GACnBZ,0BAA0B,CAAChK,OAAD,EAAU4H,OAAV,EAAmB+C,YAAnB,CADP,GAEnBnB,gBAAgB,CAACxJ,OAAD,EAAU4H,OAAV,CAFlB;AAIAX,EAAAA,EAAE,CAACoD,kBAAH,GAAwBO,UAAU,GAAGhD,OAAH,GAAa,IAA/C;AACAX,EAAAA,EAAE,CAACuD,eAAH,GAAqBA,eAArB;AACAvD,EAAAA,EAAE,CAAC0C,MAAH,GAAYA,MAAZ;AACA1C,EAAAA,EAAE,CAAC6B,QAAH,GAAcQ,GAAd;AACA8B,EAAAA,QAAQ,CAAC9B,GAAD,CAAR,GAAgBrC,EAAhB;AAEAjH,EAAAA,OAAO,CAACwG,gBAAR,CAAyBqE,SAAzB,EAAoC5D,EAApC,EAAwC2D,UAAxC;AACD;;AAED,SAASW,aAAT,CAAuBvL,OAAvB,EAAgCoK,MAAhC,EAAwCS,SAAxC,EAAmDjD,OAAnD,EAA4DyC,kBAA5D,EAAgF;AAC9E,QAAMpD,EAAE,GAAGkD,WAAW,CAACC,MAAM,CAACS,SAAD,CAAP,EAAoBjD,OAApB,EAA6ByC,kBAA7B,CAAtB;;AAEA,MAAI,CAACpD,EAAL,EAAS;AACP;AACD;;AAEDjH,EAAAA,OAAO,CAAC8H,mBAAR,CAA4B+C,SAA5B,EAAuC5D,EAAvC,EAA2CuE,OAAO,CAACnB,kBAAD,CAAlD;AACA,SAAOD,MAAM,CAACS,SAAD,CAAN,CAAkB5D,EAAE,CAAC6B,QAArB,CAAP;AACD;;AAED,SAAS2C,wBAAT,CAAkCzL,OAAlC,EAA2CoK,MAA3C,EAAmDS,SAAnD,EAA8Da,SAA9D,EAAyE;AACvE,QAAMC,iBAAiB,GAAGvB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;AAEAvG,EAAAA,MAAM,CAACC,IAAP,CAAYoH,iBAAZ,EAA+BnH,OAA/B,CAAuCoH,UAAU,IAAI;AACnD,QAAIA,UAAU,CAAChJ,QAAX,CAAoB8I,SAApB,CAAJ,EAAoC;AAClC,YAAMjC,KAAK,GAAGkC,iBAAiB,CAACC,UAAD,CAA/B;AAEAL,MAAAA,aAAa,CAACvL,OAAD,EAAUoK,MAAV,EAAkBS,SAAlB,EAA6BpB,KAAK,CAACe,eAAnC,EAAoDf,KAAK,CAACY,kBAA1D,CAAb;AACD;AACF,GAND;AAOD;;AAED,SAASS,YAAT,CAAsBrB,KAAtB,EAA6B;AAC3B;AACAA,EAAAA,KAAK,GAAGA,KAAK,CAAC6B,OAAN,CAAc3C,cAAd,EAA8B,EAA9B,CAAR;AACA,SAAOI,YAAY,CAACU,KAAD,CAAZ,IAAuBA,KAA9B;AACD;;AAED,MAAMG,YAAY,GAAG;AACnBiC,EAAAA,EAAE,CAAC7L,OAAD,EAAUyJ,KAAV,EAAiB7B,OAAjB,EAA0B+C,YAA1B,EAAwC;AACxCM,IAAAA,UAAU,CAACjL,OAAD,EAAUyJ,KAAV,EAAiB7B,OAAjB,EAA0B+C,YAA1B,EAAwC,KAAxC,CAAV;AACD,GAHkB;;AAKnBmB,EAAAA,GAAG,CAAC9L,OAAD,EAAUyJ,KAAV,EAAiB7B,OAAjB,EAA0B+C,YAA1B,EAAwC;AACzCM,IAAAA,UAAU,CAACjL,OAAD,EAAUyJ,KAAV,EAAiB7B,OAAjB,EAA0B+C,YAA1B,EAAwC,IAAxC,CAAV;AACD,GAPkB;;AASnBd,EAAAA,GAAG,CAAC7J,OAAD,EAAU0K,iBAAV,EAA6B9C,OAA7B,EAAsC+C,YAAtC,EAAoD;AACrD,QAAI,OAAOD,iBAAP,KAA6B,QAA7B,IAAyC,CAAC1K,OAA9C,EAAuD;AACrD;AACD;;AAED,UAAM,CAAC4K,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,IAA2CJ,eAAe,CAACC,iBAAD,EAAoB9C,OAApB,EAA6B+C,YAA7B,CAAhE;AACA,UAAMoB,WAAW,GAAGlB,SAAS,KAAKH,iBAAlC;AACA,UAAMN,MAAM,GAAGb,QAAQ,CAACvJ,OAAD,CAAvB;AACA,UAAMgM,WAAW,GAAGtB,iBAAiB,CAAC7H,UAAlB,CAA6B,GAA7B,CAApB;;AAEA,QAAI,OAAO2H,eAAP,KAA2B,WAA/B,EAA4C;AAC1C;AACA,UAAI,CAACJ,MAAD,IAAW,CAACA,MAAM,CAACS,SAAD,CAAtB,EAAmC;AACjC;AACD;;AAEDU,MAAAA,aAAa,CAACvL,OAAD,EAAUoK,MAAV,EAAkBS,SAAlB,EAA6BL,eAA7B,EAA8CI,UAAU,GAAGhD,OAAH,GAAa,IAArE,CAAb;AACA;AACD;;AAED,QAAIoE,WAAJ,EAAiB;AACf1H,MAAAA,MAAM,CAACC,IAAP,CAAY6F,MAAZ,EAAoB5F,OAApB,CAA4ByH,YAAY,IAAI;AAC1CR,QAAAA,wBAAwB,CAACzL,OAAD,EAAUoK,MAAV,EAAkB6B,YAAlB,EAAgCvB,iBAAiB,CAACwB,KAAlB,CAAwB,CAAxB,CAAhC,CAAxB;AACD,OAFD;AAGD;;AAED,UAAMP,iBAAiB,GAAGvB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;AACAvG,IAAAA,MAAM,CAACC,IAAP,CAAYoH,iBAAZ,EAA+BnH,OAA/B,CAAuC2H,WAAW,IAAI;AACpD,YAAMP,UAAU,GAAGO,WAAW,CAACb,OAAZ,CAAoB1C,aAApB,EAAmC,EAAnC,CAAnB;;AAEA,UAAI,CAACmD,WAAD,IAAgBrB,iBAAiB,CAAC9H,QAAlB,CAA2BgJ,UAA3B,CAApB,EAA4D;AAC1D,cAAMnC,KAAK,GAAGkC,iBAAiB,CAACQ,WAAD,CAA/B;AAEAZ,QAAAA,aAAa,CAACvL,OAAD,EAAUoK,MAAV,EAAkBS,SAAlB,EAA6BpB,KAAK,CAACe,eAAnC,EAAoDf,KAAK,CAACY,kBAA1D,CAAb;AACD;AACF,KARD;AASD,GA7CkB;;AA+CnB+B,EAAAA,OAAO,CAACpM,OAAD,EAAUyJ,KAAV,EAAiB4C,IAAjB,EAAuB;AAC5B,QAAI,OAAO5C,KAAP,KAAiB,QAAjB,IAA6B,CAACzJ,OAAlC,EAA2C;AACzC,aAAO,IAAP;AACD;;AAED,UAAM6G,CAAC,GAAGZ,SAAS,EAAnB;AACA,UAAM4E,SAAS,GAAGC,YAAY,CAACrB,KAAD,CAA9B;AACA,UAAMsC,WAAW,GAAGtC,KAAK,KAAKoB,SAA9B;AACA,UAAME,QAAQ,GAAG5B,YAAY,CAAC6B,GAAb,CAAiBH,SAAjB,CAAjB;AAEA,QAAIyB,WAAJ;AACA,QAAIC,OAAO,GAAG,IAAd;AACA,QAAIC,cAAc,GAAG,IAArB;AACA,QAAIC,gBAAgB,GAAG,KAAvB;AACA,QAAIC,GAAG,GAAG,IAAV;;AAEA,QAAIX,WAAW,IAAIlF,CAAnB,EAAsB;AACpByF,MAAAA,WAAW,GAAGzF,CAAC,CAAChD,KAAF,CAAQ4F,KAAR,EAAe4C,IAAf,CAAd;AAEAxF,MAAAA,CAAC,CAAC7G,OAAD,CAAD,CAAWoM,OAAX,CAAmBE,WAAnB;AACAC,MAAAA,OAAO,GAAG,CAACD,WAAW,CAACK,oBAAZ,EAAX;AACAH,MAAAA,cAAc,GAAG,CAACF,WAAW,CAACM,6BAAZ,EAAlB;AACAH,MAAAA,gBAAgB,GAAGH,WAAW,CAACO,kBAAZ,EAAnB;AACD;;AAED,QAAI9B,QAAJ,EAAc;AACZ2B,MAAAA,GAAG,GAAGzM,QAAQ,CAAC6M,WAAT,CAAqB,YAArB,CAAN;AACAJ,MAAAA,GAAG,CAACK,SAAJ,CAAclC,SAAd,EAAyB0B,OAAzB,EAAkC,IAAlC;AACD,KAHD,MAGO;AACLG,MAAAA,GAAG,GAAG,IAAIM,WAAJ,CAAgBvD,KAAhB,EAAuB;AAC3B8C,QAAAA,OAD2B;AAE3BU,QAAAA,UAAU,EAAE;AAFe,OAAvB,CAAN;AAID,KAjC2B;;;AAoC5B,QAAI,OAAOZ,IAAP,KAAgB,WAApB,EAAiC;AAC/B/H,MAAAA,MAAM,CAACC,IAAP,CAAY8H,IAAZ,EAAkB7H,OAAlB,CAA0B0I,GAAG,IAAI;AAC/B5I,QAAAA,MAAM,CAAC6I,cAAP,CAAsBT,GAAtB,EAA2BQ,GAA3B,EAAgC;AAC9BE,UAAAA,GAAG,GAAG;AACJ,mBAAOf,IAAI,CAACa,GAAD,CAAX;AACD;;AAH6B,SAAhC;AAKD,OAND;AAOD;;AAED,QAAIT,gBAAJ,EAAsB;AACpBC,MAAAA,GAAG,CAACW,cAAJ;AACD;;AAED,QAAIb,cAAJ,EAAoB;AAClBxM,MAAAA,OAAO,CAAC4D,aAAR,CAAsB8I,GAAtB;AACD;;AAED,QAAIA,GAAG,CAACD,gBAAJ,IAAwB,OAAOH,WAAP,KAAuB,WAAnD,EAAgE;AAC9DA,MAAAA,WAAW,CAACe,cAAZ;AACD;;AAED,WAAOX,GAAP;AACD;;AA1GkB,CAArB;;AC/OA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAEA,MAAMY,UAAU,GAAG,IAAIC,GAAJ,EAAnB;AAEA,WAAe;AACbC,EAAAA,GAAG,CAACxN,OAAD,EAAUkN,GAAV,EAAeO,QAAf,EAAyB;AAC1B,QAAI,CAACH,UAAU,CAACtC,GAAX,CAAehL,OAAf,CAAL,EAA8B;AAC5BsN,MAAAA,UAAU,CAACE,GAAX,CAAexN,OAAf,EAAwB,IAAIuN,GAAJ,EAAxB;AACD;;AAED,UAAMG,WAAW,GAAGJ,UAAU,CAACF,GAAX,CAAepN,OAAf,CAApB,CAL0B;AAQ1B;;AACA,QAAI,CAAC0N,WAAW,CAAC1C,GAAZ,CAAgBkC,GAAhB,CAAD,IAAyBQ,WAAW,CAACC,IAAZ,KAAqB,CAAlD,EAAqD;AACnD;AACAC,MAAAA,OAAO,CAACC,KAAR,CAAe,+EAA8EC,KAAK,CAACC,IAAN,CAAWL,WAAW,CAACnJ,IAAZ,EAAX,EAA+B,CAA/B,CAAkC,GAA/H;AACA;AACD;;AAEDmJ,IAAAA,WAAW,CAACF,GAAZ,CAAgBN,GAAhB,EAAqBO,QAArB;AACD,GAjBY;;AAmBbL,EAAAA,GAAG,CAACpN,OAAD,EAAUkN,GAAV,EAAe;AAChB,QAAII,UAAU,CAACtC,GAAX,CAAehL,OAAf,CAAJ,EAA6B;AAC3B,aAAOsN,UAAU,CAACF,GAAX,CAAepN,OAAf,EAAwBoN,GAAxB,CAA4BF,GAA5B,KAAoC,IAA3C;AACD;;AAED,WAAO,IAAP;AACD,GAzBY;;AA2Bbc,EAAAA,MAAM,CAAChO,OAAD,EAAUkN,GAAV,EAAe;AACnB,QAAI,CAACI,UAAU,CAACtC,GAAX,CAAehL,OAAf,CAAL,EAA8B;AAC5B;AACD;;AAED,UAAM0N,WAAW,GAAGJ,UAAU,CAACF,GAAX,CAAepN,OAAf,CAApB;AAEA0N,IAAAA,WAAW,CAACO,MAAZ,CAAmBf,GAAnB,EAPmB;;AAUnB,QAAIQ,WAAW,CAACC,IAAZ,KAAqB,CAAzB,EAA4B;AAC1BL,MAAAA,UAAU,CAACW,MAAX,CAAkBjO,OAAlB;AACD;AACF;;AAxCY,CAAf;;ACfA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;AACA;AACA;AACA;;AAEA,MAAMkO,OAAO,GAAG,OAAhB;;AAEA,MAAMC,aAAN,CAAoB;AAClBC,EAAAA,WAAW,CAACpO,OAAD,EAAU;AACnBA,IAAAA,OAAO,GAAGgE,UAAU,CAAChE,OAAD,CAApB;;AAEA,QAAI,CAACA,OAAL,EAAc;AACZ;AACD;;AAED,SAAKqO,QAAL,GAAgBrO,OAAhB;AACAsO,IAAAA,IAAI,CAACd,GAAL,CAAS,KAAKa,QAAd,EAAwB,KAAKD,WAAL,CAAiBG,QAAzC,EAAmD,IAAnD;AACD;;AAEDC,EAAAA,OAAO,GAAG;AACRF,IAAAA,IAAI,CAACN,MAAL,CAAY,KAAKK,QAAjB,EAA2B,KAAKD,WAAL,CAAiBG,QAA5C;AACA3E,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKwE,QAAtB,EAAgC,KAAKD,WAAL,CAAiBK,SAAjD;AAEAnK,IAAAA,MAAM,CAACoK,mBAAP,CAA2B,IAA3B,EAAiClK,OAAjC,CAAyCmK,YAAY,IAAI;AACvD,WAAKA,YAAL,IAAqB,IAArB;AACD,KAFD;AAGD;;AAEDC,EAAAA,cAAc,CAACtI,QAAD,EAAWtG,OAAX,EAAoB6O,UAAU,GAAG,IAAjC,EAAuC;AACnDvH,IAAAA,sBAAsB,CAAChB,QAAD,EAAWtG,OAAX,EAAoB6O,UAApB,CAAtB;AACD;AAED;;;AAEkB,SAAXC,WAAW,CAAC9O,OAAD,EAAU;AAC1B,WAAOsO,IAAI,CAAClB,GAAL,CAASpN,OAAT,EAAkB,KAAKuO,QAAvB,CAAP;AACD;;AAEyB,SAAnBQ,mBAAmB,CAAC/O,OAAD,EAAUoE,MAAM,GAAG,EAAnB,EAAuB;AAC/C,WAAO,KAAK0K,WAAL,CAAiB9O,OAAjB,KAA6B,IAAI,IAAJ,CAASA,OAAT,EAAkB,OAAOoE,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAxD,CAApC;AACD;;AAEiB,aAAP8J,OAAO,GAAG;AACnB,WAAOA,OAAP;AACD;;AAEc,aAAJnH,IAAI,GAAG;AAChB,UAAM,IAAIiI,KAAJ,CAAU,qEAAV,CAAN;AACD;;AAEkB,aAART,QAAQ,GAAG;AACpB,WAAQ,MAAK,KAAKxH,IAAK,EAAvB;AACD;;AAEmB,aAAT0H,SAAS,GAAG;AACrB,WAAQ,IAAG,KAAKF,QAAS,EAAzB;AACD;;AAjDiB;;ACtBpB;AACA;AACA;AACA;AACA;AACA;AASA;AACA;AACA;AACA;AACA;;AAEA,MAAMxH,MAAI,GAAG,OAAb;AACA,MAAMwH,UAAQ,GAAG,UAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMU,cAAY,GAAG,WAArB;AAEA,MAAMC,gBAAgB,GAAG,2BAAzB;AAEA,MAAMC,WAAW,GAAI,QAAOV,WAAU,EAAtC;AACA,MAAMW,YAAY,GAAI,SAAQX,WAAU,EAAxC;AACA,MAAMY,sBAAoB,GAAI,QAAOZ,WAAU,GAAEQ,cAAa,EAA9D;AAEA,MAAMK,gBAAgB,GAAG,OAAzB;AACA,MAAMC,iBAAe,GAAG,MAAxB;AACA,MAAMC,iBAAe,GAAG,MAAxB;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,KAAN,SAAoBtB,aAApB,CAAkC;AAChC;AAEe,aAAJpH,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD,GAL+B;;;AAShC2I,EAAAA,KAAK,CAAC1P,OAAD,EAAU;AACb,UAAM2P,WAAW,GAAG3P,OAAO,GAAG,KAAK4P,eAAL,CAAqB5P,OAArB,CAAH,GAAmC,KAAKqO,QAAnE;;AACA,UAAMwB,WAAW,GAAG,KAAKC,kBAAL,CAAwBH,WAAxB,CAApB;;AAEA,QAAIE,WAAW,KAAK,IAAhB,IAAwBA,WAAW,CAACpD,gBAAxC,EAA0D;AACxD;AACD;;AAED,SAAKsD,cAAL,CAAoBJ,WAApB;AACD,GAlB+B;;;AAsBhCC,EAAAA,eAAe,CAAC5P,OAAD,EAAU;AACvB,WAAOiD,sBAAsB,CAACjD,OAAD,CAAtB,IAAmCA,OAAO,CAACgQ,OAAR,CAAiB,IAAGV,gBAAiB,EAArC,CAA1C;AACD;;AAEDQ,EAAAA,kBAAkB,CAAC9P,OAAD,EAAU;AAC1B,WAAO4J,YAAY,CAACwC,OAAb,CAAqBpM,OAArB,EAA8BmP,WAA9B,CAAP;AACD;;AAEDY,EAAAA,cAAc,CAAC/P,OAAD,EAAU;AACtBA,IAAAA,OAAO,CAACqF,SAAR,CAAkB2I,MAAlB,CAAyBwB,iBAAzB;AAEA,UAAMX,UAAU,GAAG7O,OAAO,CAACqF,SAAR,CAAkBC,QAAlB,CAA2BiK,iBAA3B,CAAnB;;AACA,SAAKX,cAAL,CAAoB,MAAM,KAAKqB,eAAL,CAAqBjQ,OAArB,CAA1B,EAAyDA,OAAzD,EAAkE6O,UAAlE;AACD;;AAEDoB,EAAAA,eAAe,CAACjQ,OAAD,EAAU;AACvBA,IAAAA,OAAO,CAACgO,MAAR;AAEApE,IAAAA,YAAY,CAACwC,OAAb,CAAqBpM,OAArB,EAA8BoP,YAA9B;AACD,GAzC+B;;;AA6CV,SAAflI,eAAe,CAAC9C,MAAD,EAAS;AAC7B,WAAO,KAAK8L,IAAL,CAAU,YAAY;AAC3B,YAAMC,IAAI,GAAGV,KAAK,CAACV,mBAAN,CAA0B,IAA1B,CAAb;;AAEA,UAAI3K,MAAM,KAAK,OAAf,EAAwB;AACtB+L,QAAAA,IAAI,CAAC/L,MAAD,CAAJ,CAAa,IAAb;AACD;AACF,KANM,CAAP;AAOD;;AAEmB,SAAbgM,aAAa,CAACC,aAAD,EAAgB;AAClC,WAAO,UAAU5G,KAAV,EAAiB;AACtB,UAAIA,KAAJ,EAAW;AACTA,QAAAA,KAAK,CAAC4D,cAAN;AACD;;AAEDgD,MAAAA,aAAa,CAACX,KAAd,CAAoB,IAApB;AACD,KAND;AAOD;;AA/D+B;AAkElC;AACA;AACA;AACA;AACA;;;AAEA9F,YAAY,CAACiC,EAAb,CAAgB5L,QAAhB,EAA0BoP,sBAA1B,EAAgDH,gBAAhD,EAAkEO,KAAK,CAACW,aAAN,CAAoB,IAAIX,KAAJ,EAApB,CAAlE;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA9I,kBAAkB,CAAC8I,KAAD,CAAlB;;AC1HA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;;AAEA,MAAM1I,MAAI,GAAG,QAAb;AACA,MAAMwH,UAAQ,GAAG,WAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMU,cAAY,GAAG,WAArB;AAEA,MAAMqB,mBAAiB,GAAG,QAA1B;AAEA,MAAMC,sBAAoB,GAAG,2BAA7B;AAEA,MAAMlB,sBAAoB,GAAI,QAAOZ,WAAU,GAAEQ,cAAa,EAA9D;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMuB,MAAN,SAAqBrC,aAArB,CAAmC;AACjC;AAEe,aAAJpH,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD,GALgC;;;AASjC0J,EAAAA,MAAM,GAAG;AACP;AACA,SAAKpC,QAAL,CAAcqC,YAAd,CAA2B,cAA3B,EAA2C,KAAKrC,QAAL,CAAchJ,SAAd,CAAwBoL,MAAxB,CAA+BH,mBAA/B,CAA3C;AACD,GAZgC;;;AAgBX,SAAfpJ,eAAe,CAAC9C,MAAD,EAAS;AAC7B,WAAO,KAAK8L,IAAL,CAAU,YAAY;AAC3B,YAAMC,IAAI,GAAGK,MAAM,CAACzB,mBAAP,CAA2B,IAA3B,CAAb;;AAEA,UAAI3K,MAAM,KAAK,QAAf,EAAyB;AACvB+L,QAAAA,IAAI,CAAC/L,MAAD,CAAJ;AACD;AACF,KANM,CAAP;AAOD;;AAxBgC;AA2BnC;AACA;AACA;AACA;AACA;;;AAEAwF,YAAY,CAACiC,EAAb,CAAgB5L,QAAhB,EAA0BoP,sBAA1B,EAAgDkB,sBAAhD,EAAsE9G,KAAK,IAAI;AAC7EA,EAAAA,KAAK,CAAC4D,cAAN;AAEA,QAAMsD,MAAM,GAAGlH,KAAK,CAAC5B,MAAN,CAAamI,OAAb,CAAqBO,sBAArB,CAAf;AACA,QAAMJ,IAAI,GAAGK,MAAM,CAACzB,mBAAP,CAA2B4B,MAA3B,CAAb;AAEAR,EAAAA,IAAI,CAACM,MAAL;AACD,CAPD;AASA;AACA;AACA;AACA;AACA;AACA;;AAEA9J,kBAAkB,CAAC6J,MAAD,CAAlB;;ACnFA;AACA;AACA;AACA;AACA;AACA;AAEA,SAASI,aAAT,CAAuBC,GAAvB,EAA4B;AAC1B,MAAIA,GAAG,KAAK,MAAZ,EAAoB;AAClB,WAAO,IAAP;AACD;;AAED,MAAIA,GAAG,KAAK,OAAZ,EAAqB;AACnB,WAAO,KAAP;AACD;;AAED,MAAIA,GAAG,KAAKrN,MAAM,CAACqN,GAAD,CAAN,CAAY7O,QAAZ,EAAZ,EAAoC;AAClC,WAAOwB,MAAM,CAACqN,GAAD,CAAb;AACD;;AAED,MAAIA,GAAG,KAAK,EAAR,IAAcA,GAAG,KAAK,MAA1B,EAAkC;AAChC,WAAO,IAAP;AACD;;AAED,SAAOA,GAAP;AACD;;AAED,SAASC,gBAAT,CAA0B5D,GAA1B,EAA+B;AAC7B,SAAOA,GAAG,CAAC5B,OAAJ,CAAY,QAAZ,EAAsByF,GAAG,IAAK,IAAGA,GAAG,CAAC7O,WAAJ,EAAkB,EAAnD,CAAP;AACD;;AAED,MAAM8O,WAAW,GAAG;AAClBC,EAAAA,gBAAgB,CAACjR,OAAD,EAAUkN,GAAV,EAAevI,KAAf,EAAsB;AACpC3E,IAAAA,OAAO,CAAC0Q,YAAR,CAAsB,WAAUI,gBAAgB,CAAC5D,GAAD,CAAM,EAAtD,EAAyDvI,KAAzD;AACD,GAHiB;;AAKlBuM,EAAAA,mBAAmB,CAAClR,OAAD,EAAUkN,GAAV,EAAe;AAChClN,IAAAA,OAAO,CAACmR,eAAR,CAAyB,WAAUL,gBAAgB,CAAC5D,GAAD,CAAM,EAAzD;AACD,GAPiB;;AASlBkE,EAAAA,iBAAiB,CAACpR,OAAD,EAAU;AACzB,QAAI,CAACA,OAAL,EAAc;AACZ,aAAO,EAAP;AACD;;AAED,UAAMqR,UAAU,GAAG,EAAnB;AAEA/M,IAAAA,MAAM,CAACC,IAAP,CAAYvE,OAAO,CAACsR,OAApB,EACG3Q,MADH,CACUuM,GAAG,IAAIA,GAAG,CAACrK,UAAJ,CAAe,IAAf,CADjB,EAEG2B,OAFH,CAEW0I,GAAG,IAAI;AACd,UAAIqE,OAAO,GAAGrE,GAAG,CAAC5B,OAAJ,CAAY,KAAZ,EAAmB,EAAnB,CAAd;AACAiG,MAAAA,OAAO,GAAGA,OAAO,CAACC,MAAR,CAAe,CAAf,EAAkBtP,WAAlB,KAAkCqP,OAAO,CAACrF,KAAR,CAAc,CAAd,EAAiBqF,OAAO,CAACtN,MAAzB,CAA5C;AACAoN,MAAAA,UAAU,CAACE,OAAD,CAAV,GAAsBX,aAAa,CAAC5Q,OAAO,CAACsR,OAAR,CAAgBpE,GAAhB,CAAD,CAAnC;AACD,KANH;AAQA,WAAOmE,UAAP;AACD,GAzBiB;;AA2BlBI,EAAAA,gBAAgB,CAACzR,OAAD,EAAUkN,GAAV,EAAe;AAC7B,WAAO0D,aAAa,CAAC5Q,OAAO,CAAC0C,YAAR,CAAsB,WAAUoO,gBAAgB,CAAC5D,GAAD,CAAM,EAAtD,CAAD,CAApB;AACD,GA7BiB;;AA+BlBwE,EAAAA,MAAM,CAAC1R,OAAD,EAAU;AACd,UAAM2R,IAAI,GAAG3R,OAAO,CAAC4R,qBAAR,EAAb;AAEA,WAAO;AACLC,MAAAA,GAAG,EAAEF,IAAI,CAACE,GAAL,GAAW5R,QAAQ,CAACkG,IAAT,CAAc2L,SADzB;AAELC,MAAAA,IAAI,EAAEJ,IAAI,CAACI,IAAL,GAAY9R,QAAQ,CAACkG,IAAT,CAAc6L;AAF3B,KAAP;AAID,GAtCiB;;AAwClBC,EAAAA,QAAQ,CAACjS,OAAD,EAAU;AAChB,WAAO;AACL6R,MAAAA,GAAG,EAAE7R,OAAO,CAACkS,SADR;AAELH,MAAAA,IAAI,EAAE/R,OAAO,CAACmS;AAFT,KAAP;AAID;;AA7CiB,CAApB;;AC/BA;AACA;AACA;AACA;AACA;AACA;AAiBA;AACA;AACA;AACA;AACA;;AAEA,MAAMpL,MAAI,GAAG,UAAb;AACA,MAAMwH,UAAQ,GAAG,aAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMU,cAAY,GAAG,WAArB;AAEA,MAAMmD,cAAc,GAAG,WAAvB;AACA,MAAMC,eAAe,GAAG,YAAxB;AACA,MAAMC,sBAAsB,GAAG,GAA/B;;AACA,MAAMC,eAAe,GAAG,EAAxB;AAEA,MAAMC,SAAO,GAAG;AACdC,EAAAA,QAAQ,EAAE,IADI;AAEdC,EAAAA,QAAQ,EAAE,IAFI;AAGdC,EAAAA,KAAK,EAAE,KAHO;AAIdC,EAAAA,KAAK,EAAE,OAJO;AAKdC,EAAAA,IAAI,EAAE,IALQ;AAMdC,EAAAA,KAAK,EAAE;AANO,CAAhB;AASA,MAAMC,aAAW,GAAG;AAClBN,EAAAA,QAAQ,EAAE,kBADQ;AAElBC,EAAAA,QAAQ,EAAE,SAFQ;AAGlBC,EAAAA,KAAK,EAAE,kBAHW;AAIlBC,EAAAA,KAAK,EAAE,kBAJW;AAKlBC,EAAAA,IAAI,EAAE,SALY;AAMlBC,EAAAA,KAAK,EAAE;AANW,CAApB;AASA,MAAME,UAAU,GAAG,MAAnB;AACA,MAAMC,UAAU,GAAG,MAAnB;AACA,MAAMC,cAAc,GAAG,MAAvB;AACA,MAAMC,eAAe,GAAG,OAAxB;AAEA,MAAMC,gBAAgB,GAAG;AACvB,GAAChB,cAAD,GAAkBe,eADK;AAEvB,GAACd,eAAD,GAAmBa;AAFI,CAAzB;AAKA,MAAMG,WAAW,GAAI,QAAO5E,WAAU,EAAtC;AACA,MAAM6E,UAAU,GAAI,OAAM7E,WAAU,EAApC;AACA,MAAM8E,aAAa,GAAI,UAAS9E,WAAU,EAA1C;AACA,MAAM+E,gBAAgB,GAAI,aAAY/E,WAAU,EAAhD;AACA,MAAMgF,gBAAgB,GAAI,aAAYhF,WAAU,EAAhD;AACA,MAAMiF,gBAAgB,GAAI,aAAYjF,WAAU,EAAhD;AACA,MAAMkF,eAAe,GAAI,YAAWlF,WAAU,EAA9C;AACA,MAAMmF,cAAc,GAAI,WAAUnF,WAAU,EAA5C;AACA,MAAMoF,iBAAiB,GAAI,cAAapF,WAAU,EAAlD;AACA,MAAMqF,eAAe,GAAI,YAAWrF,WAAU,EAA9C;AACA,MAAMsF,gBAAgB,GAAI,YAAWtF,WAAU,EAA/C;AACA,MAAMuF,qBAAmB,GAAI,OAAMvF,WAAU,GAAEQ,cAAa,EAA5D;AACA,MAAMI,sBAAoB,GAAI,QAAOZ,WAAU,GAAEQ,cAAa,EAA9D;AAEA,MAAMgF,mBAAmB,GAAG,UAA5B;AACA,MAAM3D,mBAAiB,GAAG,QAA1B;AACA,MAAM4D,gBAAgB,GAAG,OAAzB;AACA,MAAMC,cAAc,GAAG,mBAAvB;AACA,MAAMC,gBAAgB,GAAG,qBAAzB;AACA,MAAMC,eAAe,GAAG,oBAAxB;AACA,MAAMC,eAAe,GAAG,oBAAxB;AACA,MAAMC,wBAAwB,GAAG,eAAjC;AAEA,MAAMC,iBAAe,GAAG,SAAxB;AACA,MAAMC,oBAAoB,GAAG,uBAA7B;AACA,MAAMC,aAAa,GAAG,gBAAtB;AACA,MAAMC,iBAAiB,GAAG,oBAA1B;AACA,MAAMC,kBAAkB,GAAG,0CAA3B;AACA,MAAMC,mBAAmB,GAAG,sBAA5B;AACA,MAAMC,kBAAkB,GAAG,kBAA3B;AACA,MAAMC,mBAAmB,GAAG,qCAA5B;AACA,MAAMC,kBAAkB,GAAG,2BAA3B;AAEA,MAAMC,kBAAkB,GAAG,OAA3B;AACA,MAAMC,gBAAgB,GAAG,KAAzB;AAEA;AACA;AACA;AACA;AACA;;AACA,MAAMC,QAAN,SAAuBhH,aAAvB,CAAqC;AACnCC,EAAAA,WAAW,CAACpO,OAAD,EAAUoE,MAAV,EAAkB;AAC3B,UAAMpE,OAAN;AAEA,SAAKoV,MAAL,GAAc,IAAd;AACA,SAAKC,SAAL,GAAiB,IAAjB;AACA,SAAKC,cAAL,GAAsB,IAAtB;AACA,SAAKC,SAAL,GAAiB,KAAjB;AACA,SAAKC,UAAL,GAAkB,KAAlB;AACA,SAAKC,YAAL,GAAoB,IAApB;AACA,SAAKC,WAAL,GAAmB,CAAnB;AACA,SAAKC,WAAL,GAAmB,CAAnB;AAEA,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgBzR,MAAhB,CAAf;AACA,SAAK0R,kBAAL,GAA0BjW,cAAc,CAACW,OAAf,CAAuBqU,mBAAvB,EAA4C,KAAKxG,QAAjD,CAA1B;AACA,SAAK0H,eAAL,GAAuB,kBAAkB9V,QAAQ,CAACC,eAA3B,IAA8C8V,SAAS,CAACC,cAAV,GAA2B,CAAhG;AACA,SAAKC,aAAL,GAAqB1K,OAAO,CAACnI,MAAM,CAAC8S,YAAR,CAA5B;;AAEA,SAAKC,kBAAL;AACD,GAnBkC;;;AAuBjB,aAAP5D,OAAO,GAAG;AACnB,WAAOA,SAAP;AACD;;AAEc,aAAJzL,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD,GA7BkC;;;AAiCnCvF,EAAAA,IAAI,GAAG;AACL,SAAK6U,MAAL,CAAYrD,UAAZ;AACD;;AAEDsD,EAAAA,eAAe,GAAG;AAChB;AACA;AACA,QAAI,CAACrW,QAAQ,CAACsW,MAAV,IAAoBtR,SAAS,CAAC,KAAKoJ,QAAN,CAAjC,EAAkD;AAChD,WAAK7M,IAAL;AACD;AACF;;AAEDH,EAAAA,IAAI,GAAG;AACL,SAAKgV,MAAL,CAAYpD,UAAZ;AACD;;AAEDL,EAAAA,KAAK,CAACnJ,KAAD,EAAQ;AACX,QAAI,CAACA,KAAL,EAAY;AACV,WAAK8L,SAAL,GAAiB,IAAjB;AACD;;AAED,QAAI1V,cAAc,CAACW,OAAf,CAAuBoU,kBAAvB,EAA2C,KAAKvG,QAAhD,CAAJ,EAA+D;AAC7D1K,MAAAA,oBAAoB,CAAC,KAAK0K,QAAN,CAApB;AACA,WAAKmI,KAAL,CAAW,IAAX;AACD;;AAEDC,IAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;AACA,SAAKA,SAAL,GAAiB,IAAjB;AACD;;AAEDmB,EAAAA,KAAK,CAAC/M,KAAD,EAAQ;AACX,QAAI,CAACA,KAAL,EAAY;AACV,WAAK8L,SAAL,GAAiB,KAAjB;AACD;;AAED,QAAI,KAAKF,SAAT,EAAoB;AAClBoB,MAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;AACA,WAAKA,SAAL,GAAiB,IAAjB;AACD;;AAED,QAAI,KAAKO,OAAL,IAAgB,KAAKA,OAAL,CAAanD,QAA7B,IAAyC,CAAC,KAAK8C,SAAnD,EAA8D;AAC5D,WAAKmB,eAAL;;AAEA,WAAKrB,SAAL,GAAiBsB,WAAW,CAC1B,CAAC1W,QAAQ,CAAC2W,eAAT,GAA2B,KAAKN,eAAhC,GAAkD,KAAK9U,IAAxD,EAA8DqV,IAA9D,CAAmE,IAAnE,CAD0B,EAE1B,KAAKjB,OAAL,CAAanD,QAFa,CAA5B;AAID;AACF;;AAEDqE,EAAAA,EAAE,CAACzO,KAAD,EAAQ;AACR,SAAKiN,cAAL,GAAsBzV,cAAc,CAACW,OAAf,CAAuBiU,oBAAvB,EAA6C,KAAKpG,QAAlD,CAAtB;;AACA,UAAM0I,WAAW,GAAG,KAAKC,aAAL,CAAmB,KAAK1B,cAAxB,CAApB;;AAEA,QAAIjN,KAAK,GAAG,KAAK+M,MAAL,CAAYnR,MAAZ,GAAqB,CAA7B,IAAkCoE,KAAK,GAAG,CAA9C,EAAiD;AAC/C;AACD;;AAED,QAAI,KAAKmN,UAAT,EAAqB;AACnB5L,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKuC,QAAtB,EAAgCiF,UAAhC,EAA4C,MAAM,KAAKwD,EAAL,CAAQzO,KAAR,CAAlD;AACA;AACD;;AAED,QAAI0O,WAAW,KAAK1O,KAApB,EAA2B;AACzB,WAAKuK,KAAL;AACA,WAAK4D,KAAL;AACA;AACD;;AAED,UAAMS,KAAK,GAAG5O,KAAK,GAAG0O,WAAR,GACZ/D,UADY,GAEZC,UAFF;;AAIA,SAAKoD,MAAL,CAAYY,KAAZ,EAAmB,KAAK7B,MAAL,CAAY/M,KAAZ,CAAnB;AACD,GA3GkC;;;AA+GnCwN,EAAAA,UAAU,CAACzR,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,EACP,GAAGoO,SADI;AAEP,SAAGxB,WAAW,CAACI,iBAAZ,CAA8B,KAAK/C,QAAnC,CAFI;AAGP,UAAI,OAAOjK,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;AAHO,KAAT;AAKAF,IAAAA,eAAe,CAAC6C,MAAD,EAAO3C,MAAP,EAAe2O,aAAf,CAAf;AACA,WAAO3O,MAAP;AACD;;AAED8S,EAAAA,YAAY,GAAG;AACb,UAAMC,SAAS,GAAG9U,IAAI,CAAC+U,GAAL,CAAS,KAAKzB,WAAd,CAAlB;;AAEA,QAAIwB,SAAS,IAAI5E,eAAjB,EAAkC;AAChC;AACD;;AAED,UAAM8E,SAAS,GAAGF,SAAS,GAAG,KAAKxB,WAAnC;AAEA,SAAKA,WAAL,GAAmB,CAAnB;;AAEA,QAAI,CAAC0B,SAAL,EAAgB;AACd;AACD;;AAED,SAAKhB,MAAL,CAAYgB,SAAS,GAAG,CAAZ,GAAgBlE,eAAhB,GAAkCD,cAA9C;AACD;;AAEDkD,EAAAA,kBAAkB,GAAG;AACnB,QAAI,KAAKR,OAAL,CAAalD,QAAjB,EAA2B;AACzB9I,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BkF,aAA/B,EAA8C9J,KAAK,IAAI,KAAK6N,QAAL,CAAc7N,KAAd,CAAvD;AACD;;AAED,QAAI,KAAKmM,OAAL,CAAahD,KAAb,KAAuB,OAA3B,EAAoC;AAClChJ,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BmF,gBAA/B,EAAiD/J,KAAK,IAAI,KAAKmJ,KAAL,CAAWnJ,KAAX,CAA1D;AACAG,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BoF,gBAA/B,EAAiDhK,KAAK,IAAI,KAAK+M,KAAL,CAAW/M,KAAX,CAA1D;AACD;;AAED,QAAI,KAAKmM,OAAL,CAAa9C,KAAb,IAAsB,KAAKiD,eAA/B,EAAgD;AAC9C,WAAKwB,uBAAL;AACD;AACF;;AAEDA,EAAAA,uBAAuB,GAAG;AACxB,UAAMC,KAAK,GAAG/N,KAAK,IAAI;AACrB,UAAI,KAAKyM,aAAL,KAAuBzM,KAAK,CAACgO,WAAN,KAAsBvC,gBAAtB,IAA0CzL,KAAK,CAACgO,WAAN,KAAsBxC,kBAAvF,CAAJ,EAAgH;AAC9G,aAAKS,WAAL,GAAmBjM,KAAK,CAACiO,OAAzB;AACD,OAFD,MAEO,IAAI,CAAC,KAAKxB,aAAV,EAAyB;AAC9B,aAAKR,WAAL,GAAmBjM,KAAK,CAACkO,OAAN,CAAc,CAAd,EAAiBD,OAApC;AACD;AACF,KAND;;AAQA,UAAME,IAAI,GAAGnO,KAAK,IAAI;AACpB;AACA,WAAKkM,WAAL,GAAmBlM,KAAK,CAACkO,OAAN,IAAiBlO,KAAK,CAACkO,OAAN,CAAc1T,MAAd,GAAuB,CAAxC,GACjB,CADiB,GAEjBwF,KAAK,CAACkO,OAAN,CAAc,CAAd,EAAiBD,OAAjB,GAA2B,KAAKhC,WAFlC;AAGD,KALD;;AAOA,UAAMmC,GAAG,GAAGpO,KAAK,IAAI;AACnB,UAAI,KAAKyM,aAAL,KAAuBzM,KAAK,CAACgO,WAAN,KAAsBvC,gBAAtB,IAA0CzL,KAAK,CAACgO,WAAN,KAAsBxC,kBAAvF,CAAJ,EAAgH;AAC9G,aAAKU,WAAL,GAAmBlM,KAAK,CAACiO,OAAN,GAAgB,KAAKhC,WAAxC;AACD;;AAED,WAAKwB,YAAL;;AACA,UAAI,KAAKtB,OAAL,CAAahD,KAAb,KAAuB,OAA3B,EAAoC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,aAAKA,KAAL;;AACA,YAAI,KAAK6C,YAAT,EAAuB;AACrBqC,UAAAA,YAAY,CAAC,KAAKrC,YAAN,CAAZ;AACD;;AAED,aAAKA,YAAL,GAAoB1N,UAAU,CAAC0B,KAAK,IAAI,KAAK+M,KAAL,CAAW/M,KAAX,CAAV,EAA6B6I,sBAAsB,GAAG,KAAKsD,OAAL,CAAanD,QAAnE,CAA9B;AACD;AACF,KAtBD;;AAwBA5S,IAAAA,cAAc,CAACC,IAAf,CAAoB6U,iBAApB,EAAuC,KAAKtG,QAA5C,EAAsD7J,OAAtD,CAA8DuT,OAAO,IAAI;AACvEnO,MAAAA,YAAY,CAACiC,EAAb,CAAgBkM,OAAhB,EAAyBhE,gBAAzB,EAA2CiE,CAAC,IAAIA,CAAC,CAAC3K,cAAF,EAAhD;AACD,KAFD;;AAIA,QAAI,KAAK6I,aAAT,EAAwB;AACtBtM,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BwF,iBAA/B,EAAkDpK,KAAK,IAAI+N,KAAK,CAAC/N,KAAD,CAAhE;AACAG,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+ByF,eAA/B,EAAgDrK,KAAK,IAAIoO,GAAG,CAACpO,KAAD,CAA5D;;AAEA,WAAK4E,QAAL,CAAchJ,SAAd,CAAwB4S,GAAxB,CAA4B1D,wBAA5B;AACD,KALD,MAKO;AACL3K,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BqF,gBAA/B,EAAiDjK,KAAK,IAAI+N,KAAK,CAAC/N,KAAD,CAA/D;AACAG,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BsF,eAA/B,EAAgDlK,KAAK,IAAImO,IAAI,CAACnO,KAAD,CAA7D;AACAG,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BuF,cAA/B,EAA+CnK,KAAK,IAAIoO,GAAG,CAACpO,KAAD,CAA3D;AACD;AACF;;AAED6N,EAAAA,QAAQ,CAAC7N,KAAD,EAAQ;AACd,QAAI,kBAAkB3E,IAAlB,CAAuB2E,KAAK,CAAC5B,MAAN,CAAaqQ,OAApC,CAAJ,EAAkD;AAChD;AACD;;AAED,UAAMb,SAAS,GAAGjE,gBAAgB,CAAC3J,KAAK,CAACyD,GAAP,CAAlC;;AACA,QAAImK,SAAJ,EAAe;AACb5N,MAAAA,KAAK,CAAC4D,cAAN;;AACA,WAAKgJ,MAAL,CAAYgB,SAAZ;AACD;AACF;;AAEDL,EAAAA,aAAa,CAAChX,OAAD,EAAU;AACrB,SAAKoV,MAAL,GAAcpV,OAAO,IAAIA,OAAO,CAACgB,UAAnB,GACZnB,cAAc,CAACC,IAAf,CAAoB4U,aAApB,EAAmC1U,OAAO,CAACgB,UAA3C,CADY,GAEZ,EAFF;AAIA,WAAO,KAAKoU,MAAL,CAAY9M,OAAZ,CAAoBtI,OAApB,CAAP;AACD;;AAEDmY,EAAAA,eAAe,CAAClB,KAAD,EAAQ/O,aAAR,EAAuB;AACpC,UAAMkQ,MAAM,GAAGnB,KAAK,KAAKjE,UAAzB;AACA,WAAOhL,oBAAoB,CAAC,KAAKoN,MAAN,EAAclN,aAAd,EAA6BkQ,MAA7B,EAAqC,KAAKxC,OAAL,CAAa/C,IAAlD,CAA3B;AACD;;AAEDwF,EAAAA,kBAAkB,CAAClN,aAAD,EAAgBmN,kBAAhB,EAAoC;AACpD,UAAMC,WAAW,GAAG,KAAKvB,aAAL,CAAmB7L,aAAnB,CAApB;;AACA,UAAMqN,SAAS,GAAG,KAAKxB,aAAL,CAAmBnX,cAAc,CAACW,OAAf,CAAuBiU,oBAAvB,EAA6C,KAAKpG,QAAlD,CAAnB,CAAlB;;AAEA,WAAOzE,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoCgF,WAApC,EAAiD;AACtDlI,MAAAA,aADsD;AAEtDkM,MAAAA,SAAS,EAAEiB,kBAF2C;AAGtDvK,MAAAA,IAAI,EAAEyK,SAHgD;AAItD1B,MAAAA,EAAE,EAAEyB;AAJkD,KAAjD,CAAP;AAMD;;AAEDE,EAAAA,0BAA0B,CAACzY,OAAD,EAAU;AAClC,QAAI,KAAK8V,kBAAT,EAA6B;AAC3B,YAAM4C,eAAe,GAAG7Y,cAAc,CAACW,OAAf,CAAuBgU,iBAAvB,EAAwC,KAAKsB,kBAA7C,CAAxB;AAEA4C,MAAAA,eAAe,CAACrT,SAAhB,CAA0B2I,MAA1B,CAAiCsC,mBAAjC;AACAoI,MAAAA,eAAe,CAACvH,eAAhB,CAAgC,cAAhC;AAEA,YAAMwH,UAAU,GAAG9Y,cAAc,CAACC,IAAf,CAAoBgV,kBAApB,EAAwC,KAAKgB,kBAA7C,CAAnB;;AAEA,WAAK,IAAI5L,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGyO,UAAU,CAAC1U,MAA/B,EAAuCiG,CAAC,EAAxC,EAA4C;AAC1C,YAAI1G,MAAM,CAACoV,QAAP,CAAgBD,UAAU,CAACzO,CAAD,CAAV,CAAcxH,YAAd,CAA2B,kBAA3B,CAAhB,EAAgE,EAAhE,MAAwE,KAAKsU,aAAL,CAAmBhX,OAAnB,CAA5E,EAAyG;AACvG2Y,UAAAA,UAAU,CAACzO,CAAD,CAAV,CAAc7E,SAAd,CAAwB4S,GAAxB,CAA4B3H,mBAA5B;AACAqI,UAAAA,UAAU,CAACzO,CAAD,CAAV,CAAcwG,YAAd,CAA2B,cAA3B,EAA2C,MAA3C;AACA;AACD;AACF;AACF;AACF;;AAEDgG,EAAAA,eAAe,GAAG;AAChB,UAAM1W,OAAO,GAAG,KAAKsV,cAAL,IAAuBzV,cAAc,CAACW,OAAf,CAAuBiU,oBAAvB,EAA6C,KAAKpG,QAAlD,CAAvC;;AAEA,QAAI,CAACrO,OAAL,EAAc;AACZ;AACD;;AAED,UAAM6Y,eAAe,GAAGrV,MAAM,CAACoV,QAAP,CAAgB5Y,OAAO,CAAC0C,YAAR,CAAqB,kBAArB,CAAhB,EAA0D,EAA1D,CAAxB;;AAEA,QAAImW,eAAJ,EAAqB;AACnB,WAAKjD,OAAL,CAAakD,eAAb,GAA+B,KAAKlD,OAAL,CAAakD,eAAb,IAAgC,KAAKlD,OAAL,CAAanD,QAA5E;AACA,WAAKmD,OAAL,CAAanD,QAAb,GAAwBoG,eAAxB;AACD,KAHD,MAGO;AACL,WAAKjD,OAAL,CAAanD,QAAb,GAAwB,KAAKmD,OAAL,CAAakD,eAAb,IAAgC,KAAKlD,OAAL,CAAanD,QAArE;AACD;AACF;;AAED4D,EAAAA,MAAM,CAAC0C,gBAAD,EAAmB/Y,OAAnB,EAA4B;AAChC,UAAMiX,KAAK,GAAG,KAAK+B,iBAAL,CAAuBD,gBAAvB,CAAd;;AACA,UAAM7Q,aAAa,GAAGrI,cAAc,CAACW,OAAf,CAAuBiU,oBAAvB,EAA6C,KAAKpG,QAAlD,CAAtB;;AACA,UAAM4K,kBAAkB,GAAG,KAAKjC,aAAL,CAAmB9O,aAAnB,CAA3B;;AACA,UAAMgR,WAAW,GAAGlZ,OAAO,IAAI,KAAKmY,eAAL,CAAqBlB,KAArB,EAA4B/O,aAA5B,CAA/B;;AAEA,UAAMiR,gBAAgB,GAAG,KAAKnC,aAAL,CAAmBkC,WAAnB,CAAzB;;AACA,UAAME,SAAS,GAAG5N,OAAO,CAAC,KAAK6J,SAAN,CAAzB;AAEA,UAAM+C,MAAM,GAAGnB,KAAK,KAAKjE,UAAzB;AACA,UAAMqG,oBAAoB,GAAGjB,MAAM,GAAGhE,gBAAH,GAAsBD,cAAzD;AACA,UAAMmF,cAAc,GAAGlB,MAAM,GAAG/D,eAAH,GAAqBC,eAAlD;;AACA,UAAMgE,kBAAkB,GAAG,KAAKiB,iBAAL,CAAuBtC,KAAvB,CAA3B;;AAEA,QAAIiC,WAAW,IAAIA,WAAW,CAAC7T,SAAZ,CAAsBC,QAAtB,CAA+BgL,mBAA/B,CAAnB,EAAsE;AACpE,WAAKkF,UAAL,GAAkB,KAAlB;AACA;AACD;;AAED,QAAI,KAAKA,UAAT,EAAqB;AACnB;AACD;;AAED,UAAMgE,UAAU,GAAG,KAAKnB,kBAAL,CAAwBa,WAAxB,EAAqCZ,kBAArC,CAAnB;;AACA,QAAIkB,UAAU,CAAC/M,gBAAf,EAAiC;AAC/B;AACD;;AAED,QAAI,CAACvE,aAAD,IAAkB,CAACgR,WAAvB,EAAoC;AAClC;AACA;AACD;;AAED,SAAK1D,UAAL,GAAkB,IAAlB;;AAEA,QAAI4D,SAAJ,EAAe;AACb,WAAKxG,KAAL;AACD;;AAED,SAAK6F,0BAAL,CAAgCS,WAAhC;;AACA,SAAK5D,cAAL,GAAsB4D,WAAtB;;AAEA,UAAMO,gBAAgB,GAAG,MAAM;AAC7B7P,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoCiF,UAApC,EAAgD;AAC9CnI,QAAAA,aAAa,EAAE+N,WAD+B;AAE9C7B,QAAAA,SAAS,EAAEiB,kBAFmC;AAG9CvK,QAAAA,IAAI,EAAEkL,kBAHwC;AAI9CnC,QAAAA,EAAE,EAAEqC;AAJ0C,OAAhD;AAMD,KAPD;;AASA,QAAI,KAAK9K,QAAL,CAAchJ,SAAd,CAAwBC,QAAxB,CAAiC4O,gBAAjC,CAAJ,EAAwD;AACtDgF,MAAAA,WAAW,CAAC7T,SAAZ,CAAsB4S,GAAtB,CAA0BqB,cAA1B;AAEAvT,MAAAA,MAAM,CAACmT,WAAD,CAAN;AAEAhR,MAAAA,aAAa,CAAC7C,SAAd,CAAwB4S,GAAxB,CAA4BoB,oBAA5B;AACAH,MAAAA,WAAW,CAAC7T,SAAZ,CAAsB4S,GAAtB,CAA0BoB,oBAA1B;;AAEA,YAAMK,gBAAgB,GAAG,MAAM;AAC7BR,QAAAA,WAAW,CAAC7T,SAAZ,CAAsB2I,MAAtB,CAA6BqL,oBAA7B,EAAmDC,cAAnD;AACAJ,QAAAA,WAAW,CAAC7T,SAAZ,CAAsB4S,GAAtB,CAA0B3H,mBAA1B;AAEApI,QAAAA,aAAa,CAAC7C,SAAd,CAAwB2I,MAAxB,CAA+BsC,mBAA/B,EAAkDgJ,cAAlD,EAAkED,oBAAlE;AAEA,aAAK7D,UAAL,GAAkB,KAAlB;AAEAzN,QAAAA,UAAU,CAAC0R,gBAAD,EAAmB,CAAnB,CAAV;AACD,OATD;;AAWA,WAAK7K,cAAL,CAAoB8K,gBAApB,EAAsCxR,aAAtC,EAAqD,IAArD;AACD,KApBD,MAoBO;AACLA,MAAAA,aAAa,CAAC7C,SAAd,CAAwB2I,MAAxB,CAA+BsC,mBAA/B;AACA4I,MAAAA,WAAW,CAAC7T,SAAZ,CAAsB4S,GAAtB,CAA0B3H,mBAA1B;AAEA,WAAKkF,UAAL,GAAkB,KAAlB;AACAiE,MAAAA,gBAAgB;AACjB;;AAED,QAAIL,SAAJ,EAAe;AACb,WAAK5C,KAAL;AACD;AACF;;AAEDwC,EAAAA,iBAAiB,CAAC3B,SAAD,EAAY;AAC3B,QAAI,CAAC,CAAClE,eAAD,EAAkBD,cAAlB,EAAkCtQ,QAAlC,CAA2CyU,SAA3C,CAAL,EAA4D;AAC1D,aAAOA,SAAP;AACD;;AAED,QAAI5Q,KAAK,EAAT,EAAa;AACX,aAAO4Q,SAAS,KAAKnE,cAAd,GAA+BD,UAA/B,GAA4CD,UAAnD;AACD;;AAED,WAAOqE,SAAS,KAAKnE,cAAd,GAA+BF,UAA/B,GAA4CC,UAAnD;AACD;;AAEDsG,EAAAA,iBAAiB,CAACtC,KAAD,EAAQ;AACvB,QAAI,CAAC,CAACjE,UAAD,EAAaC,UAAb,EAAyBrQ,QAAzB,CAAkCqU,KAAlC,CAAL,EAA+C;AAC7C,aAAOA,KAAP;AACD;;AAED,QAAIxQ,KAAK,EAAT,EAAa;AACX,aAAOwQ,KAAK,KAAKhE,UAAV,GAAuBC,cAAvB,GAAwCC,eAA/C;AACD;;AAED,WAAO8D,KAAK,KAAKhE,UAAV,GAAuBE,eAAvB,GAAyCD,cAAhD;AACD,GArYkC;;;AAyYX,SAAjByG,iBAAiB,CAAC3Z,OAAD,EAAUoE,MAAV,EAAkB;AACxC,UAAM+L,IAAI,GAAGgF,QAAQ,CAACpG,mBAAT,CAA6B/O,OAA7B,EAAsCoE,MAAtC,CAAb;AAEA,QAAI;AAAEwR,MAAAA;AAAF,QAAczF,IAAlB;;AACA,QAAI,OAAO/L,MAAP,KAAkB,QAAtB,EAAgC;AAC9BwR,MAAAA,OAAO,GAAG,EACR,GAAGA,OADK;AAER,WAAGxR;AAFK,OAAV;AAID;;AAED,UAAMwV,MAAM,GAAG,OAAOxV,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsCwR,OAAO,CAACjD,KAA7D;;AAEA,QAAI,OAAOvO,MAAP,KAAkB,QAAtB,EAAgC;AAC9B+L,MAAAA,IAAI,CAAC2G,EAAL,CAAQ1S,MAAR;AACD,KAFD,MAEO,IAAI,OAAOwV,MAAP,KAAkB,QAAtB,EAAgC;AACrC,UAAI,OAAOzJ,IAAI,CAACyJ,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAI7U,SAAJ,CAAe,oBAAmB6U,MAAO,GAAzC,CAAN;AACD;;AAEDzJ,MAAAA,IAAI,CAACyJ,MAAD,CAAJ;AACD,KANM,MAMA,IAAIhE,OAAO,CAACnD,QAAR,IAAoBmD,OAAO,CAACiE,IAAhC,EAAsC;AAC3C1J,MAAAA,IAAI,CAACyC,KAAL;AACAzC,MAAAA,IAAI,CAACqG,KAAL;AACD;AACF;;AAEqB,SAAftP,eAAe,CAAC9C,MAAD,EAAS;AAC7B,WAAO,KAAK8L,IAAL,CAAU,YAAY;AAC3BiF,MAAAA,QAAQ,CAACwE,iBAAT,CAA2B,IAA3B,EAAiCvV,MAAjC;AACD,KAFM,CAAP;AAGD;;AAEyB,SAAnB0V,mBAAmB,CAACrQ,KAAD,EAAQ;AAChC,UAAM5B,MAAM,GAAG5E,sBAAsB,CAAC,IAAD,CAArC;;AAEA,QAAI,CAAC4E,MAAD,IAAW,CAACA,MAAM,CAACxC,SAAP,CAAiBC,QAAjB,CAA0B2O,mBAA1B,CAAhB,EAAgE;AAC9D;AACD;;AAED,UAAM7P,MAAM,GAAG,EACb,GAAG4M,WAAW,CAACI,iBAAZ,CAA8BvJ,MAA9B,CADU;AAEb,SAAGmJ,WAAW,CAACI,iBAAZ,CAA8B,IAA9B;AAFU,KAAf;AAIA,UAAM2I,UAAU,GAAG,KAAKrX,YAAL,CAAkB,kBAAlB,CAAnB;;AAEA,QAAIqX,UAAJ,EAAgB;AACd3V,MAAAA,MAAM,CAACqO,QAAP,GAAkB,KAAlB;AACD;;AAED0C,IAAAA,QAAQ,CAACwE,iBAAT,CAA2B9R,MAA3B,EAAmCzD,MAAnC;;AAEA,QAAI2V,UAAJ,EAAgB;AACd5E,MAAAA,QAAQ,CAACrG,WAAT,CAAqBjH,MAArB,EAA6BiP,EAA7B,CAAgCiD,UAAhC;AACD;;AAEDtQ,IAAAA,KAAK,CAAC4D,cAAN;AACD;;AAlckC;AAqcrC;AACA;AACA;AACA;AACA;;;AAEAzD,YAAY,CAACiC,EAAb,CAAgB5L,QAAhB,EAA0BoP,sBAA1B,EAAgD0F,mBAAhD,EAAqEI,QAAQ,CAAC2E,mBAA9E;AAEAlQ,YAAY,CAACiC,EAAb,CAAgBxI,MAAhB,EAAwB2Q,qBAAxB,EAA6C,MAAM;AACjD,QAAMgG,SAAS,GAAGna,cAAc,CAACC,IAAf,CAAoBkV,kBAApB,CAAlB;;AAEA,OAAK,IAAI9K,CAAC,GAAG,CAAR,EAAWK,GAAG,GAAGyP,SAAS,CAAC/V,MAAhC,EAAwCiG,CAAC,GAAGK,GAA5C,EAAiDL,CAAC,EAAlD,EAAsD;AACpDiL,IAAAA,QAAQ,CAACwE,iBAAT,CAA2BK,SAAS,CAAC9P,CAAD,CAApC,EAAyCiL,QAAQ,CAACrG,WAAT,CAAqBkL,SAAS,CAAC9P,CAAD,CAA9B,CAAzC;AACD;AACF,CAND;AAQA;AACA;AACA;AACA;AACA;AACA;;AAEAvD,kBAAkB,CAACwO,QAAD,CAAlB;;ACvkBA;AACA;AACA;AACA;AACA;AACA;AAgBA;AACA;AACA;AACA;AACA;;AAEA,MAAMpO,MAAI,GAAG,UAAb;AACA,MAAMwH,UAAQ,GAAG,aAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMU,cAAY,GAAG,WAArB;AAEA,MAAMuD,SAAO,GAAG;AACd/B,EAAAA,MAAM,EAAE,IADM;AAEdwJ,EAAAA,MAAM,EAAE;AAFM,CAAhB;AAKA,MAAMlH,aAAW,GAAG;AAClBtC,EAAAA,MAAM,EAAE,SADU;AAElBwJ,EAAAA,MAAM,EAAE;AAFU,CAApB;AAKA,MAAMC,YAAU,GAAI,OAAMzL,WAAU,EAApC;AACA,MAAM0L,aAAW,GAAI,QAAO1L,WAAU,EAAtC;AACA,MAAM2L,YAAU,GAAI,OAAM3L,WAAU,EAApC;AACA,MAAM4L,cAAY,GAAI,SAAQ5L,WAAU,EAAxC;AACA,MAAMY,sBAAoB,GAAI,QAAOZ,WAAU,GAAEQ,cAAa,EAA9D;AAEA,MAAMO,iBAAe,GAAG,MAAxB;AACA,MAAM8K,mBAAmB,GAAG,UAA5B;AACA,MAAMC,qBAAqB,GAAG,YAA9B;AACA,MAAMC,oBAAoB,GAAG,WAA7B;AAEA,MAAMC,KAAK,GAAG,OAAd;AACA,MAAMC,MAAM,GAAG,QAAf;AAEA,MAAMC,gBAAgB,GAAG,oBAAzB;AACA,MAAMpK,sBAAoB,GAAG,6BAA7B;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMqK,QAAN,SAAuBzM,aAAvB,CAAqC;AACnCC,EAAAA,WAAW,CAACpO,OAAD,EAAUoE,MAAV,EAAkB;AAC3B,UAAMpE,OAAN;AAEA,SAAK6a,gBAAL,GAAwB,KAAxB;AACA,SAAKjF,OAAL,GAAe,KAAKC,UAAL,CAAgBzR,MAAhB,CAAf;AACA,SAAK0W,aAAL,GAAqBjb,cAAc,CAACC,IAAf,CAClB,GAAEyQ,sBAAqB,WAAU,KAAKlC,QAAL,CAAc0M,EAAG,KAAnD,GACC,GAAExK,sBAAqB,qBAAoB,KAAKlC,QAAL,CAAc0M,EAAG,IAF1C,CAArB;AAKA,UAAMC,UAAU,GAAGnb,cAAc,CAACC,IAAf,CAAoByQ,sBAApB,CAAnB;;AAEA,SAAK,IAAIrG,CAAC,GAAG,CAAR,EAAWK,GAAG,GAAGyQ,UAAU,CAAC/W,MAAjC,EAAyCiG,CAAC,GAAGK,GAA7C,EAAkDL,CAAC,EAAnD,EAAuD;AACrD,YAAM+Q,IAAI,GAAGD,UAAU,CAAC9Q,CAAD,CAAvB;AACA,YAAMnK,QAAQ,GAAGiD,sBAAsB,CAACiY,IAAD,CAAvC;AACA,YAAMC,aAAa,GAAGrb,cAAc,CAACC,IAAf,CAAoBC,QAApB,EACnBY,MADmB,CACZwa,SAAS,IAAIA,SAAS,KAAK,KAAK9M,QADpB,CAAtB;;AAGA,UAAItO,QAAQ,KAAK,IAAb,IAAqBmb,aAAa,CAACjX,MAAvC,EAA+C;AAC7C,aAAKmX,SAAL,GAAiBrb,QAAjB;;AACA,aAAK+a,aAAL,CAAmB1Z,IAAnB,CAAwB6Z,IAAxB;AACD;AACF;;AAED,SAAKI,OAAL,GAAe,KAAKzF,OAAL,CAAaqE,MAAb,GAAsB,KAAKqB,UAAL,EAAtB,GAA0C,IAAzD;;AAEA,QAAI,CAAC,KAAK1F,OAAL,CAAaqE,MAAlB,EAA0B;AACxB,WAAKsB,yBAAL,CAA+B,KAAKlN,QAApC,EAA8C,KAAKyM,aAAnD;AACD;;AAED,QAAI,KAAKlF,OAAL,CAAanF,MAAjB,EAAyB;AACvB,WAAKA,MAAL;AACD;AACF,GAlCkC;;;AAsCjB,aAAP+B,OAAO,GAAG;AACnB,WAAOA,SAAP;AACD;;AAEc,aAAJzL,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD,GA5CkC;;;AAgDnC0J,EAAAA,MAAM,GAAG;AACP,QAAI,KAAKpC,QAAL,CAAchJ,SAAd,CAAwBC,QAAxB,CAAiCkK,iBAAjC,CAAJ,EAAuD;AACrD,WAAKgM,IAAL;AACD,KAFD,MAEO;AACL,WAAKC,IAAL;AACD;AACF;;AAEDA,EAAAA,IAAI,GAAG;AACL,QAAI,KAAKZ,gBAAL,IAAyB,KAAKxM,QAAL,CAAchJ,SAAd,CAAwBC,QAAxB,CAAiCkK,iBAAjC,CAA7B,EAAgF;AAC9E;AACD;;AAED,QAAIkM,OAAJ;AACA,QAAIC,WAAJ;;AAEA,QAAI,KAAKN,OAAT,EAAkB;AAChBK,MAAAA,OAAO,GAAG7b,cAAc,CAACC,IAAf,CAAoB6a,gBAApB,EAAsC,KAAKU,OAA3C,EACP1a,MADO,CACAsa,IAAI,IAAI;AACd,YAAI,OAAO,KAAKrF,OAAL,CAAaqE,MAApB,KAA+B,QAAnC,EAA6C;AAC3C,iBAAOgB,IAAI,CAACvY,YAAL,CAAkB,gBAAlB,MAAwC,KAAKkT,OAAL,CAAaqE,MAA5D;AACD;;AAED,eAAOgB,IAAI,CAAC5V,SAAL,CAAeC,QAAf,CAAwBgV,mBAAxB,CAAP;AACD,OAPO,CAAV;;AASA,UAAIoB,OAAO,CAACzX,MAAR,KAAmB,CAAvB,EAA0B;AACxByX,QAAAA,OAAO,GAAG,IAAV;AACD;AACF;;AAED,UAAME,SAAS,GAAG/b,cAAc,CAACW,OAAf,CAAuB,KAAK4a,SAA5B,CAAlB;;AACA,QAAIM,OAAJ,EAAa;AACX,YAAMG,cAAc,GAAGH,OAAO,CAAC5b,IAAR,CAAamb,IAAI,IAAIW,SAAS,KAAKX,IAAnC,CAAvB;AACAU,MAAAA,WAAW,GAAGE,cAAc,GAAGjB,QAAQ,CAAC9L,WAAT,CAAqB+M,cAArB,CAAH,GAA0C,IAAtE;;AAEA,UAAIF,WAAW,IAAIA,WAAW,CAACd,gBAA/B,EAAiD;AAC/C;AACD;AACF;;AAED,UAAMiB,UAAU,GAAGlS,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC6L,YAApC,CAAnB;;AACA,QAAI4B,UAAU,CAACrP,gBAAf,EAAiC;AAC/B;AACD;;AAED,QAAIiP,OAAJ,EAAa;AACXA,MAAAA,OAAO,CAAClX,OAAR,CAAgBuX,UAAU,IAAI;AAC5B,YAAIH,SAAS,KAAKG,UAAlB,EAA8B;AAC5BnB,UAAAA,QAAQ,CAACoB,iBAAT,CAA2BD,UAA3B,EAAuC,MAAvC;AACD;;AAED,YAAI,CAACJ,WAAL,EAAkB;AAChBrN,UAAAA,IAAI,CAACd,GAAL,CAASuO,UAAT,EAAqBxN,UAArB,EAA+B,IAA/B;AACD;AACF,OARD;AASD;;AAED,UAAM0N,SAAS,GAAG,KAAKC,aAAL,EAAlB;;AAEA,SAAK7N,QAAL,CAAchJ,SAAd,CAAwB2I,MAAxB,CAA+BsM,mBAA/B;;AACA,SAAKjM,QAAL,CAAchJ,SAAd,CAAwB4S,GAAxB,CAA4BsC,qBAA5B;;AAEA,SAAKlM,QAAL,CAAc8N,KAAd,CAAoBF,SAApB,IAAiC,CAAjC;;AAEA,QAAI,KAAKnB,aAAL,CAAmB7W,MAAvB,EAA+B;AAC7B,WAAK6W,aAAL,CAAmBtW,OAAnB,CAA2BxE,OAAO,IAAI;AACpCA,QAAAA,OAAO,CAACqF,SAAR,CAAkB2I,MAAlB,CAAyBwM,oBAAzB;AACAxa,QAAAA,OAAO,CAAC0Q,YAAR,CAAqB,eAArB,EAAsC,IAAtC;AACD,OAHD;AAID;;AAED,SAAK0L,gBAAL,CAAsB,IAAtB;;AAEA,UAAMC,QAAQ,GAAG,MAAM;AACrB,WAAKhO,QAAL,CAAchJ,SAAd,CAAwB2I,MAAxB,CAA+BuM,qBAA/B;;AACA,WAAKlM,QAAL,CAAchJ,SAAd,CAAwB4S,GAAxB,CAA4BqC,mBAA5B,EAAiD9K,iBAAjD;;AAEA,WAAKnB,QAAL,CAAc8N,KAAd,CAAoBF,SAApB,IAAiC,EAAjC;AAEA,WAAKG,gBAAL,CAAsB,KAAtB;AAEAxS,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC8L,aAApC;AACD,KATD;;AAWA,UAAMmC,oBAAoB,GAAGL,SAAS,CAAC,CAAD,CAAT,CAAajX,WAAb,KAA6BiX,SAAS,CAAC/P,KAAV,CAAgB,CAAhB,CAA1D;AACA,UAAMqQ,UAAU,GAAI,SAAQD,oBAAqB,EAAjD;;AAEA,SAAK1N,cAAL,CAAoByN,QAApB,EAA8B,KAAKhO,QAAnC,EAA6C,IAA7C;;AACA,SAAKA,QAAL,CAAc8N,KAAd,CAAoBF,SAApB,IAAkC,GAAE,KAAK5N,QAAL,CAAckO,UAAd,CAA0B,IAA9D;AACD;;AAEDf,EAAAA,IAAI,GAAG;AACL,QAAI,KAAKX,gBAAL,IAAyB,CAAC,KAAKxM,QAAL,CAAchJ,SAAd,CAAwBC,QAAxB,CAAiCkK,iBAAjC,CAA9B,EAAiF;AAC/E;AACD;;AAED,UAAMsM,UAAU,GAAGlS,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC+L,YAApC,CAAnB;;AACA,QAAI0B,UAAU,CAACrP,gBAAf,EAAiC;AAC/B;AACD;;AAED,UAAMwP,SAAS,GAAG,KAAKC,aAAL,EAAlB;;AAEA,SAAK7N,QAAL,CAAc8N,KAAd,CAAoBF,SAApB,IAAkC,GAAE,KAAK5N,QAAL,CAAcuD,qBAAd,GAAsCqK,SAAtC,CAAiD,IAArF;AAEAlW,IAAAA,MAAM,CAAC,KAAKsI,QAAN,CAAN;;AAEA,SAAKA,QAAL,CAAchJ,SAAd,CAAwB4S,GAAxB,CAA4BsC,qBAA5B;;AACA,SAAKlM,QAAL,CAAchJ,SAAd,CAAwB2I,MAAxB,CAA+BsM,mBAA/B,EAAoD9K,iBAApD;;AAEA,UAAMgN,kBAAkB,GAAG,KAAK1B,aAAL,CAAmB7W,MAA9C;;AACA,QAAIuY,kBAAkB,GAAG,CAAzB,EAA4B;AAC1B,WAAK,IAAItS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsS,kBAApB,EAAwCtS,CAAC,EAAzC,EAA6C;AAC3C,cAAMkC,OAAO,GAAG,KAAK0O,aAAL,CAAmB5Q,CAAnB,CAAhB;AACA,cAAM+Q,IAAI,GAAGhY,sBAAsB,CAACmJ,OAAD,CAAnC;;AAEA,YAAI6O,IAAI,IAAI,CAACA,IAAI,CAAC5V,SAAL,CAAeC,QAAf,CAAwBkK,iBAAxB,CAAb,EAAuD;AACrDpD,UAAAA,OAAO,CAAC/G,SAAR,CAAkB4S,GAAlB,CAAsBuC,oBAAtB;AACApO,UAAAA,OAAO,CAACsE,YAAR,CAAqB,eAArB,EAAsC,KAAtC;AACD;AACF;AACF;;AAED,SAAK0L,gBAAL,CAAsB,IAAtB;;AAEA,UAAMC,QAAQ,GAAG,MAAM;AACrB,WAAKD,gBAAL,CAAsB,KAAtB;;AACA,WAAK/N,QAAL,CAAchJ,SAAd,CAAwB2I,MAAxB,CAA+BuM,qBAA/B;;AACA,WAAKlM,QAAL,CAAchJ,SAAd,CAAwB4S,GAAxB,CAA4BqC,mBAA5B;;AACA1Q,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoCgM,cAApC;AACD,KALD;;AAOA,SAAKhM,QAAL,CAAc8N,KAAd,CAAoBF,SAApB,IAAiC,EAAjC;;AAEA,SAAKrN,cAAL,CAAoByN,QAApB,EAA8B,KAAKhO,QAAnC,EAA6C,IAA7C;AACD;;AAED+N,EAAAA,gBAAgB,CAACK,eAAD,EAAkB;AAChC,SAAK5B,gBAAL,GAAwB4B,eAAxB;AACD,GA5LkC;;;AAgMnC5G,EAAAA,UAAU,CAACzR,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,EACP,GAAGoO,SADI;AAEP,SAAGpO;AAFI,KAAT;AAIAA,IAAAA,MAAM,CAACqM,MAAP,GAAgBjF,OAAO,CAACpH,MAAM,CAACqM,MAAR,CAAvB,CALiB;;AAMjBvM,IAAAA,eAAe,CAAC6C,MAAD,EAAO3C,MAAP,EAAe2O,aAAf,CAAf;AACA,WAAO3O,MAAP;AACD;;AAED8X,EAAAA,aAAa,GAAG;AACd,WAAO,KAAK7N,QAAL,CAAchJ,SAAd,CAAwBC,QAAxB,CAAiCmV,KAAjC,IAA0CA,KAA1C,GAAkDC,MAAzD;AACD;;AAEDY,EAAAA,UAAU,GAAG;AACX,QAAI;AAAErB,MAAAA;AAAF,QAAa,KAAKrE,OAAtB;AAEAqE,IAAAA,MAAM,GAAGjW,UAAU,CAACiW,MAAD,CAAnB;AAEA,UAAMla,QAAQ,GAAI,GAAEwQ,sBAAqB,oBAAmB0J,MAAO,IAAnE;AAEApa,IAAAA,cAAc,CAACC,IAAf,CAAoBC,QAApB,EAA8Bka,MAA9B,EACGzV,OADH,CACWxE,OAAO,IAAI;AAClB,YAAM0c,QAAQ,GAAGzZ,sBAAsB,CAACjD,OAAD,CAAvC;;AAEA,WAAKub,yBAAL,CACEmB,QADF,EAEE,CAAC1c,OAAD,CAFF;AAID,KARH;AAUA,WAAOia,MAAP;AACD;;AAEDsB,EAAAA,yBAAyB,CAACvb,OAAD,EAAU2c,YAAV,EAAwB;AAC/C,QAAI,CAAC3c,OAAD,IAAY,CAAC2c,YAAY,CAAC1Y,MAA9B,EAAsC;AACpC;AACD;;AAED,UAAM2Y,MAAM,GAAG5c,OAAO,CAACqF,SAAR,CAAkBC,QAAlB,CAA2BkK,iBAA3B,CAAf;AAEAmN,IAAAA,YAAY,CAACnY,OAAb,CAAqByW,IAAI,IAAI;AAC3B,UAAI2B,MAAJ,EAAY;AACV3B,QAAAA,IAAI,CAAC5V,SAAL,CAAe2I,MAAf,CAAsBwM,oBAAtB;AACD,OAFD,MAEO;AACLS,QAAAA,IAAI,CAAC5V,SAAL,CAAe4S,GAAf,CAAmBuC,oBAAnB;AACD;;AAEDS,MAAAA,IAAI,CAACvK,YAAL,CAAkB,eAAlB,EAAmCkM,MAAnC;AACD,KARD;AASD,GAlPkC;;;AAsPX,SAAjBZ,iBAAiB,CAAChc,OAAD,EAAUoE,MAAV,EAAkB;AACxC,QAAI+L,IAAI,GAAGyK,QAAQ,CAAC9L,WAAT,CAAqB9O,OAArB,CAAX;AACA,UAAM4V,OAAO,GAAG,EACd,GAAGpD,SADW;AAEd,SAAGxB,WAAW,CAACI,iBAAZ,CAA8BpR,OAA9B,CAFW;AAGd,UAAI,OAAOoE,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;AAHc,KAAhB;;AAMA,QAAI,CAAC+L,IAAD,IAASyF,OAAO,CAACnF,MAAjB,IAA2B,OAAOrM,MAAP,KAAkB,QAA7C,IAAyD,YAAYU,IAAZ,CAAiBV,MAAjB,CAA7D,EAAuF;AACrFwR,MAAAA,OAAO,CAACnF,MAAR,GAAiB,KAAjB;AACD;;AAED,QAAI,CAACN,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAIyK,QAAJ,CAAa5a,OAAb,EAAsB4V,OAAtB,CAAP;AACD;;AAED,QAAI,OAAOxR,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,UAAI,OAAO+L,IAAI,CAAC/L,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED+L,MAAAA,IAAI,CAAC/L,MAAD,CAAJ;AACD;AACF;;AAEqB,SAAf8C,eAAe,CAAC9C,MAAD,EAAS;AAC7B,WAAO,KAAK8L,IAAL,CAAU,YAAY;AAC3B0K,MAAAA,QAAQ,CAACoB,iBAAT,CAA2B,IAA3B,EAAiC5X,MAAjC;AACD,KAFM,CAAP;AAGD;;AAnRkC;AAsRrC;AACA;AACA;AACA;AACA;;;AAEAwF,YAAY,CAACiC,EAAb,CAAgB5L,QAAhB,EAA0BoP,sBAA1B,EAAgDkB,sBAAhD,EAAsE,UAAU9G,KAAV,EAAiB;AACrF;AACA,MAAIA,KAAK,CAAC5B,MAAN,CAAaqQ,OAAb,KAAyB,GAAzB,IAAiCzO,KAAK,CAACC,cAAN,IAAwBD,KAAK,CAACC,cAAN,CAAqBwO,OAArB,KAAiC,GAA9F,EAAoG;AAClGzO,IAAAA,KAAK,CAAC4D,cAAN;AACD;;AAED,QAAMwP,WAAW,GAAG7L,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAApB;AACA,QAAMrR,QAAQ,GAAGiD,sBAAsB,CAAC,IAAD,CAAvC;AACA,QAAM8Z,gBAAgB,GAAGjd,cAAc,CAACC,IAAf,CAAoBC,QAApB,CAAzB;AAEA+c,EAAAA,gBAAgB,CAACtY,OAAjB,CAAyBxE,OAAO,IAAI;AAClC,UAAMmQ,IAAI,GAAGyK,QAAQ,CAAC9L,WAAT,CAAqB9O,OAArB,CAAb;AACA,QAAIoE,MAAJ;;AACA,QAAI+L,IAAJ,EAAU;AACR;AACA,UAAIA,IAAI,CAACkL,OAAL,KAAiB,IAAjB,IAAyB,OAAOwB,WAAW,CAAC5C,MAAnB,KAA8B,QAA3D,EAAqE;AACnE9J,QAAAA,IAAI,CAACyF,OAAL,CAAaqE,MAAb,GAAsB4C,WAAW,CAAC5C,MAAlC;AACA9J,QAAAA,IAAI,CAACkL,OAAL,GAAelL,IAAI,CAACmL,UAAL,EAAf;AACD;;AAEDlX,MAAAA,MAAM,GAAG,QAAT;AACD,KARD,MAQO;AACLA,MAAAA,MAAM,GAAGyY,WAAT;AACD;;AAEDjC,IAAAA,QAAQ,CAACoB,iBAAT,CAA2Bhc,OAA3B,EAAoCoE,MAApC;AACD,GAhBD;AAiBD,CA3BD;AA6BA;AACA;AACA;AACA;AACA;AACA;;AAEAuC,kBAAkB,CAACiU,QAAD,CAAlB;;ACjYA;AACA;AACA;AACA;AACA;AACA;AAqBA;AACA;AACA;AACA;AACA;;AAEA,MAAM7T,MAAI,GAAG,UAAb;AACA,MAAMwH,UAAQ,GAAG,aAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMU,cAAY,GAAG,WAArB;AAEA,MAAM8N,YAAU,GAAG,QAAnB;AACA,MAAMC,SAAS,GAAG,OAAlB;AACA,MAAMC,OAAO,GAAG,KAAhB;AACA,MAAMC,YAAY,GAAG,SAArB;AACA,MAAMC,cAAc,GAAG,WAAvB;AACA,MAAMC,kBAAkB,GAAG,CAA3B;;AAEA,MAAMC,cAAc,GAAG,IAAIxY,MAAJ,CAAY,GAAEqY,YAAa,IAAGC,cAAe,IAAGJ,YAAW,EAA3D,CAAvB;AAEA,MAAM3C,YAAU,GAAI,OAAM3L,WAAU,EAApC;AACA,MAAM4L,cAAY,GAAI,SAAQ5L,WAAU,EAAxC;AACA,MAAMyL,YAAU,GAAI,OAAMzL,WAAU,EAApC;AACA,MAAM0L,aAAW,GAAI,QAAO1L,WAAU,EAAtC;AACA,MAAM6O,WAAW,GAAI,QAAO7O,WAAU,EAAtC;AACA,MAAMY,sBAAoB,GAAI,QAAOZ,WAAU,GAAEQ,cAAa,EAA9D;AACA,MAAMsO,sBAAsB,GAAI,UAAS9O,WAAU,GAAEQ,cAAa,EAAlE;AACA,MAAMuO,oBAAoB,GAAI,QAAO/O,WAAU,GAAEQ,cAAa,EAA9D;AAEA,MAAMO,iBAAe,GAAG,MAAxB;AACA,MAAMiO,iBAAiB,GAAG,QAA1B;AACA,MAAMC,kBAAkB,GAAG,SAA3B;AACA,MAAMC,oBAAoB,GAAG,WAA7B;AACA,MAAMC,iBAAiB,GAAG,QAA1B;AAEA,MAAMrN,sBAAoB,GAAG,6BAA7B;AACA,MAAMsN,aAAa,GAAG,gBAAtB;AACA,MAAMC,mBAAmB,GAAG,aAA5B;AACA,MAAMC,sBAAsB,GAAG,6DAA/B;AAEA,MAAMC,aAAa,GAAGvX,KAAK,KAAK,SAAL,GAAiB,WAA5C;AACA,MAAMwX,gBAAgB,GAAGxX,KAAK,KAAK,WAAL,GAAmB,SAAjD;AACA,MAAMyX,gBAAgB,GAAGzX,KAAK,KAAK,YAAL,GAAoB,cAAlD;AACA,MAAM0X,mBAAmB,GAAG1X,KAAK,KAAK,cAAL,GAAsB,YAAvD;AACA,MAAM2X,eAAe,GAAG3X,KAAK,KAAK,YAAL,GAAoB,aAAjD;AACA,MAAM4X,cAAc,GAAG5X,KAAK,KAAK,aAAL,GAAqB,YAAjD;AAEA,MAAM+L,SAAO,GAAG;AACdd,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CADM;AAEd4M,EAAAA,QAAQ,EAAE,iBAFI;AAGdC,EAAAA,SAAS,EAAE,QAHG;AAIdC,EAAAA,OAAO,EAAE,SAJK;AAKdC,EAAAA,YAAY,EAAE,IALA;AAMdC,EAAAA,SAAS,EAAE;AANG,CAAhB;AASA,MAAM3L,aAAW,GAAG;AAClBrB,EAAAA,MAAM,EAAE,yBADU;AAElB4M,EAAAA,QAAQ,EAAE,kBAFQ;AAGlBC,EAAAA,SAAS,EAAE,yBAHO;AAIlBC,EAAAA,OAAO,EAAE,QAJS;AAKlBC,EAAAA,YAAY,EAAE,wBALI;AAMlBC,EAAAA,SAAS,EAAE;AANO,CAApB;AASA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,QAAN,SAAuBxQ,aAAvB,CAAqC;AACnCC,EAAAA,WAAW,CAACpO,OAAD,EAAUoE,MAAV,EAAkB;AAC3B,UAAMpE,OAAN;AAEA,SAAK4e,OAAL,GAAe,IAAf;AACA,SAAKhJ,OAAL,GAAe,KAAKC,UAAL,CAAgBzR,MAAhB,CAAf;AACA,SAAKya,KAAL,GAAa,KAAKC,eAAL,EAAb;AACA,SAAKC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;AAEA,SAAK5I,kBAAL;AACD,GAVkC;;;AAcjB,aAAP5D,OAAO,GAAG;AACnB,WAAOA,SAAP;AACD;;AAEqB,aAAXO,WAAW,GAAG;AACvB,WAAOA,aAAP;AACD;;AAEc,aAAJhM,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD,GAxBkC;;;AA4BnC0J,EAAAA,MAAM,GAAG;AACP,QAAIrL,UAAU,CAAC,KAAKiJ,QAAN,CAAd,EAA+B;AAC7B;AACD;;AAED,UAAM4Q,QAAQ,GAAG,KAAK5Q,QAAL,CAAchJ,SAAd,CAAwBC,QAAxB,CAAiCkK,iBAAjC,CAAjB;;AAEA,QAAIyP,QAAJ,EAAc;AACZ,WAAKzD,IAAL;AACA;AACD;;AAED,SAAKC,IAAL;AACD;;AAEDA,EAAAA,IAAI,GAAG;AACL,QAAIrW,UAAU,CAAC,KAAKiJ,QAAN,CAAV,IAA6B,KAAKwQ,KAAL,CAAWxZ,SAAX,CAAqBC,QAArB,CAA8BkK,iBAA9B,CAAjC,EAAiF;AAC/E;AACD;;AAED,UAAMyK,MAAM,GAAG0E,QAAQ,CAACO,oBAAT,CAA8B,KAAK7Q,QAAnC,CAAf;AACA,UAAMlD,aAAa,GAAG;AACpBA,MAAAA,aAAa,EAAE,KAAKkD;AADA,KAAtB;AAIA,UAAM8Q,SAAS,GAAGvV,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC6L,YAApC,EAAgD/O,aAAhD,CAAlB;;AAEA,QAAIgU,SAAS,CAAC1S,gBAAd,EAAgC;AAC9B;AACD,KAdI;;;AAiBL,QAAI,KAAKsS,SAAT,EAAoB;AAClB/N,MAAAA,WAAW,CAACC,gBAAZ,CAA6B,KAAK4N,KAAlC,EAAyC,QAAzC,EAAmD,MAAnD;AACD,KAFD,MAEO;AACL,UAAI,OAAOO,MAAP,KAAkB,WAAtB,EAAmC;AACjC,cAAM,IAAIra,SAAJ,CAAc,+DAAd,CAAN;AACD;;AAED,UAAIsa,gBAAgB,GAAG,KAAKhR,QAA5B;;AAEA,UAAI,KAAKuH,OAAL,CAAa2I,SAAb,KAA2B,QAA/B,EAAyC;AACvCc,QAAAA,gBAAgB,GAAGpF,MAAnB;AACD,OAFD,MAEO,IAAInW,SAAS,CAAC,KAAK8R,OAAL,CAAa2I,SAAd,CAAb,EAAuC;AAC5Cc,QAAAA,gBAAgB,GAAGrb,UAAU,CAAC,KAAK4R,OAAL,CAAa2I,SAAd,CAA7B;AACD,OAFM,MAEA,IAAI,OAAO,KAAK3I,OAAL,CAAa2I,SAApB,KAAkC,QAAtC,EAAgD;AACrDc,QAAAA,gBAAgB,GAAG,KAAKzJ,OAAL,CAAa2I,SAAhC;AACD;;AAED,YAAME,YAAY,GAAG,KAAKa,gBAAL,EAArB;;AACA,YAAMC,eAAe,GAAGd,YAAY,CAACe,SAAb,CAAuB1f,IAAvB,CAA4B2f,QAAQ,IAAIA,QAAQ,CAAC3Y,IAAT,KAAkB,aAAlB,IAAmC2Y,QAAQ,CAACC,OAAT,KAAqB,KAAhG,CAAxB;AAEA,WAAKd,OAAL,GAAeQ,MAAM,CAACO,YAAP,CAAoBN,gBAApB,EAAsC,KAAKR,KAA3C,EAAkDJ,YAAlD,CAAf;;AAEA,UAAIc,eAAJ,EAAqB;AACnBvO,QAAAA,WAAW,CAACC,gBAAZ,CAA6B,KAAK4N,KAAlC,EAAyC,QAAzC,EAAmD,QAAnD;AACD;AACF,KA1CI;AA6CL;AACA;AACA;;;AACA,QAAI,kBAAkB5e,QAAQ,CAACC,eAA3B,IACF,CAAC+Z,MAAM,CAACjK,OAAP,CAAe8N,mBAAf,CADH,EACwC;AACtC,SAAG3d,MAAH,CAAU,GAAGF,QAAQ,CAACkG,IAAT,CAAczF,QAA3B,EACG8D,OADH,CACWyW,IAAI,IAAIrR,YAAY,CAACiC,EAAb,CAAgBoP,IAAhB,EAAsB,WAAtB,EAAmCnV,IAAnC,CADnB;AAED;;AAED,SAAKuI,QAAL,CAAcuR,KAAd;;AACA,SAAKvR,QAAL,CAAcqC,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;AAEA,SAAKmO,KAAL,CAAWxZ,SAAX,CAAqBoL,MAArB,CAA4BjB,iBAA5B;;AACA,SAAKnB,QAAL,CAAchJ,SAAd,CAAwBoL,MAAxB,CAA+BjB,iBAA/B;;AACA5F,IAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC8L,aAApC,EAAiDhP,aAAjD;AACD;;AAEDqQ,EAAAA,IAAI,GAAG;AACL,QAAIpW,UAAU,CAAC,KAAKiJ,QAAN,CAAV,IAA6B,CAAC,KAAKwQ,KAAL,CAAWxZ,SAAX,CAAqBC,QAArB,CAA8BkK,iBAA9B,CAAlC,EAAkF;AAChF;AACD;;AAED,UAAMrE,aAAa,GAAG;AACpBA,MAAAA,aAAa,EAAE,KAAKkD;AADA,KAAtB;;AAIA,SAAKwR,aAAL,CAAmB1U,aAAnB;AACD;;AAEDqD,EAAAA,OAAO,GAAG;AACR,QAAI,KAAKoQ,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAakB,OAAb;AACD;;AAED,UAAMtR,OAAN;AACD;;AAEDuR,EAAAA,MAAM,GAAG;AACP,SAAKhB,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;AACA,QAAI,KAAKJ,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAamB,MAAb;AACD;AACF,GAlIkC;;;AAsInC3J,EAAAA,kBAAkB,GAAG;AACnBxM,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BiP,WAA/B,EAA4C7T,KAAK,IAAI;AACnDA,MAAAA,KAAK,CAAC4D,cAAN;AACA,WAAKoD,MAAL;AACD,KAHD;AAID;;AAEDoP,EAAAA,aAAa,CAAC1U,aAAD,EAAgB;AAC3B,UAAM6U,SAAS,GAAGpW,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC+L,YAApC,EAAgDjP,aAAhD,CAAlB;;AACA,QAAI6U,SAAS,CAACvT,gBAAd,EAAgC;AAC9B;AACD,KAJ0B;AAO3B;;;AACA,QAAI,kBAAkBxM,QAAQ,CAACC,eAA/B,EAAgD;AAC9C,SAAGC,MAAH,CAAU,GAAGF,QAAQ,CAACkG,IAAT,CAAczF,QAA3B,EACG8D,OADH,CACWyW,IAAI,IAAIrR,YAAY,CAACC,GAAb,CAAiBoR,IAAjB,EAAuB,WAAvB,EAAoCnV,IAApC,CADnB;AAED;;AAED,QAAI,KAAK8Y,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAakB,OAAb;AACD;;AAED,SAAKjB,KAAL,CAAWxZ,SAAX,CAAqB2I,MAArB,CAA4BwB,iBAA5B;;AACA,SAAKnB,QAAL,CAAchJ,SAAd,CAAwB2I,MAAxB,CAA+BwB,iBAA/B;;AACA,SAAKnB,QAAL,CAAcqC,YAAd,CAA2B,eAA3B,EAA4C,OAA5C;;AACAM,IAAAA,WAAW,CAACE,mBAAZ,CAAgC,KAAK2N,KAArC,EAA4C,QAA5C;AACAjV,IAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoCgM,cAApC,EAAkDlP,aAAlD;AACD;;AAED0K,EAAAA,UAAU,CAACzR,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,EACP,GAAG,KAAKgK,WAAL,CAAiBoE,OADb;AAEP,SAAGxB,WAAW,CAACI,iBAAZ,CAA8B,KAAK/C,QAAnC,CAFI;AAGP,SAAGjK;AAHI,KAAT;AAMAF,IAAAA,eAAe,CAAC6C,MAAD,EAAO3C,MAAP,EAAe,KAAKgK,WAAL,CAAiB2E,WAAhC,CAAf;;AAEA,QAAI,OAAO3O,MAAM,CAACma,SAAd,KAA4B,QAA5B,IAAwC,CAACza,SAAS,CAACM,MAAM,CAACma,SAAR,CAAlD,IACF,OAAOna,MAAM,CAACma,SAAP,CAAiB3M,qBAAxB,KAAkD,UADpD,EAEE;AACA;AACA,YAAM,IAAI7M,SAAJ,CAAe,GAAEgC,MAAI,CAAC/B,WAAL,EAAmB,gGAApC,CAAN;AACD;;AAED,WAAOZ,MAAP;AACD;;AAED0a,EAAAA,eAAe,GAAG;AAChB,WAAOjf,cAAc,CAAC2B,IAAf,CAAoB,KAAK6M,QAAzB,EAAmCwP,aAAnC,EAAkD,CAAlD,CAAP;AACD;;AAEDoC,EAAAA,aAAa,GAAG;AACd,UAAMC,cAAc,GAAG,KAAK7R,QAAL,CAAcrN,UAArC;;AAEA,QAAIkf,cAAc,CAAC7a,SAAf,CAAyBC,QAAzB,CAAkCoY,kBAAlC,CAAJ,EAA2D;AACzD,aAAOU,eAAP;AACD;;AAED,QAAI8B,cAAc,CAAC7a,SAAf,CAAyBC,QAAzB,CAAkCqY,oBAAlC,CAAJ,EAA6D;AAC3D,aAAOU,cAAP;AACD,KATa;;;AAYd,UAAM8B,KAAK,GAAG7c,gBAAgB,CAAC,KAAKub,KAAN,CAAhB,CAA6B1Z,gBAA7B,CAA8C,eAA9C,EAA+DpC,IAA/D,OAA0E,KAAxF;;AAEA,QAAImd,cAAc,CAAC7a,SAAf,CAAyBC,QAAzB,CAAkCmY,iBAAlC,CAAJ,EAA0D;AACxD,aAAO0C,KAAK,GAAGlC,gBAAH,GAAsBD,aAAlC;AACD;;AAED,WAAOmC,KAAK,GAAGhC,mBAAH,GAAyBD,gBAArC;AACD;;AAEDc,EAAAA,aAAa,GAAG;AACd,WAAO,KAAK3Q,QAAL,CAAc2B,OAAd,CAAuB,IAAG4N,iBAAkB,EAA5C,MAAmD,IAA1D;AACD;;AAEDwC,EAAAA,UAAU,GAAG;AACX,UAAM;AAAE1O,MAAAA;AAAF,QAAa,KAAKkE,OAAxB;;AAEA,QAAI,OAAOlE,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,aAAOA,MAAM,CAAC5O,KAAP,CAAa,GAAb,EAAkBud,GAAlB,CAAsBxP,GAAG,IAAIrN,MAAM,CAACoV,QAAP,CAAgB/H,GAAhB,EAAqB,EAArB,CAA7B,CAAP;AACD;;AAED,QAAI,OAAOa,MAAP,KAAkB,UAAtB,EAAkC;AAChC,aAAO4O,UAAU,IAAI5O,MAAM,CAAC4O,UAAD,EAAa,KAAKjS,QAAlB,CAA3B;AACD;;AAED,WAAOqD,MAAP;AACD;;AAED4N,EAAAA,gBAAgB,GAAG;AACjB,UAAMiB,qBAAqB,GAAG;AAC5BC,MAAAA,SAAS,EAAE,KAAKP,aAAL,EADiB;AAE5BT,MAAAA,SAAS,EAAE,CAAC;AACV1Y,QAAAA,IAAI,EAAE,iBADI;AAEV2Z,QAAAA,OAAO,EAAE;AACPnC,UAAAA,QAAQ,EAAE,KAAK1I,OAAL,CAAa0I;AADhB;AAFC,OAAD,EAMX;AACExX,QAAAA,IAAI,EAAE,QADR;AAEE2Z,QAAAA,OAAO,EAAE;AACP/O,UAAAA,MAAM,EAAE,KAAK0O,UAAL;AADD;AAFX,OANW;AAFiB,KAA9B,CADiB;;AAkBjB,QAAI,KAAKxK,OAAL,CAAa4I,OAAb,KAAyB,QAA7B,EAAuC;AACrC+B,MAAAA,qBAAqB,CAACf,SAAtB,GAAkC,CAAC;AACjC1Y,QAAAA,IAAI,EAAE,aAD2B;AAEjC4Y,QAAAA,OAAO,EAAE;AAFwB,OAAD,CAAlC;AAID;;AAED,WAAO,EACL,GAAGa,qBADE;AAEL,UAAI,OAAO,KAAK3K,OAAL,CAAa6I,YAApB,KAAqC,UAArC,GAAkD,KAAK7I,OAAL,CAAa6I,YAAb,CAA0B8B,qBAA1B,CAAlD,GAAqG,KAAK3K,OAAL,CAAa6I,YAAtH;AAFK,KAAP;AAID;;AAEDiC,EAAAA,eAAe,CAAC;AAAExT,IAAAA,GAAF;AAAOrF,IAAAA;AAAP,GAAD,EAAkB;AAC/B,UAAM8Y,KAAK,GAAG9gB,cAAc,CAACC,IAAf,CAAoBie,sBAApB,EAA4C,KAAKc,KAAjD,EAAwDle,MAAxD,CAA+DsE,SAA/D,CAAd;;AAEA,QAAI,CAAC0b,KAAK,CAAC1c,MAAX,EAAmB;AACjB;AACD,KAL8B;AAQ/B;;;AACA+D,IAAAA,oBAAoB,CAAC2Y,KAAD,EAAQ9Y,MAAR,EAAgBqF,GAAG,KAAKiQ,cAAxB,EAAwC,CAACwD,KAAK,CAAC/d,QAAN,CAAeiF,MAAf,CAAzC,CAApB,CAAqF+X,KAArF;AACD,GA5QkC;;;AAgRX,SAAjBgB,iBAAiB,CAAC5gB,OAAD,EAAUoE,MAAV,EAAkB;AACxC,UAAM+L,IAAI,GAAGwO,QAAQ,CAAC5P,mBAAT,CAA6B/O,OAA7B,EAAsCoE,MAAtC,CAAb;;AAEA,QAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,UAAI,OAAO+L,IAAI,CAAC/L,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED+L,MAAAA,IAAI,CAAC/L,MAAD,CAAJ;AACD;AACF;;AAEqB,SAAf8C,eAAe,CAAC9C,MAAD,EAAS;AAC7B,WAAO,KAAK8L,IAAL,CAAU,YAAY;AAC3ByO,MAAAA,QAAQ,CAACiC,iBAAT,CAA2B,IAA3B,EAAiCxc,MAAjC;AACD,KAFM,CAAP;AAGD;;AAEgB,SAAVyc,UAAU,CAACpX,KAAD,EAAQ;AACvB,QAAIA,KAAK,KAAKA,KAAK,CAACkH,MAAN,KAAiByM,kBAAjB,IAAwC3T,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAACyD,GAAN,KAAc+P,OAArF,CAAT,EAAyG;AACvG;AACD;;AAED,UAAM6D,OAAO,GAAGjhB,cAAc,CAACC,IAAf,CAAoByQ,sBAApB,CAAhB;;AAEA,SAAK,IAAIrG,CAAC,GAAG,CAAR,EAAWK,GAAG,GAAGuW,OAAO,CAAC7c,MAA9B,EAAsCiG,CAAC,GAAGK,GAA1C,EAA+CL,CAAC,EAAhD,EAAoD;AAClD,YAAM6W,OAAO,GAAGpC,QAAQ,CAAC7P,WAAT,CAAqBgS,OAAO,CAAC5W,CAAD,CAA5B,CAAhB;;AACA,UAAI,CAAC6W,OAAD,IAAYA,OAAO,CAACnL,OAAR,CAAgB8I,SAAhB,KAA8B,KAA9C,EAAqD;AACnD;AACD;;AAED,UAAI,CAACqC,OAAO,CAAC1S,QAAR,CAAiBhJ,SAAjB,CAA2BC,QAA3B,CAAoCkK,iBAApC,CAAL,EAA2D;AACzD;AACD;;AAED,YAAMrE,aAAa,GAAG;AACpBA,QAAAA,aAAa,EAAE4V,OAAO,CAAC1S;AADH,OAAtB;;AAIA,UAAI5E,KAAJ,EAAW;AACT,cAAMuX,YAAY,GAAGvX,KAAK,CAACuX,YAAN,EAArB;AACA,cAAMC,YAAY,GAAGD,YAAY,CAACpe,QAAb,CAAsBme,OAAO,CAAClC,KAA9B,CAArB;;AACA,YACEmC,YAAY,CAACpe,QAAb,CAAsBme,OAAO,CAAC1S,QAA9B,KACC0S,OAAO,CAACnL,OAAR,CAAgB8I,SAAhB,KAA8B,QAA9B,IAA0C,CAACuC,YAD5C,IAECF,OAAO,CAACnL,OAAR,CAAgB8I,SAAhB,KAA8B,SAA9B,IAA2CuC,YAH9C,EAIE;AACA;AACD,SATQ;;;AAYT,YAAIF,OAAO,CAAClC,KAAR,CAAcvZ,QAAd,CAAuBmE,KAAK,CAAC5B,MAA7B,MAA0C4B,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAACyD,GAAN,KAAc+P,OAAzC,IAAqD,qCAAqCnY,IAArC,CAA0C2E,KAAK,CAAC5B,MAAN,CAAaqQ,OAAvD,CAA9F,CAAJ,EAAoK;AAClK;AACD;;AAED,YAAIzO,KAAK,CAACK,IAAN,KAAe,OAAnB,EAA4B;AAC1BqB,UAAAA,aAAa,CAAC+V,UAAd,GAA2BzX,KAA3B;AACD;AACF;;AAEDsX,MAAAA,OAAO,CAAClB,aAAR,CAAsB1U,aAAtB;AACD;AACF;;AAE0B,SAApB+T,oBAAoB,CAAClf,OAAD,EAAU;AACnC,WAAOiD,sBAAsB,CAACjD,OAAD,CAAtB,IAAmCA,OAAO,CAACgB,UAAlD;AACD;;AAE2B,SAArBmgB,qBAAqB,CAAC1X,KAAD,EAAQ;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAI,kBAAkB3E,IAAlB,CAAuB2E,KAAK,CAAC5B,MAAN,CAAaqQ,OAApC,IACFzO,KAAK,CAACyD,GAAN,KAAc8P,SAAd,IAA4BvT,KAAK,CAACyD,GAAN,KAAc6P,YAAd,KAC1BtT,KAAK,CAACyD,GAAN,KAAciQ,cAAd,IAAgC1T,KAAK,CAACyD,GAAN,KAAcgQ,YAA/C,IACCzT,KAAK,CAAC5B,MAAN,CAAamI,OAAb,CAAqB6N,aAArB,CAF0B,CAD1B,GAIF,CAACR,cAAc,CAACvY,IAAf,CAAoB2E,KAAK,CAACyD,GAA1B,CAJH,EAImC;AACjC;AACD;;AAED,UAAM+R,QAAQ,GAAG,KAAK5Z,SAAL,CAAeC,QAAf,CAAwBkK,iBAAxB,CAAjB;;AAEA,QAAI,CAACyP,QAAD,IAAaxV,KAAK,CAACyD,GAAN,KAAc6P,YAA/B,EAA2C;AACzC;AACD;;AAEDtT,IAAAA,KAAK,CAAC4D,cAAN;AACA5D,IAAAA,KAAK,CAAC2X,eAAN;;AAEA,QAAIhc,UAAU,CAAC,IAAD,CAAd,EAAsB;AACpB;AACD;;AAED,UAAMic,eAAe,GAAG,MAAM,KAAKxgB,OAAL,CAAa0P,sBAAb,IAAqC,IAArC,GAA4C1Q,cAAc,CAACwB,IAAf,CAAoB,IAApB,EAA0BkP,sBAA1B,EAAgD,CAAhD,CAA1E;;AAEA,QAAI9G,KAAK,CAACyD,GAAN,KAAc6P,YAAlB,EAA8B;AAC5BsE,MAAAA,eAAe,GAAGzB,KAAlB;AACAjB,MAAAA,QAAQ,CAACkC,UAAT;AACA;AACD;;AAED,QAAIpX,KAAK,CAACyD,GAAN,KAAcgQ,YAAd,IAA8BzT,KAAK,CAACyD,GAAN,KAAciQ,cAAhD,EAAgE;AAC9D,UAAI,CAAC8B,QAAL,EAAe;AACboC,QAAAA,eAAe,GAAGC,KAAlB;AACD;;AAED3C,MAAAA,QAAQ,CAAC7P,WAAT,CAAqBuS,eAAe,EAApC,EAAwCX,eAAxC,CAAwDjX,KAAxD;;AACA;AACD;;AAED,QAAI,CAACwV,QAAD,IAAaxV,KAAK,CAACyD,GAAN,KAAc8P,SAA/B,EAA0C;AACxC2B,MAAAA,QAAQ,CAACkC,UAAT;AACD;AACF;;AArYkC;AAwYrC;AACA;AACA;AACA;AACA;;;AAEAjX,YAAY,CAACiC,EAAb,CAAgB5L,QAAhB,EAA0Bsd,sBAA1B,EAAkDhN,sBAAlD,EAAwEoO,QAAQ,CAACwC,qBAAjF;AACAvX,YAAY,CAACiC,EAAb,CAAgB5L,QAAhB,EAA0Bsd,sBAA1B,EAAkDM,aAAlD,EAAiEc,QAAQ,CAACwC,qBAA1E;AACAvX,YAAY,CAACiC,EAAb,CAAgB5L,QAAhB,EAA0BoP,sBAA1B,EAAgDsP,QAAQ,CAACkC,UAAzD;AACAjX,YAAY,CAACiC,EAAb,CAAgB5L,QAAhB,EAA0Bud,oBAA1B,EAAgDmB,QAAQ,CAACkC,UAAzD;AACAjX,YAAY,CAACiC,EAAb,CAAgB5L,QAAhB,EAA0BoP,sBAA1B,EAAgDkB,sBAAhD,EAAsE,UAAU9G,KAAV,EAAiB;AACrFA,EAAAA,KAAK,CAAC4D,cAAN;AACAsR,EAAAA,QAAQ,CAACiC,iBAAT,CAA2B,IAA3B;AACD,CAHD;AAKA;AACA;AACA;AACA;AACA;AACA;;AAEAja,kBAAkB,CAACgY,QAAD,CAAlB;;AC/fA;AACA;AACA;AACA;AACA;AACA;AAMA,MAAM4C,sBAAsB,GAAG,mDAA/B;AACA,MAAMC,uBAAuB,GAAG,aAAhC;;AAEA,MAAMC,eAAN,CAAsB;AACpBrT,EAAAA,WAAW,GAAG;AACZ,SAAKC,QAAL,GAAgBpO,QAAQ,CAACkG,IAAzB;AACD;;AAEDub,EAAAA,QAAQ,GAAG;AACT;AACA,UAAMC,aAAa,GAAG1hB,QAAQ,CAACC,eAAT,CAAyB0hB,WAA/C;AACA,WAAOvf,IAAI,CAAC+U,GAAL,CAAS/T,MAAM,CAACwe,UAAP,GAAoBF,aAA7B,CAAP;AACD;;AAEDnG,EAAAA,IAAI,GAAG;AACL,UAAMsG,KAAK,GAAG,KAAKJ,QAAL,EAAd;;AACA,SAAKK,gBAAL,GAFK;;;AAIL,SAAKC,qBAAL,CAA2B,KAAK3T,QAAhC,EAA0C,cAA1C,EAA0D4T,eAAe,IAAIA,eAAe,GAAGH,KAA/F,EAJK;;;AAML,SAAKE,qBAAL,CAA2BT,sBAA3B,EAAmD,cAAnD,EAAmEU,eAAe,IAAIA,eAAe,GAAGH,KAAxG;;AACA,SAAKE,qBAAL,CAA2BR,uBAA3B,EAAoD,aAApD,EAAmES,eAAe,IAAIA,eAAe,GAAGH,KAAxG;AACD;;AAEDC,EAAAA,gBAAgB,GAAG;AACjB,SAAKG,qBAAL,CAA2B,KAAK7T,QAAhC,EAA0C,UAA1C;;AACA,SAAKA,QAAL,CAAc8N,KAAd,CAAoBgG,QAApB,GAA+B,QAA/B;AACD;;AAEDH,EAAAA,qBAAqB,CAACjiB,QAAD,EAAWqiB,SAAX,EAAsB9b,QAAtB,EAAgC;AACnD,UAAM+b,cAAc,GAAG,KAAKX,QAAL,EAAvB;;AACA,UAAMY,oBAAoB,GAAGtiB,OAAO,IAAI;AACtC,UAAIA,OAAO,KAAK,KAAKqO,QAAjB,IAA6BhL,MAAM,CAACwe,UAAP,GAAoB7hB,OAAO,CAAC4hB,WAAR,GAAsBS,cAA3E,EAA2F;AACzF;AACD;;AAED,WAAKH,qBAAL,CAA2BliB,OAA3B,EAAoCoiB,SAApC;;AACA,YAAMH,eAAe,GAAG5e,MAAM,CAACC,gBAAP,CAAwBtD,OAAxB,EAAiCoiB,SAAjC,CAAxB;AACApiB,MAAAA,OAAO,CAACmc,KAAR,CAAciG,SAAd,IAA4B,GAAE9b,QAAQ,CAAC9C,MAAM,CAACC,UAAP,CAAkBwe,eAAlB,CAAD,CAAqC,IAA3E;AACD,KARD;;AAUA,SAAKM,0BAAL,CAAgCxiB,QAAhC,EAA0CuiB,oBAA1C;AACD;;AAEDE,EAAAA,KAAK,GAAG;AACN,SAAKC,uBAAL,CAA6B,KAAKpU,QAAlC,EAA4C,UAA5C;;AACA,SAAKoU,uBAAL,CAA6B,KAAKpU,QAAlC,EAA4C,cAA5C;;AACA,SAAKoU,uBAAL,CAA6BlB,sBAA7B,EAAqD,cAArD;;AACA,SAAKkB,uBAAL,CAA6BjB,uBAA7B,EAAsD,aAAtD;AACD;;AAEDU,EAAAA,qBAAqB,CAACliB,OAAD,EAAUoiB,SAAV,EAAqB;AACxC,UAAMM,WAAW,GAAG1iB,OAAO,CAACmc,KAAR,CAAciG,SAAd,CAApB;;AACA,QAAIM,WAAJ,EAAiB;AACf1R,MAAAA,WAAW,CAACC,gBAAZ,CAA6BjR,OAA7B,EAAsCoiB,SAAtC,EAAiDM,WAAjD;AACD;AACF;;AAEDD,EAAAA,uBAAuB,CAAC1iB,QAAD,EAAWqiB,SAAX,EAAsB;AAC3C,UAAME,oBAAoB,GAAGtiB,OAAO,IAAI;AACtC,YAAM2E,KAAK,GAAGqM,WAAW,CAACS,gBAAZ,CAA6BzR,OAA7B,EAAsCoiB,SAAtC,CAAd;;AACA,UAAI,OAAOzd,KAAP,KAAiB,WAArB,EAAkC;AAChC3E,QAAAA,OAAO,CAACmc,KAAR,CAAcwG,cAAd,CAA6BP,SAA7B;AACD,OAFD,MAEO;AACLpR,QAAAA,WAAW,CAACE,mBAAZ,CAAgClR,OAAhC,EAAyCoiB,SAAzC;AACApiB,QAAAA,OAAO,CAACmc,KAAR,CAAciG,SAAd,IAA2Bzd,KAA3B;AACD;AACF,KARD;;AAUA,SAAK4d,0BAAL,CAAgCxiB,QAAhC,EAA0CuiB,oBAA1C;AACD;;AAEDC,EAAAA,0BAA0B,CAACxiB,QAAD,EAAW6iB,QAAX,EAAqB;AAC7C,QAAI9e,SAAS,CAAC/D,QAAD,CAAb,EAAyB;AACvB6iB,MAAAA,QAAQ,CAAC7iB,QAAD,CAAR;AACD,KAFD,MAEO;AACLF,MAAAA,cAAc,CAACC,IAAf,CAAoBC,QAApB,EAA8B,KAAKsO,QAAnC,EAA6C7J,OAA7C,CAAqDoe,QAArD;AACD;AACF;;AAEDC,EAAAA,aAAa,GAAG;AACd,WAAO,KAAKnB,QAAL,KAAkB,CAAzB;AACD;;AA/EmB;;ACdtB;AACA;AACA;AACA;AACA;AACA;AAKA,MAAMlP,SAAO,GAAG;AACdvN,EAAAA,SAAS,EAAE,IADG;AACG;AACjB4J,EAAAA,UAAU,EAAE,KAFE;AAGdc,EAAAA,WAAW,EAAE,MAHC;AAGO;AACrBmT,EAAAA,aAAa,EAAE;AAJD,CAAhB;AAOA,MAAM/P,aAAW,GAAG;AAClB9N,EAAAA,SAAS,EAAE,SADO;AAElB4J,EAAAA,UAAU,EAAE,SAFM;AAGlBc,EAAAA,WAAW,EAAE,kBAHK;AAIlBmT,EAAAA,aAAa,EAAE;AAJG,CAApB;AAMA,MAAM/b,MAAI,GAAG,UAAb;AACA,MAAMgc,mBAAmB,GAAG,gBAA5B;AACA,MAAMxT,iBAAe,GAAG,MAAxB;AACA,MAAMC,iBAAe,GAAG,MAAxB;AAEA,MAAMwT,eAAe,GAAI,gBAAejc,MAAK,EAA7C;;AAEA,MAAMkc,QAAN,CAAe;AACb7U,EAAAA,WAAW,CAAChK,MAAD,EAAS;AAClB,SAAKwR,OAAL,GAAe,KAAKC,UAAL,CAAgBzR,MAAhB,CAAf;AACA,SAAK8e,WAAL,GAAmB,KAAnB;AACA,SAAK7U,QAAL,GAAgB,IAAhB;AACD;;AAEDoN,EAAAA,IAAI,CAACnV,QAAD,EAAW;AACb,QAAI,CAAC,KAAKsP,OAAL,CAAa3Q,SAAlB,EAA6B;AAC3BoC,MAAAA,OAAO,CAACf,QAAD,CAAP;AACA;AACD;;AAED,SAAK6c,OAAL;;AAEA,QAAI,KAAKvN,OAAL,CAAa/G,UAAjB,EAA6B;AAC3B9I,MAAAA,MAAM,CAAC,KAAKqd,WAAL,EAAD,CAAN;AACD;;AAED,SAAKA,WAAL,GAAmB/d,SAAnB,CAA6B4S,GAA7B,CAAiCzI,iBAAjC;;AAEA,SAAK6T,iBAAL,CAAuB,MAAM;AAC3Bhc,MAAAA,OAAO,CAACf,QAAD,CAAP;AACD,KAFD;AAGD;;AAEDkV,EAAAA,IAAI,CAAClV,QAAD,EAAW;AACb,QAAI,CAAC,KAAKsP,OAAL,CAAa3Q,SAAlB,EAA6B;AAC3BoC,MAAAA,OAAO,CAACf,QAAD,CAAP;AACA;AACD;;AAED,SAAK8c,WAAL,GAAmB/d,SAAnB,CAA6B2I,MAA7B,CAAoCwB,iBAApC;;AAEA,SAAK6T,iBAAL,CAAuB,MAAM;AAC3B,WAAK7U,OAAL;AACAnH,MAAAA,OAAO,CAACf,QAAD,CAAP;AACD,KAHD;AAID,GAtCY;;;AA0Cb8c,EAAAA,WAAW,GAAG;AACZ,QAAI,CAAC,KAAK/U,QAAV,EAAoB;AAClB,YAAMiV,QAAQ,GAAGrjB,QAAQ,CAACsjB,aAAT,CAAuB,KAAvB,CAAjB;AACAD,MAAAA,QAAQ,CAACE,SAAT,GAAqBT,mBAArB;;AACA,UAAI,KAAKnN,OAAL,CAAa/G,UAAjB,EAA6B;AAC3ByU,QAAAA,QAAQ,CAACje,SAAT,CAAmB4S,GAAnB,CAAuB1I,iBAAvB;AACD;;AAED,WAAKlB,QAAL,GAAgBiV,QAAhB;AACD;;AAED,WAAO,KAAKjV,QAAZ;AACD;;AAEDwH,EAAAA,UAAU,CAACzR,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,EACP,GAAGoO,SADI;AAEP,UAAI,OAAOpO,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;AAFO,KAAT,CADiB;;AAOjBA,IAAAA,MAAM,CAACuL,WAAP,GAAqB3L,UAAU,CAACI,MAAM,CAACuL,WAAR,CAA/B;AACAzL,IAAAA,eAAe,CAAC6C,MAAD,EAAO3C,MAAP,EAAe2O,aAAf,CAAf;AACA,WAAO3O,MAAP;AACD;;AAED+e,EAAAA,OAAO,GAAG;AACR,QAAI,KAAKD,WAAT,EAAsB;AACpB;AACD;;AAED,SAAKtN,OAAL,CAAajG,WAAb,CAAyB8T,WAAzB,CAAqC,KAAKL,WAAL,EAArC;;AAEAxZ,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKuX,WAAL,EAAhB,EAAoCJ,eAApC,EAAqD,MAAM;AACzD3b,MAAAA,OAAO,CAAC,KAAKuO,OAAL,CAAakN,aAAd,CAAP;AACD,KAFD;AAIA,SAAKI,WAAL,GAAmB,IAAnB;AACD;;AAED1U,EAAAA,OAAO,GAAG;AACR,QAAI,CAAC,KAAK0U,WAAV,EAAuB;AACrB;AACD;;AAEDtZ,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKwE,QAAtB,EAAgC2U,eAAhC;;AAEA,SAAK3U,QAAL,CAAcL,MAAd;;AACA,SAAKkV,WAAL,GAAmB,KAAnB;AACD;;AAEDG,EAAAA,iBAAiB,CAAC/c,QAAD,EAAW;AAC1BgB,IAAAA,sBAAsB,CAAChB,QAAD,EAAW,KAAK8c,WAAL,EAAX,EAA+B,KAAKxN,OAAL,CAAa/G,UAA5C,CAAtB;AACD;;AA/FY;;AC9Bf;AACA;AACA;AACA;AACA;AACA;AAiBA;AACA;AACA;AACA;AACA;;AAEA,MAAM9H,MAAI,GAAG,OAAb;AACA,MAAMwH,UAAQ,GAAG,UAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMU,cAAY,GAAG,WAArB;AACA,MAAM8N,YAAU,GAAG,QAAnB;AAEA,MAAMvK,SAAO,GAAG;AACd8Q,EAAAA,QAAQ,EAAE,IADI;AAEd5Q,EAAAA,QAAQ,EAAE,IAFI;AAGdkN,EAAAA,KAAK,EAAE;AAHO,CAAhB;AAMA,MAAM7M,aAAW,GAAG;AAClBuQ,EAAAA,QAAQ,EAAE,kBADQ;AAElB5Q,EAAAA,QAAQ,EAAE,SAFQ;AAGlBkN,EAAAA,KAAK,EAAE;AAHW,CAApB;AAMA,MAAMxF,YAAU,GAAI,OAAM3L,WAAU,EAApC;AACA,MAAMiV,oBAAoB,GAAI,gBAAejV,WAAU,EAAvD;AACA,MAAM4L,cAAY,GAAI,SAAQ5L,WAAU,EAAxC;AACA,MAAMyL,YAAU,GAAI,OAAMzL,WAAU,EAApC;AACA,MAAM0L,aAAW,GAAI,QAAO1L,WAAU,EAAtC;AACA,MAAMkV,eAAa,GAAI,UAASlV,WAAU,EAA1C;AACA,MAAMmV,YAAY,GAAI,SAAQnV,WAAU,EAAxC;AACA,MAAMoV,qBAAmB,GAAI,gBAAepV,WAAU,EAAtD;AACA,MAAMqV,uBAAqB,GAAI,kBAAiBrV,WAAU,EAA1D;AACA,MAAMsV,qBAAqB,GAAI,kBAAiBtV,WAAU,EAA1D;AACA,MAAMuV,uBAAuB,GAAI,oBAAmBvV,WAAU,EAA9D;AACA,MAAMY,sBAAoB,GAAI,QAAOZ,WAAU,GAAEQ,cAAa,EAA9D;AAEA,MAAMgV,eAAe,GAAG,YAAxB;AACA,MAAM1U,iBAAe,GAAG,MAAxB;AACA,MAAMC,iBAAe,GAAG,MAAxB;AACA,MAAM0U,iBAAiB,GAAG,cAA1B;AAEA,MAAMC,eAAe,GAAG,eAAxB;AACA,MAAMC,mBAAmB,GAAG,aAA5B;AACA,MAAM7T,sBAAoB,GAAG,0BAA7B;AACA,MAAM8T,uBAAqB,GAAG,2BAA9B;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,KAAN,SAAoBnW,aAApB,CAAkC;AAChCC,EAAAA,WAAW,CAACpO,OAAD,EAAUoE,MAAV,EAAkB;AAC3B,UAAMpE,OAAN;AAEA,SAAK4V,OAAL,GAAe,KAAKC,UAAL,CAAgBzR,MAAhB,CAAf;AACA,SAAKmgB,OAAL,GAAe1kB,cAAc,CAACW,OAAf,CAAuB2jB,eAAvB,EAAwC,KAAK9V,QAA7C,CAAf;AACA,SAAKmW,SAAL,GAAiB,KAAKC,mBAAL,EAAjB;AACA,SAAKC,QAAL,GAAgB,KAAhB;AACA,SAAKC,oBAAL,GAA4B,KAA5B;AACA,SAAK9J,gBAAL,GAAwB,KAAxB;AACA,SAAK+J,UAAL,GAAkB,IAAInD,eAAJ,EAAlB;AACD,GAX+B;;;AAed,aAAPjP,OAAO,GAAG;AACnB,WAAOA,SAAP;AACD;;AAEc,aAAJzL,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD,GArB+B;;;AAyBhC0J,EAAAA,MAAM,CAACtF,aAAD,EAAgB;AACpB,WAAO,KAAKuZ,QAAL,GAAgB,KAAKlJ,IAAL,EAAhB,GAA8B,KAAKC,IAAL,CAAUtQ,aAAV,CAArC;AACD;;AAEDsQ,EAAAA,IAAI,CAACtQ,aAAD,EAAgB;AAClB,QAAI,KAAKuZ,QAAL,IAAiB,KAAK7J,gBAA1B,EAA4C;AAC1C;AACD;;AAED,UAAMsE,SAAS,GAAGvV,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC6L,YAApC,EAAgD;AAChE/O,MAAAA;AADgE,KAAhD,CAAlB;;AAIA,QAAIgU,SAAS,CAAC1S,gBAAd,EAAgC;AAC9B;AACD;;AAED,SAAKiY,QAAL,GAAgB,IAAhB;;AAEA,QAAI,KAAKG,WAAL,EAAJ,EAAwB;AACtB,WAAKhK,gBAAL,GAAwB,IAAxB;AACD;;AAED,SAAK+J,UAAL,CAAgBpJ,IAAhB;;AAEAvb,IAAAA,QAAQ,CAACkG,IAAT,CAAcd,SAAd,CAAwB4S,GAAxB,CAA4BgM,eAA5B;;AAEA,SAAKa,aAAL;;AAEA,SAAKC,eAAL;;AACA,SAAKC,eAAL;;AAEApb,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BwV,qBAA/B,EAAoDQ,uBAApD,EAA2E5a,KAAK,IAAI,KAAK+R,IAAL,CAAU/R,KAAV,CAApF;AAEAG,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0Y,OAArB,EAA8BP,uBAA9B,EAAuD,MAAM;AAC3Dpa,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKuC,QAAtB,EAAgC0V,qBAAhC,EAAuDta,KAAK,IAAI;AAC9D,YAAIA,KAAK,CAAC5B,MAAN,KAAiB,KAAKwG,QAA1B,EAAoC;AAClC,eAAKsW,oBAAL,GAA4B,IAA5B;AACD;AACF,OAJD;AAKD,KAND;;AAQA,SAAKM,aAAL,CAAmB,MAAM,KAAKC,YAAL,CAAkB/Z,aAAlB,CAAzB;AACD;;AAEDqQ,EAAAA,IAAI,CAAC/R,KAAD,EAAQ;AACV,QAAIA,KAAK,IAAI,CAAC,GAAD,EAAM,MAAN,EAAc7G,QAAd,CAAuB6G,KAAK,CAAC5B,MAAN,CAAaqQ,OAApC,CAAb,EAA2D;AACzDzO,MAAAA,KAAK,CAAC4D,cAAN;AACD;;AAED,QAAI,CAAC,KAAKqX,QAAN,IAAkB,KAAK7J,gBAA3B,EAA6C;AAC3C;AACD;;AAED,UAAMmF,SAAS,GAAGpW,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC+L,YAApC,CAAlB;;AAEA,QAAI4F,SAAS,CAACvT,gBAAd,EAAgC;AAC9B;AACD;;AAED,SAAKiY,QAAL,GAAgB,KAAhB;;AACA,UAAM7V,UAAU,GAAG,KAAKgW,WAAL,EAAnB;;AAEA,QAAIhW,UAAJ,EAAgB;AACd,WAAKgM,gBAAL,GAAwB,IAAxB;AACD;;AAED,SAAKkK,eAAL;;AACA,SAAKC,eAAL;;AAEApb,IAAAA,YAAY,CAACC,GAAb,CAAiB5J,QAAjB,EAA2B0jB,eAA3B;;AAEA,SAAKtV,QAAL,CAAchJ,SAAd,CAAwB2I,MAAxB,CAA+BwB,iBAA/B;;AAEA5F,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKwE,QAAtB,EAAgCwV,qBAAhC;AACAja,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0a,OAAtB,EAA+BP,uBAA/B;;AAEA,SAAKpV,cAAL,CAAoB,MAAM,KAAKuW,UAAL,EAA1B,EAA6C,KAAK9W,QAAlD,EAA4DQ,UAA5D;AACD;;AAEDL,EAAAA,OAAO,GAAG;AACR,KAACnL,MAAD,EAAS,KAAKkhB,OAAd,EACG/f,OADH,CACW4gB,WAAW,IAAIxb,YAAY,CAACC,GAAb,CAAiBub,WAAjB,EAA8B3W,WAA9B,CAD1B;;AAGA,SAAK+V,SAAL,CAAehW,OAAf;;AACA,UAAMA,OAAN;AAEA;AACJ;AACA;AACA;AACA;;AACI5E,IAAAA,YAAY,CAACC,GAAb,CAAiB5J,QAAjB,EAA2B0jB,eAA3B;AACD;;AAED0B,EAAAA,YAAY,GAAG;AACb,SAAKP,aAAL;AACD,GA1H+B;;;AA8HhCL,EAAAA,mBAAmB,GAAG;AACpB,WAAO,IAAIxB,QAAJ,CAAa;AAClBhe,MAAAA,SAAS,EAAEuG,OAAO,CAAC,KAAKoK,OAAL,CAAa0N,QAAd,CADA;AACyB;AAC3CzU,MAAAA,UAAU,EAAE,KAAKgW,WAAL;AAFM,KAAb,CAAP;AAID;;AAEDhP,EAAAA,UAAU,CAACzR,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,EACP,GAAGoO,SADI;AAEP,SAAGxB,WAAW,CAACI,iBAAZ,CAA8B,KAAK/C,QAAnC,CAFI;AAGP,UAAI,OAAOjK,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;AAHO,KAAT;AAKAF,IAAAA,eAAe,CAAC6C,MAAD,EAAO3C,MAAP,EAAe2O,aAAf,CAAf;AACA,WAAO3O,MAAP;AACD;;AAED8gB,EAAAA,YAAY,CAAC/Z,aAAD,EAAgB;AAC1B,UAAM0D,UAAU,GAAG,KAAKgW,WAAL,EAAnB;;AACA,UAAMS,SAAS,GAAGzlB,cAAc,CAACW,OAAf,CAAuB4jB,mBAAvB,EAA4C,KAAKG,OAAjD,CAAlB;;AAEA,QAAI,CAAC,KAAKlW,QAAL,CAAcrN,UAAf,IAA6B,KAAKqN,QAAL,CAAcrN,UAAd,CAAyBC,QAAzB,KAAsCC,IAAI,CAACC,YAA5E,EAA0F;AACxF;AACAlB,MAAAA,QAAQ,CAACkG,IAAT,CAAcsd,WAAd,CAA0B,KAAKpV,QAA/B;AACD;;AAED,SAAKA,QAAL,CAAc8N,KAAd,CAAoBqC,OAApB,GAA8B,OAA9B;;AACA,SAAKnQ,QAAL,CAAc8C,eAAd,CAA8B,aAA9B;;AACA,SAAK9C,QAAL,CAAcqC,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;AACA,SAAKrC,QAAL,CAAcqC,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;AACA,SAAKrC,QAAL,CAAcyD,SAAd,GAA0B,CAA1B;;AAEA,QAAIwT,SAAJ,EAAe;AACbA,MAAAA,SAAS,CAACxT,SAAV,GAAsB,CAAtB;AACD;;AAED,QAAIjD,UAAJ,EAAgB;AACd9I,MAAAA,MAAM,CAAC,KAAKsI,QAAN,CAAN;AACD;;AAED,SAAKA,QAAL,CAAchJ,SAAd,CAAwB4S,GAAxB,CAA4BzI,iBAA5B;;AAEA,QAAI,KAAKoG,OAAL,CAAagK,KAAjB,EAAwB;AACtB,WAAK2F,aAAL;AACD;;AAED,UAAMC,kBAAkB,GAAG,MAAM;AAC/B,UAAI,KAAK5P,OAAL,CAAagK,KAAjB,EAAwB;AACtB,aAAKvR,QAAL,CAAcuR,KAAd;AACD;;AAED,WAAK/E,gBAAL,GAAwB,KAAxB;AACAjR,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC8L,aAApC,EAAiD;AAC/ChP,QAAAA;AAD+C,OAAjD;AAGD,KATD;;AAWA,SAAKyD,cAAL,CAAoB4W,kBAApB,EAAwC,KAAKjB,OAA7C,EAAsD1V,UAAtD;AACD;;AAED0W,EAAAA,aAAa,GAAG;AACd3b,IAAAA,YAAY,CAACC,GAAb,CAAiB5J,QAAjB,EAA2B0jB,eAA3B,EADc;;AAEd/Z,IAAAA,YAAY,CAACiC,EAAb,CAAgB5L,QAAhB,EAA0B0jB,eAA1B,EAAyCla,KAAK,IAAI;AAChD,UAAIxJ,QAAQ,KAAKwJ,KAAK,CAAC5B,MAAnB,IACA,KAAKwG,QAAL,KAAkB5E,KAAK,CAAC5B,MADxB,IAEA,CAAC,KAAKwG,QAAL,CAAc/I,QAAd,CAAuBmE,KAAK,CAAC5B,MAA7B,CAFL,EAE2C;AACzC,aAAKwG,QAAL,CAAcuR,KAAd;AACD;AACF,KAND;AAOD;;AAEDmF,EAAAA,eAAe,GAAG;AAChB,QAAI,KAAKL,QAAT,EAAmB;AACjB9a,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+ByV,uBAA/B,EAAsDra,KAAK,IAAI;AAC7D,YAAI,KAAKmM,OAAL,CAAalD,QAAb,IAAyBjJ,KAAK,CAACyD,GAAN,KAAc6P,YAA3C,EAAuD;AACrDtT,UAAAA,KAAK,CAAC4D,cAAN;AACA,eAAKmO,IAAL;AACD,SAHD,MAGO,IAAI,CAAC,KAAK5F,OAAL,CAAalD,QAAd,IAA0BjJ,KAAK,CAACyD,GAAN,KAAc6P,YAA5C,EAAwD;AAC7D,eAAK0I,0BAAL;AACD;AACF,OAPD;AAQD,KATD,MASO;AACL7b,MAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKwE,QAAtB,EAAgCyV,uBAAhC;AACD;AACF;;AAEDkB,EAAAA,eAAe,GAAG;AAChB,QAAI,KAAKN,QAAT,EAAmB;AACjB9a,MAAAA,YAAY,CAACiC,EAAb,CAAgBxI,MAAhB,EAAwBugB,YAAxB,EAAsC,MAAM,KAAKkB,aAAL,EAA5C;AACD,KAFD,MAEO;AACLlb,MAAAA,YAAY,CAACC,GAAb,CAAiBxG,MAAjB,EAAyBugB,YAAzB;AACD;AACF;;AAEDuB,EAAAA,UAAU,GAAG;AACX,SAAK9W,QAAL,CAAc8N,KAAd,CAAoBqC,OAApB,GAA8B,MAA9B;;AACA,SAAKnQ,QAAL,CAAcqC,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;AACA,SAAKrC,QAAL,CAAc8C,eAAd,CAA8B,YAA9B;;AACA,SAAK9C,QAAL,CAAc8C,eAAd,CAA8B,MAA9B;;AACA,SAAK0J,gBAAL,GAAwB,KAAxB;;AACA,SAAK2J,SAAL,CAAehJ,IAAf,CAAoB,MAAM;AACxBvb,MAAAA,QAAQ,CAACkG,IAAT,CAAcd,SAAd,CAAwB2I,MAAxB,CAA+BiW,eAA/B;;AACA,WAAKyB,iBAAL;;AACA,WAAKd,UAAL,CAAgBpC,KAAhB;;AACA5Y,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoCgM,cAApC;AACD,KALD;AAMD;;AAED4K,EAAAA,aAAa,CAAC3e,QAAD,EAAW;AACtBsD,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BwV,qBAA/B,EAAoDpa,KAAK,IAAI;AAC3D,UAAI,KAAKkb,oBAAT,EAA+B;AAC7B,aAAKA,oBAAL,GAA4B,KAA5B;AACA;AACD;;AAED,UAAIlb,KAAK,CAAC5B,MAAN,KAAiB4B,KAAK,CAACkc,aAA3B,EAA0C;AACxC;AACD;;AAED,UAAI,KAAK/P,OAAL,CAAa0N,QAAb,KAA0B,IAA9B,EAAoC;AAClC,aAAK9H,IAAL;AACD,OAFD,MAEO,IAAI,KAAK5F,OAAL,CAAa0N,QAAb,KAA0B,QAA9B,EAAwC;AAC7C,aAAKmC,0BAAL;AACD;AACF,KAfD;;AAiBA,SAAKjB,SAAL,CAAe/I,IAAf,CAAoBnV,QAApB;AACD;;AAEDue,EAAAA,WAAW,GAAG;AACZ,WAAO,KAAKxW,QAAL,CAAchJ,SAAd,CAAwBC,QAAxB,CAAiCiK,iBAAjC,CAAP;AACD;;AAEDkW,EAAAA,0BAA0B,GAAG;AAC3B,UAAMzF,SAAS,GAAGpW,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoCqV,oBAApC,CAAlB;;AACA,QAAI1D,SAAS,CAACvT,gBAAd,EAAgC;AAC9B;AACD;;AAED,UAAM;AAAEpH,MAAAA,SAAF;AAAaugB,MAAAA,YAAb;AAA2BzJ,MAAAA;AAA3B,QAAqC,KAAK9N,QAAhD;AACA,UAAMwX,kBAAkB,GAAGD,YAAY,GAAG3lB,QAAQ,CAACC,eAAT,CAAyB4lB,YAAnE,CAP2B;;AAU3B,QAAK,CAACD,kBAAD,IAAuB1J,KAAK,CAAC4J,SAAN,KAAoB,QAA5C,IAAyD1gB,SAAS,CAACC,QAAV,CAAmB4e,iBAAnB,CAA7D,EAAoG;AAClG;AACD;;AAED,QAAI,CAAC2B,kBAAL,EAAyB;AACvB1J,MAAAA,KAAK,CAAC4J,SAAN,GAAkB,QAAlB;AACD;;AAED1gB,IAAAA,SAAS,CAAC4S,GAAV,CAAciM,iBAAd;;AACA,SAAKtV,cAAL,CAAoB,MAAM;AACxBvJ,MAAAA,SAAS,CAAC2I,MAAV,CAAiBkW,iBAAjB;;AACA,UAAI,CAAC2B,kBAAL,EAAyB;AACvB,aAAKjX,cAAL,CAAoB,MAAM;AACxBuN,UAAAA,KAAK,CAAC4J,SAAN,GAAkB,EAAlB;AACD,SAFD,EAEG,KAAKxB,OAFR;AAGD;AACF,KAPD,EAOG,KAAKA,OAPR;;AASA,SAAKlW,QAAL,CAAcuR,KAAd;AACD,GAhS+B;AAmShC;AACA;;;AAEAkF,EAAAA,aAAa,GAAG;AACd,UAAMe,kBAAkB,GAAG,KAAKxX,QAAL,CAAcuX,YAAd,GAA6B3lB,QAAQ,CAACC,eAAT,CAAyB4lB,YAAjF;;AACA,UAAMzD,cAAc,GAAG,KAAKuC,UAAL,CAAgBlD,QAAhB,EAAvB;;AACA,UAAMsE,iBAAiB,GAAG3D,cAAc,GAAG,CAA3C;;AAEA,QAAK,CAAC2D,iBAAD,IAAsBH,kBAAtB,IAA4C,CAACpf,KAAK,EAAnD,IAA2Duf,iBAAiB,IAAI,CAACH,kBAAtB,IAA4Cpf,KAAK,EAAhH,EAAqH;AACnH,WAAK4H,QAAL,CAAc8N,KAAd,CAAoB8J,WAApB,GAAmC,GAAE5D,cAAe,IAApD;AACD;;AAED,QAAK2D,iBAAiB,IAAI,CAACH,kBAAtB,IAA4C,CAACpf,KAAK,EAAnD,IAA2D,CAACuf,iBAAD,IAAsBH,kBAAtB,IAA4Cpf,KAAK,EAAhH,EAAqH;AACnH,WAAK4H,QAAL,CAAc8N,KAAd,CAAoB+J,YAApB,GAAoC,GAAE7D,cAAe,IAArD;AACD;AACF;;AAEDqD,EAAAA,iBAAiB,GAAG;AAClB,SAAKrX,QAAL,CAAc8N,KAAd,CAAoB8J,WAApB,GAAkC,EAAlC;AACA,SAAK5X,QAAL,CAAc8N,KAAd,CAAoB+J,YAApB,GAAmC,EAAnC;AACD,GAvT+B;;;AA2TV,SAAfhf,eAAe,CAAC9C,MAAD,EAAS+G,aAAT,EAAwB;AAC5C,WAAO,KAAK+E,IAAL,CAAU,YAAY;AAC3B,YAAMC,IAAI,GAAGmU,KAAK,CAACvV,mBAAN,CAA0B,IAA1B,EAAgC3K,MAAhC,CAAb;;AAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B;AACD;;AAED,UAAI,OAAO+L,IAAI,CAAC/L,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED+L,MAAAA,IAAI,CAAC/L,MAAD,CAAJ,CAAa+G,aAAb;AACD,KAZM,CAAP;AAaD;;AAzU+B;AA4UlC;AACA;AACA;AACA;AACA;;;AAEAvB,YAAY,CAACiC,EAAb,CAAgB5L,QAAhB,EAA0BoP,sBAA1B,EAAgDkB,sBAAhD,EAAsE,UAAU9G,KAAV,EAAiB;AACrF,QAAM5B,MAAM,GAAG5E,sBAAsB,CAAC,IAAD,CAArC;;AAEA,MAAI,CAAC,GAAD,EAAM,MAAN,EAAcL,QAAd,CAAuB,KAAKsV,OAA5B,CAAJ,EAA0C;AACxCzO,IAAAA,KAAK,CAAC4D,cAAN;AACD;;AAEDzD,EAAAA,YAAY,CAACkC,GAAb,CAAiBjE,MAAjB,EAAyBqS,YAAzB,EAAqCiF,SAAS,IAAI;AAChD,QAAIA,SAAS,CAAC1S,gBAAd,EAAgC;AAC9B;AACA;AACD;;AAED7C,IAAAA,YAAY,CAACkC,GAAb,CAAiBjE,MAAjB,EAAyBwS,cAAzB,EAAuC,MAAM;AAC3C,UAAIpV,SAAS,CAAC,IAAD,CAAb,EAAqB;AACnB,aAAK2a,KAAL;AACD;AACF,KAJD;AAKD,GAXD;AAaA,QAAMzP,IAAI,GAAGmU,KAAK,CAACvV,mBAAN,CAA0BlH,MAA1B,CAAb;AAEAsI,EAAAA,IAAI,CAACM,MAAL,CAAY,IAAZ;AACD,CAvBD;AAyBA;AACA;AACA;AACA;AACA;AACA;;AAEA9J,kBAAkB,CAAC2d,KAAD,CAAlB;;AC7bA;AACA;AACA;AACA;AACA;AACA;AAgBA;AACA;AACA;AACA;AACA;;AAEA,MAAMvd,MAAI,GAAG,WAAb;AACA,MAAMwH,UAAQ,GAAG,cAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMU,cAAY,GAAG,WAArB;AACA,MAAM+E,qBAAmB,GAAI,OAAMvF,WAAU,GAAEQ,cAAa,EAA5D;AACA,MAAM8N,UAAU,GAAG,QAAnB;AAEA,MAAMvK,SAAO,GAAG;AACd8Q,EAAAA,QAAQ,EAAE,IADI;AAEd5Q,EAAAA,QAAQ,EAAE,IAFI;AAGdyT,EAAAA,MAAM,EAAE;AAHM,CAAhB;AAMA,MAAMpT,aAAW,GAAG;AAClBuQ,EAAAA,QAAQ,EAAE,SADQ;AAElB5Q,EAAAA,QAAQ,EAAE,SAFQ;AAGlByT,EAAAA,MAAM,EAAE;AAHU,CAApB;AAMA,MAAM3W,iBAAe,GAAG,MAAxB;AACA,MAAM4W,aAAa,GAAG,iBAAtB;AAEA,MAAMlM,YAAU,GAAI,OAAMzL,WAAU,EAApC;AACA,MAAM0L,aAAW,GAAI,QAAO1L,WAAU,EAAtC;AACA,MAAM2L,YAAU,GAAI,OAAM3L,WAAU,EAApC;AACA,MAAM4L,cAAY,GAAI,SAAQ5L,WAAU,EAAxC;AACA,MAAMkV,eAAa,GAAI,UAASlV,WAAU,EAA1C;AACA,MAAMY,sBAAoB,GAAI,QAAOZ,WAAU,GAAEQ,cAAa,EAA9D;AACA,MAAM4U,qBAAmB,GAAI,gBAAepV,WAAU,EAAtD;AACA,MAAMqV,qBAAqB,GAAI,kBAAiBrV,WAAU,EAA1D;AAEA,MAAM4V,uBAAqB,GAAG,+BAA9B;AACA,MAAM9T,sBAAoB,GAAG,8BAA7B;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAM8V,SAAN,SAAwBlY,aAAxB,CAAsC;AACpCC,EAAAA,WAAW,CAACpO,OAAD,EAAUoE,MAAV,EAAkB;AAC3B,UAAMpE,OAAN;AAEA,SAAK4V,OAAL,GAAe,KAAKC,UAAL,CAAgBzR,MAAhB,CAAf;AACA,SAAKsgB,QAAL,GAAgB,KAAhB;AACA,SAAKF,SAAL,GAAiB,KAAKC,mBAAL,EAAjB;;AACA,SAAKrO,kBAAL;AACD,GARmC;;;AAYrB,aAAJrP,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD;;AAEiB,aAAPyL,OAAO,GAAG;AACnB,WAAOA,SAAP;AACD,GAlBmC;;;AAsBpC/B,EAAAA,MAAM,CAACtF,aAAD,EAAgB;AACpB,WAAO,KAAKuZ,QAAL,GAAgB,KAAKlJ,IAAL,EAAhB,GAA8B,KAAKC,IAAL,CAAUtQ,aAAV,CAArC;AACD;;AAEDsQ,EAAAA,IAAI,CAACtQ,aAAD,EAAgB;AAClB,QAAI,KAAKuZ,QAAT,EAAmB;AACjB;AACD;;AAED,UAAMvF,SAAS,GAAGvV,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC6L,YAApC,EAAgD;AAAE/O,MAAAA;AAAF,KAAhD,CAAlB;;AAEA,QAAIgU,SAAS,CAAC1S,gBAAd,EAAgC;AAC9B;AACD;;AAED,SAAKiY,QAAL,GAAgB,IAAhB;AACA,SAAKrW,QAAL,CAAc8N,KAAd,CAAoBmK,UAApB,GAAiC,SAAjC;;AAEA,SAAK9B,SAAL,CAAe/I,IAAf;;AAEA,QAAI,CAAC,KAAK7F,OAAL,CAAauQ,MAAlB,EAA0B;AACxB,UAAI1E,eAAJ,GAAsBjG,IAAtB;;AACA,WAAK+K,sBAAL,CAA4B,KAAKlY,QAAjC;AACD;;AAED,SAAKA,QAAL,CAAc8C,eAAd,CAA8B,aAA9B;;AACA,SAAK9C,QAAL,CAAcqC,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;AACA,SAAKrC,QAAL,CAAcqC,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;AACA,SAAKrC,QAAL,CAAchJ,SAAd,CAAwB4S,GAAxB,CAA4BzI,iBAA5B;;AAEA,UAAMkK,gBAAgB,GAAG,MAAM;AAC7B9P,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC8L,aAApC,EAAiD;AAAEhP,QAAAA;AAAF,OAAjD;AACD,KAFD;;AAIA,SAAKyD,cAAL,CAAoB8K,gBAApB,EAAsC,KAAKrL,QAA3C,EAAqD,IAArD;AACD;;AAEDmN,EAAAA,IAAI,GAAG;AACL,QAAI,CAAC,KAAKkJ,QAAV,EAAoB;AAClB;AACD;;AAED,UAAM1E,SAAS,GAAGpW,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC+L,YAApC,CAAlB;;AAEA,QAAI4F,SAAS,CAACvT,gBAAd,EAAgC;AAC9B;AACD;;AAED7C,IAAAA,YAAY,CAACC,GAAb,CAAiB5J,QAAjB,EAA2B0jB,eAA3B;;AACA,SAAKtV,QAAL,CAAcmY,IAAd;;AACA,SAAK9B,QAAL,GAAgB,KAAhB;;AACA,SAAKrW,QAAL,CAAchJ,SAAd,CAAwB2I,MAAxB,CAA+BwB,iBAA/B;;AACA,SAAKgV,SAAL,CAAehJ,IAAf;;AAEA,UAAMiL,gBAAgB,GAAG,MAAM;AAC7B,WAAKpY,QAAL,CAAcqC,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;AACA,WAAKrC,QAAL,CAAc8C,eAAd,CAA8B,YAA9B;;AACA,WAAK9C,QAAL,CAAc8C,eAAd,CAA8B,MAA9B;;AACA,WAAK9C,QAAL,CAAc8N,KAAd,CAAoBmK,UAApB,GAAiC,QAAjC;;AAEA,UAAI,CAAC,KAAK1Q,OAAL,CAAauQ,MAAlB,EAA0B;AACxB,YAAI1E,eAAJ,GAAsBe,KAAtB;AACD;;AAED5Y,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoCgM,cAApC;AACD,KAXD;;AAaA,SAAKzL,cAAL,CAAoB6X,gBAApB,EAAsC,KAAKpY,QAA3C,EAAqD,IAArD;AACD;;AAEDG,EAAAA,OAAO,GAAG;AACR,SAAKgW,SAAL,CAAehW,OAAf;;AACA,UAAMA,OAAN;AACA5E,IAAAA,YAAY,CAACC,GAAb,CAAiB5J,QAAjB,EAA2B0jB,eAA3B;AACD,GAhGmC;;;AAoGpC9N,EAAAA,UAAU,CAACzR,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,EACP,GAAGoO,SADI;AAEP,SAAGxB,WAAW,CAACI,iBAAZ,CAA8B,KAAK/C,QAAnC,CAFI;AAGP,UAAI,OAAOjK,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;AAHO,KAAT;AAKAF,IAAAA,eAAe,CAAC6C,MAAD,EAAO3C,MAAP,EAAe2O,aAAf,CAAf;AACA,WAAO3O,MAAP;AACD;;AAEDqgB,EAAAA,mBAAmB,GAAG;AACpB,WAAO,IAAIxB,QAAJ,CAAa;AAClBhe,MAAAA,SAAS,EAAE,KAAK2Q,OAAL,CAAa0N,QADN;AAElBzU,MAAAA,UAAU,EAAE,IAFM;AAGlBc,MAAAA,WAAW,EAAE,KAAKtB,QAAL,CAAcrN,UAHT;AAIlB8hB,MAAAA,aAAa,EAAE,MAAM,KAAKtH,IAAL;AAJH,KAAb,CAAP;AAMD;;AAED+K,EAAAA,sBAAsB,CAACvmB,OAAD,EAAU;AAC9B4J,IAAAA,YAAY,CAACC,GAAb,CAAiB5J,QAAjB,EAA2B0jB,eAA3B,EAD8B;;AAE9B/Z,IAAAA,YAAY,CAACiC,EAAb,CAAgB5L,QAAhB,EAA0B0jB,eAA1B,EAAyCla,KAAK,IAAI;AAChD,UAAIxJ,QAAQ,KAAKwJ,KAAK,CAAC5B,MAAnB,IACF7H,OAAO,KAAKyJ,KAAK,CAAC5B,MADhB,IAEF,CAAC7H,OAAO,CAACsF,QAAR,CAAiBmE,KAAK,CAAC5B,MAAvB,CAFH,EAEmC;AACjC7H,QAAAA,OAAO,CAAC4f,KAAR;AACD;AACF,KAND;AAOA5f,IAAAA,OAAO,CAAC4f,KAAR;AACD;;AAEDxJ,EAAAA,kBAAkB,GAAG;AACnBxM,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BwV,qBAA/B,EAAoDQ,uBAApD,EAA2E,MAAM,KAAK7I,IAAL,EAAjF;AAEA5R,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+ByV,qBAA/B,EAAsDra,KAAK,IAAI;AAC7D,UAAI,KAAKmM,OAAL,CAAalD,QAAb,IAAyBjJ,KAAK,CAACyD,GAAN,KAAc6P,UAA3C,EAAuD;AACrD,aAAKvB,IAAL;AACD;AACF,KAJD;AAKD,GA3ImC;;;AA+Id,SAAftU,eAAe,CAAC9C,MAAD,EAAS;AAC7B,WAAO,KAAK8L,IAAL,CAAU,YAAY;AAC3B,YAAMC,IAAI,GAAGkW,SAAS,CAACtX,mBAAV,CAA8B,IAA9B,EAAoC3K,MAApC,CAAb;;AAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B;AACD;;AAED,UAAI+L,IAAI,CAAC/L,MAAD,CAAJ,KAAiBrC,SAAjB,IAA8BqC,MAAM,CAACvB,UAAP,CAAkB,GAAlB,CAA9B,IAAwDuB,MAAM,KAAK,aAAvE,EAAsF;AACpF,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED+L,MAAAA,IAAI,CAAC/L,MAAD,CAAJ,CAAa,IAAb;AACD,KAZM,CAAP;AAaD;;AA7JmC;AAgKtC;AACA;AACA;AACA;AACA;;;AAEAwF,YAAY,CAACiC,EAAb,CAAgB5L,QAAhB,EAA0BoP,sBAA1B,EAAgDkB,sBAAhD,EAAsE,UAAU9G,KAAV,EAAiB;AACrF,QAAM5B,MAAM,GAAG5E,sBAAsB,CAAC,IAAD,CAArC;;AAEA,MAAI,CAAC,GAAD,EAAM,MAAN,EAAcL,QAAd,CAAuB,KAAKsV,OAA5B,CAAJ,EAA0C;AACxCzO,IAAAA,KAAK,CAAC4D,cAAN;AACD;;AAED,MAAIjI,UAAU,CAAC,IAAD,CAAd,EAAsB;AACpB;AACD;;AAEDwE,EAAAA,YAAY,CAACkC,GAAb,CAAiBjE,MAAjB,EAAyBwS,cAAzB,EAAuC,MAAM;AAC3C;AACA,QAAIpV,SAAS,CAAC,IAAD,CAAb,EAAqB;AACnB,WAAK2a,KAAL;AACD;AACF,GALD,EAXqF;;AAmBrF,QAAM8G,YAAY,GAAG7mB,cAAc,CAACW,OAAf,CAAuB4lB,aAAvB,CAArB;;AACA,MAAIM,YAAY,IAAIA,YAAY,KAAK7e,MAArC,EAA6C;AAC3Cwe,IAAAA,SAAS,CAACvX,WAAV,CAAsB4X,YAAtB,EAAoClL,IAApC;AACD;;AAED,QAAMrL,IAAI,GAAGkW,SAAS,CAACtX,mBAAV,CAA8BlH,MAA9B,CAAb;AACAsI,EAAAA,IAAI,CAACM,MAAL,CAAY,IAAZ;AACD,CA1BD;AA4BA7G,YAAY,CAACiC,EAAb,CAAgBxI,MAAhB,EAAwB2Q,qBAAxB,EAA6C,MAC3CnU,cAAc,CAACC,IAAf,CAAoBsmB,aAApB,EAAmC5hB,OAAnC,CAA2CmiB,EAAE,IAAIN,SAAS,CAACtX,mBAAV,CAA8B4X,EAA9B,EAAkClL,IAAlC,EAAjD,CADF;AAIA;AACA;AACA;AACA;AACA;;AAEA9U,kBAAkB,CAAC0f,SAAD,CAAlB;;AC/QA;AACA;AACA;AACA;AACA;AACA;AAEA,MAAMO,QAAQ,GAAG,IAAIxd,GAAJ,CAAQ,CACvB,YADuB,EAEvB,MAFuB,EAGvB,MAHuB,EAIvB,UAJuB,EAKvB,UALuB,EAMvB,QANuB,EAOvB,KAPuB,EAQvB,YARuB,CAAR,CAAjB;AAWA,MAAMyd,sBAAsB,GAAG,gBAA/B;AAEA;AACA;AACA;AACA;AACA;;AACA,MAAMC,gBAAgB,GAAG,4DAAzB;AAEA;AACA;AACA;AACA;AACA;;AACA,MAAMC,gBAAgB,GAAG,oIAAzB;;AAEA,MAAMC,gBAAgB,GAAG,CAACC,IAAD,EAAOC,oBAAP,KAAgC;AACvD,QAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAL,CAAcllB,WAAd,EAAjB;;AAEA,MAAIglB,oBAAoB,CAACtkB,QAArB,CAA8BukB,QAA9B,CAAJ,EAA6C;AAC3C,QAAIP,QAAQ,CAAC5b,GAAT,CAAamc,QAAb,CAAJ,EAA4B;AAC1B,aAAO3b,OAAO,CAACsb,gBAAgB,CAAChiB,IAAjB,CAAsBmiB,IAAI,CAACI,SAA3B,KAAyCN,gBAAgB,CAACjiB,IAAjB,CAAsBmiB,IAAI,CAACI,SAA3B,CAA1C,CAAd;AACD;;AAED,WAAO,IAAP;AACD;;AAED,QAAMC,MAAM,GAAGJ,oBAAoB,CAACvmB,MAArB,CAA4B4mB,SAAS,IAAIA,SAAS,YAAY1iB,MAA9D,CAAf,CAXuD;;AAcvD,OAAK,IAAIqF,CAAC,GAAG,CAAR,EAAWK,GAAG,GAAG+c,MAAM,CAACrjB,MAA7B,EAAqCiG,CAAC,GAAGK,GAAzC,EAA8CL,CAAC,EAA/C,EAAmD;AACjD,QAAIod,MAAM,CAACpd,CAAD,CAAN,CAAUpF,IAAV,CAAeqiB,QAAf,CAAJ,EAA8B;AAC5B,aAAO,IAAP;AACD;AACF;;AAED,SAAO,KAAP;AACD,CArBD;;AAuBO,MAAMK,gBAAgB,GAAG;AAC9B;AACA,OAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCX,sBAAvC,CAFyB;AAG9BY,EAAAA,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;AAI9BC,EAAAA,IAAI,EAAE,EAJwB;AAK9BC,EAAAA,CAAC,EAAE,EAL2B;AAM9BC,EAAAA,EAAE,EAAE,EAN0B;AAO9BC,EAAAA,GAAG,EAAE,EAPyB;AAQ9BC,EAAAA,IAAI,EAAE,EARwB;AAS9BC,EAAAA,GAAG,EAAE,EATyB;AAU9BC,EAAAA,EAAE,EAAE,EAV0B;AAW9BC,EAAAA,EAAE,EAAE,EAX0B;AAY9BC,EAAAA,EAAE,EAAE,EAZ0B;AAa9BC,EAAAA,EAAE,EAAE,EAb0B;AAc9BC,EAAAA,EAAE,EAAE,EAd0B;AAe9BC,EAAAA,EAAE,EAAE,EAf0B;AAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;AAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;AAkB9Bre,EAAAA,CAAC,EAAE,EAlB2B;AAmB9Bse,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,EAAkB,KAAlB,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,QAA3C,CAnByB;AAoB9BC,EAAAA,EAAE,EAAE,EApB0B;AAqB9BC,EAAAA,EAAE,EAAE,EArB0B;AAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;AAuB9BC,EAAAA,GAAG,EAAE,EAvByB;AAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;AAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;AA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;AA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;AA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;AA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;AA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;AA+B9BC,EAAAA,EAAE,EAAE;AA/B0B,CAAzB;AAkCA,SAASC,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,UAA7C,EAAyD;AAC9D,MAAI,CAACF,UAAU,CAACrlB,MAAhB,EAAwB;AACtB,WAAOqlB,UAAP;AACD;;AAED,MAAIE,UAAU,IAAI,OAAOA,UAAP,KAAsB,UAAxC,EAAoD;AAClD,WAAOA,UAAU,CAACF,UAAD,CAAjB;AACD;;AAED,QAAMG,SAAS,GAAG,IAAIpmB,MAAM,CAACqmB,SAAX,EAAlB;AACA,QAAMC,eAAe,GAAGF,SAAS,CAACG,eAAV,CAA0BN,UAA1B,EAAsC,WAAtC,CAAxB;AACA,QAAMO,aAAa,GAAGvlB,MAAM,CAACC,IAAP,CAAYglB,SAAZ,CAAtB;AACA,QAAMO,QAAQ,GAAG,GAAG3pB,MAAH,CAAU,GAAGwpB,eAAe,CAACxjB,IAAhB,CAAqB7F,gBAArB,CAAsC,GAAtC,CAAb,CAAjB;;AAEA,OAAK,IAAI4J,CAAC,GAAG,CAAR,EAAWK,GAAG,GAAGuf,QAAQ,CAAC7lB,MAA/B,EAAuCiG,CAAC,GAAGK,GAA3C,EAAgDL,CAAC,EAAjD,EAAqD;AACnD,UAAMyc,EAAE,GAAGmD,QAAQ,CAAC5f,CAAD,CAAnB;AACA,UAAM6f,MAAM,GAAGpD,EAAE,CAACS,QAAH,CAAYllB,WAAZ,EAAf;;AAEA,QAAI,CAAC2nB,aAAa,CAACjnB,QAAd,CAAuBmnB,MAAvB,CAAL,EAAqC;AACnCpD,MAAAA,EAAE,CAAC3Y,MAAH;AAEA;AACD;;AAED,UAAMgc,aAAa,GAAG,GAAG7pB,MAAH,CAAU,GAAGwmB,EAAE,CAACtV,UAAhB,CAAtB;AACA,UAAM4Y,iBAAiB,GAAG,GAAG9pB,MAAH,CAAUopB,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACQ,MAAD,CAAT,IAAqB,EAArD,CAA1B;AAEAC,IAAAA,aAAa,CAACxlB,OAAd,CAAsByiB,IAAI,IAAI;AAC5B,UAAI,CAACD,gBAAgB,CAACC,IAAD,EAAOgD,iBAAP,CAArB,EAAgD;AAC9CtD,QAAAA,EAAE,CAACxV,eAAH,CAAmB8V,IAAI,CAACG,QAAxB;AACD;AACF,KAJD;AAKD;;AAED,SAAOuC,eAAe,CAACxjB,IAAhB,CAAqB+jB,SAA5B;AACD;;AC9HD;AACA;AACA;AACA;AACA;AACA;AAwBA;AACA;AACA;AACA;AACA;;AAEA,MAAMnjB,MAAI,GAAG,SAAb;AACA,MAAMwH,UAAQ,GAAG,YAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAM4b,cAAY,GAAG,YAArB;AACA,MAAMC,oBAAkB,GAAG,IAAIvlB,MAAJ,CAAY,UAASslB,cAAa,MAAlC,EAAyC,GAAzC,CAA3B;AACA,MAAME,qBAAqB,GAAG,IAAIjhB,GAAJ,CAAQ,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAAR,CAA9B;AAEA,MAAM2J,aAAW,GAAG;AAClBuX,EAAAA,SAAS,EAAE,SADO;AAElBC,EAAAA,QAAQ,EAAE,QAFQ;AAGlBC,EAAAA,KAAK,EAAE,2BAHW;AAIlBpe,EAAAA,OAAO,EAAE,QAJS;AAKlBqe,EAAAA,KAAK,EAAE,iBALW;AAMlBC,EAAAA,IAAI,EAAE,SANY;AAOlB3qB,EAAAA,QAAQ,EAAE,kBAPQ;AAQlBygB,EAAAA,SAAS,EAAE,mBARO;AASlB9O,EAAAA,MAAM,EAAE,yBATU;AAUlBkK,EAAAA,SAAS,EAAE,0BAVO;AAWlB+O,EAAAA,kBAAkB,EAAE,OAXF;AAYlBrM,EAAAA,QAAQ,EAAE,kBAZQ;AAalBsM,EAAAA,WAAW,EAAE,mBAbK;AAclBC,EAAAA,QAAQ,EAAE,SAdQ;AAelBrB,EAAAA,UAAU,EAAE,iBAfM;AAgBlBD,EAAAA,SAAS,EAAE,QAhBO;AAiBlB9K,EAAAA,YAAY,EAAE;AAjBI,CAApB;AAoBA,MAAMqM,aAAa,GAAG;AACpBC,EAAAA,IAAI,EAAE,MADc;AAEpBC,EAAAA,GAAG,EAAE,KAFe;AAGpBC,EAAAA,KAAK,EAAExkB,KAAK,KAAK,MAAL,GAAc,OAHN;AAIpBykB,EAAAA,MAAM,EAAE,QAJY;AAKpBC,EAAAA,IAAI,EAAE1kB,KAAK,KAAK,OAAL,GAAe;AALN,CAAtB;AAQA,MAAM+L,SAAO,GAAG;AACd8X,EAAAA,SAAS,EAAE,IADG;AAEdC,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEE,mCAFF,GAGA,QALI;AAMdne,EAAAA,OAAO,EAAE,aANK;AAOdoe,EAAAA,KAAK,EAAE,EAPO;AAQdC,EAAAA,KAAK,EAAE,CARO;AASdC,EAAAA,IAAI,EAAE,KATQ;AAUd3qB,EAAAA,QAAQ,EAAE,KAVI;AAWdygB,EAAAA,SAAS,EAAE,KAXG;AAYd9O,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAZM;AAadkK,EAAAA,SAAS,EAAE,KAbG;AAcd+O,EAAAA,kBAAkB,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,CAdN;AAedrM,EAAAA,QAAQ,EAAE,iBAfI;AAgBdsM,EAAAA,WAAW,EAAE,EAhBC;AAiBdC,EAAAA,QAAQ,EAAE,IAjBI;AAkBdrB,EAAAA,UAAU,EAAE,IAlBE;AAmBdD,EAAAA,SAAS,EAAE/B,gBAnBG;AAoBd/I,EAAAA,YAAY,EAAE;AApBA,CAAhB;AAuBA,MAAM5a,OAAK,GAAG;AACZunB,EAAAA,IAAI,EAAG,OAAM3c,WAAU,EADX;AAEZ4c,EAAAA,MAAM,EAAG,SAAQ5c,WAAU,EAFf;AAGZ6c,EAAAA,IAAI,EAAG,OAAM7c,WAAU,EAHX;AAIZ8c,EAAAA,KAAK,EAAG,QAAO9c,WAAU,EAJb;AAKZ+c,EAAAA,QAAQ,EAAG,WAAU/c,WAAU,EALnB;AAMZgd,EAAAA,KAAK,EAAG,QAAOhd,WAAU,EANb;AAOZid,EAAAA,OAAO,EAAG,UAASjd,WAAU,EAPjB;AAQZkd,EAAAA,QAAQ,EAAG,WAAUld,WAAU,EARnB;AASZmd,EAAAA,UAAU,EAAG,aAAYnd,WAAU,EATvB;AAUZod,EAAAA,UAAU,EAAG,aAAYpd,WAAU;AAVvB,CAAd;AAaA,MAAMc,iBAAe,GAAG,MAAxB;AACA,MAAMuc,gBAAgB,GAAG,OAAzB;AACA,MAAMtc,iBAAe,GAAG,MAAxB;AAEA,MAAMuc,gBAAgB,GAAG,MAAzB;AACA,MAAMC,eAAe,GAAG,KAAxB;AAEA,MAAMC,sBAAsB,GAAG,gBAA/B;AAEA,MAAMC,aAAa,GAAG,OAAtB;AACA,MAAMC,aAAa,GAAG,OAAtB;AACA,MAAMC,aAAa,GAAG,OAAtB;AACA,MAAMC,cAAc,GAAG,QAAvB;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,OAAN,SAAsBne,aAAtB,CAAoC;AAClCC,EAAAA,WAAW,CAACpO,OAAD,EAAUoE,MAAV,EAAkB;AAC3B,QAAI,OAAOgb,MAAP,KAAkB,WAAtB,EAAmC;AACjC,YAAM,IAAIra,SAAJ,CAAc,8DAAd,CAAN;AACD;;AAED,UAAM/E,OAAN,EAL2B;;AAQ3B,SAAKusB,UAAL,GAAkB,IAAlB;AACA,SAAKC,QAAL,GAAgB,CAAhB;AACA,SAAKC,WAAL,GAAmB,EAAnB;AACA,SAAKC,cAAL,GAAsB,EAAtB;AACA,SAAK9N,OAAL,GAAe,IAAf,CAZ2B;;AAe3B,SAAKhJ,OAAL,GAAe,KAAKC,UAAL,CAAgBzR,MAAhB,CAAf;AACA,SAAKuoB,GAAL,GAAW,IAAX;;AAEA,SAAKC,aAAL;AACD,GApBiC;;;AAwBhB,aAAPpa,OAAO,GAAG;AACnB,WAAOA,SAAP;AACD;;AAEc,aAAJzL,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD;;AAEe,aAALlD,KAAK,GAAG;AACjB,WAAOA,OAAP;AACD;;AAEqB,aAAXkP,WAAW,GAAG;AACvB,WAAOA,aAAP;AACD,GAtCiC;;;AA0ClC8Z,EAAAA,MAAM,GAAG;AACP,SAAKN,UAAL,GAAkB,IAAlB;AACD;;AAEDO,EAAAA,OAAO,GAAG;AACR,SAAKP,UAAL,GAAkB,KAAlB;AACD;;AAEDQ,EAAAA,aAAa,GAAG;AACd,SAAKR,UAAL,GAAkB,CAAC,KAAKA,UAAxB;AACD;;AAED9b,EAAAA,MAAM,CAAChH,KAAD,EAAQ;AACZ,QAAI,CAAC,KAAK8iB,UAAV,EAAsB;AACpB;AACD;;AAED,QAAI9iB,KAAJ,EAAW;AACT,YAAMsX,OAAO,GAAG,KAAKiM,4BAAL,CAAkCvjB,KAAlC,CAAhB;;AAEAsX,MAAAA,OAAO,CAAC2L,cAAR,CAAuBpL,KAAvB,GAA+B,CAACP,OAAO,CAAC2L,cAAR,CAAuBpL,KAAvD;;AAEA,UAAIP,OAAO,CAACkM,oBAAR,EAAJ,EAAoC;AAClClM,QAAAA,OAAO,CAACmM,MAAR,CAAe,IAAf,EAAqBnM,OAArB;AACD,OAFD,MAEO;AACLA,QAAAA,OAAO,CAACoM,MAAR,CAAe,IAAf,EAAqBpM,OAArB;AACD;AACF,KAVD,MAUO;AACL,UAAI,KAAKqM,aAAL,GAAqB/nB,SAArB,CAA+BC,QAA/B,CAAwCkK,iBAAxC,CAAJ,EAA8D;AAC5D,aAAK2d,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;AACA;AACD;;AAED,WAAKD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;AACD;AACF;;AAED1e,EAAAA,OAAO,GAAG;AACRsJ,IAAAA,YAAY,CAAC,KAAK0U,QAAN,CAAZ;AAEA5iB,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKwE,QAAL,CAAc2B,OAAd,CAAuB,IAAG8b,gBAAiB,EAA3C,CAAjB,EAAgE,eAAhE,EAAiF,KAAKuB,iBAAtF;;AAEA,QAAI,KAAKV,GAAT,EAAc;AACZ,WAAKA,GAAL,CAAS3e,MAAT;AACD;;AAED,QAAI,KAAK4Q,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAakB,OAAb;AACD;;AAED,UAAMtR,OAAN;AACD;;AAEDiN,EAAAA,IAAI,GAAG;AACL,QAAI,KAAKpN,QAAL,CAAc8N,KAAd,CAAoBqC,OAApB,KAAgC,MAApC,EAA4C;AAC1C,YAAM,IAAIxP,KAAJ,CAAU,qCAAV,CAAN;AACD;;AAED,QAAI,EAAE,KAAKse,aAAL,MAAwB,KAAKf,UAA/B,CAAJ,EAAgD;AAC9C;AACD;;AAED,UAAMpN,SAAS,GAAGvV,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC,KAAKD,WAAL,CAAiBvK,KAAjB,CAAuBynB,IAA3D,CAAlB;AACA,UAAMiC,UAAU,GAAG9nB,cAAc,CAAC,KAAK4I,QAAN,CAAjC;AACA,UAAMmf,UAAU,GAAGD,UAAU,KAAK,IAAf,GACjB,KAAKlf,QAAL,CAAcof,aAAd,CAA4BvtB,eAA5B,CAA4CoF,QAA5C,CAAqD,KAAK+I,QAA1D,CADiB,GAEjBkf,UAAU,CAACjoB,QAAX,CAAoB,KAAK+I,QAAzB,CAFF;;AAIA,QAAI8Q,SAAS,CAAC1S,gBAAV,IAA8B,CAAC+gB,UAAnC,EAA+C;AAC7C;AACD;;AAED,UAAMb,GAAG,GAAG,KAAKS,aAAL,EAAZ;AACA,UAAMM,KAAK,GAAGvrB,MAAM,CAAC,KAAKiM,WAAL,CAAiBrH,IAAlB,CAApB;AAEA4lB,IAAAA,GAAG,CAACjc,YAAJ,CAAiB,IAAjB,EAAuBgd,KAAvB;;AACA,SAAKrf,QAAL,CAAcqC,YAAd,CAA2B,kBAA3B,EAA+Cgd,KAA/C;;AAEA,SAAKC,UAAL;;AAEA,QAAI,KAAK/X,OAAL,CAAa0U,SAAjB,EAA4B;AAC1BqC,MAAAA,GAAG,CAACtnB,SAAJ,CAAc4S,GAAd,CAAkB1I,iBAAlB;AACD;;AAED,UAAMiR,SAAS,GAAG,OAAO,KAAK5K,OAAL,CAAa4K,SAApB,KAAkC,UAAlC,GAChB,KAAK5K,OAAL,CAAa4K,SAAb,CAAuBjgB,IAAvB,CAA4B,IAA5B,EAAkCosB,GAAlC,EAAuC,KAAKte,QAA5C,CADgB,GAEhB,KAAKuH,OAAL,CAAa4K,SAFf;;AAIA,UAAMoN,UAAU,GAAG,KAAKC,cAAL,CAAoBrN,SAApB,CAAnB;;AACA,SAAKsN,mBAAL,CAAyBF,UAAzB;;AAEA,UAAM;AAAEhS,MAAAA;AAAF,QAAgB,KAAKhG,OAA3B;AACAtH,IAAAA,IAAI,CAACd,GAAL,CAASmf,GAAT,EAAc,KAAKve,WAAL,CAAiBG,QAA/B,EAAyC,IAAzC;;AAEA,QAAI,CAAC,KAAKF,QAAL,CAAcof,aAAd,CAA4BvtB,eAA5B,CAA4CoF,QAA5C,CAAqD,KAAKqnB,GAA1D,CAAL,EAAqE;AACnE/Q,MAAAA,SAAS,CAAC6H,WAAV,CAAsBkJ,GAAtB;AACA/iB,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC,KAAKD,WAAL,CAAiBvK,KAAjB,CAAuB2nB,QAA3D;AACD;;AAED,QAAI,KAAK5M,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAamB,MAAb;AACD,KAFD,MAEO;AACL,WAAKnB,OAAL,GAAeQ,MAAM,CAACO,YAAP,CAAoB,KAAKtR,QAAzB,EAAmCse,GAAnC,EAAwC,KAAKrN,gBAAL,CAAsBsO,UAAtB,CAAxC,CAAf;AACD;;AAEDjB,IAAAA,GAAG,CAACtnB,SAAJ,CAAc4S,GAAd,CAAkBzI,iBAAlB;AAEA,UAAMob,WAAW,GAAG,OAAO,KAAKhV,OAAL,CAAagV,WAApB,KAAoC,UAApC,GAAiD,KAAKhV,OAAL,CAAagV,WAAb,EAAjD,GAA8E,KAAKhV,OAAL,CAAagV,WAA/G;;AACA,QAAIA,WAAJ,EAAiB;AACf+B,MAAAA,GAAG,CAACtnB,SAAJ,CAAc4S,GAAd,CAAkB,GAAG2S,WAAW,CAAC9nB,KAAZ,CAAkB,GAAlB,CAArB;AACD,KAzDI;AA4DL;AACA;AACA;;;AACA,QAAI,kBAAkB7C,QAAQ,CAACC,eAA/B,EAAgD;AAC9C,SAAGC,MAAH,CAAU,GAAGF,QAAQ,CAACkG,IAAT,CAAczF,QAA3B,EAAqC8D,OAArC,CAA6CxE,OAAO,IAAI;AACtD4J,QAAAA,YAAY,CAACiC,EAAb,CAAgB7L,OAAhB,EAAyB,WAAzB,EAAsC8F,IAAtC;AACD,OAFD;AAGD;;AAED,UAAMuW,QAAQ,GAAG,MAAM;AACrB,YAAM0R,cAAc,GAAG,KAAKtB,WAA5B;AAEA,WAAKA,WAAL,GAAmB,IAAnB;AACA7iB,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC,KAAKD,WAAL,CAAiBvK,KAAjB,CAAuB0nB,KAA3D;;AAEA,UAAIwC,cAAc,KAAK/B,eAAvB,EAAwC;AACtC,aAAKmB,MAAL,CAAY,IAAZ,EAAkB,IAAlB;AACD;AACF,KATD;;AAWA,UAAMte,UAAU,GAAG,KAAK8d,GAAL,CAAStnB,SAAT,CAAmBC,QAAnB,CAA4BiK,iBAA5B,CAAnB;;AACA,SAAKX,cAAL,CAAoByN,QAApB,EAA8B,KAAKsQ,GAAnC,EAAwC9d,UAAxC;AACD;;AAED2M,EAAAA,IAAI,GAAG;AACL,QAAI,CAAC,KAAKoD,OAAV,EAAmB;AACjB;AACD;;AAED,UAAM+N,GAAG,GAAG,KAAKS,aAAL,EAAZ;;AACA,UAAM/Q,QAAQ,GAAG,MAAM;AACrB,UAAI,KAAK4Q,oBAAL,EAAJ,EAAiC;AAC/B;AACD;;AAED,UAAI,KAAKR,WAAL,KAAqBV,gBAAzB,EAA2C;AACzCY,QAAAA,GAAG,CAAC3e,MAAJ;AACD;;AAED,WAAKggB,cAAL;;AACA,WAAK3f,QAAL,CAAc8C,eAAd,CAA8B,kBAA9B;;AACAvH,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC,KAAKD,WAAL,CAAiBvK,KAAjB,CAAuBwnB,MAA3D;;AAEA,UAAI,KAAKzM,OAAT,EAAkB;AAChB,aAAKA,OAAL,CAAakB,OAAb;;AACA,aAAKlB,OAAL,GAAe,IAAf;AACD;AACF,KAjBD;;AAmBA,UAAMoB,SAAS,GAAGpW,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC,KAAKD,WAAL,CAAiBvK,KAAjB,CAAuBunB,IAA3D,CAAlB;;AACA,QAAIpL,SAAS,CAACvT,gBAAd,EAAgC;AAC9B;AACD;;AAEDkgB,IAAAA,GAAG,CAACtnB,SAAJ,CAAc2I,MAAd,CAAqBwB,iBAArB,EA9BK;AAiCL;;AACA,QAAI,kBAAkBvP,QAAQ,CAACC,eAA/B,EAAgD;AAC9C,SAAGC,MAAH,CAAU,GAAGF,QAAQ,CAACkG,IAAT,CAAczF,QAA3B,EACG8D,OADH,CACWxE,OAAO,IAAI4J,YAAY,CAACC,GAAb,CAAiB7J,OAAjB,EAA0B,WAA1B,EAAuC8F,IAAvC,CADtB;AAED;;AAED,SAAK4mB,cAAL,CAAoBN,aAApB,IAAqC,KAArC;AACA,SAAKM,cAAL,CAAoBP,aAApB,IAAqC,KAArC;AACA,SAAKO,cAAL,CAAoBR,aAApB,IAAqC,KAArC;AAEA,UAAMrd,UAAU,GAAG,KAAK8d,GAAL,CAAStnB,SAAT,CAAmBC,QAAnB,CAA4BiK,iBAA5B,CAAnB;;AACA,SAAKX,cAAL,CAAoByN,QAApB,EAA8B,KAAKsQ,GAAnC,EAAwC9d,UAAxC;;AACA,SAAK4d,WAAL,GAAmB,EAAnB;AACD;;AAED1M,EAAAA,MAAM,GAAG;AACP,QAAI,KAAKnB,OAAL,KAAiB,IAArB,EAA2B;AACzB,WAAKA,OAAL,CAAamB,MAAb;AACD;AACF,GAvOiC;;;AA2OlCuN,EAAAA,aAAa,GAAG;AACd,WAAO9hB,OAAO,CAAC,KAAKyiB,QAAL,EAAD,CAAd;AACD;;AAEDb,EAAAA,aAAa,GAAG;AACd,QAAI,KAAKT,GAAT,EAAc;AACZ,aAAO,KAAKA,GAAZ;AACD;;AAED,UAAM3sB,OAAO,GAAGC,QAAQ,CAACsjB,aAAT,CAAuB,KAAvB,CAAhB;AACAvjB,IAAAA,OAAO,CAACkqB,SAAR,GAAoB,KAAKtU,OAAL,CAAa2U,QAAjC;AAEA,SAAKoC,GAAL,GAAW3sB,OAAO,CAACU,QAAR,CAAiB,CAAjB,CAAX;AACA,WAAO,KAAKisB,GAAZ;AACD;;AAEDgB,EAAAA,UAAU,GAAG;AACX,UAAMhB,GAAG,GAAG,KAAKS,aAAL,EAAZ;AACA,SAAKc,iBAAL,CAAuBruB,cAAc,CAACW,OAAf,CAAuByrB,sBAAvB,EAA+CU,GAA/C,CAAvB,EAA4E,KAAKsB,QAAL,EAA5E;AACAtB,IAAAA,GAAG,CAACtnB,SAAJ,CAAc2I,MAAd,CAAqBuB,iBAArB,EAAsCC,iBAAtC;AACD;;AAED0e,EAAAA,iBAAiB,CAACluB,OAAD,EAAUmuB,OAAV,EAAmB;AAClC,QAAInuB,OAAO,KAAK,IAAhB,EAAsB;AACpB;AACD;;AAED,QAAI8D,SAAS,CAACqqB,OAAD,CAAb,EAAwB;AACtBA,MAAAA,OAAO,GAAGnqB,UAAU,CAACmqB,OAAD,CAApB,CADsB;;AAItB,UAAI,KAAKvY,OAAL,CAAa8U,IAAjB,EAAuB;AACrB,YAAIyD,OAAO,CAACntB,UAAR,KAAuBhB,OAA3B,EAAoC;AAClCA,UAAAA,OAAO,CAACkqB,SAAR,GAAoB,EAApB;AACAlqB,UAAAA,OAAO,CAACyjB,WAAR,CAAoB0K,OAApB;AACD;AACF,OALD,MAKO;AACLnuB,QAAAA,OAAO,CAACouB,WAAR,GAAsBD,OAAO,CAACC,WAA9B;AACD;;AAED;AACD;;AAED,QAAI,KAAKxY,OAAL,CAAa8U,IAAjB,EAAuB;AACrB,UAAI,KAAK9U,OAAL,CAAaiV,QAAjB,EAA2B;AACzBsD,QAAAA,OAAO,GAAG9E,YAAY,CAAC8E,OAAD,EAAU,KAAKvY,OAAL,CAAa2T,SAAvB,EAAkC,KAAK3T,OAAL,CAAa4T,UAA/C,CAAtB;AACD;;AAEDxpB,MAAAA,OAAO,CAACkqB,SAAR,GAAoBiE,OAApB;AACD,KAND,MAMO;AACLnuB,MAAAA,OAAO,CAACouB,WAAR,GAAsBD,OAAtB;AACD;AACF;;AAEDF,EAAAA,QAAQ,GAAG;AACT,QAAIzD,KAAK,GAAG,KAAKnc,QAAL,CAAc3L,YAAd,CAA2B,wBAA3B,CAAZ;;AAEA,QAAI,CAAC8nB,KAAL,EAAY;AACVA,MAAAA,KAAK,GAAG,OAAO,KAAK5U,OAAL,CAAa4U,KAApB,KAA8B,UAA9B,GACN,KAAK5U,OAAL,CAAa4U,KAAb,CAAmBjqB,IAAnB,CAAwB,KAAK8N,QAA7B,CADM,GAEN,KAAKuH,OAAL,CAAa4U,KAFf;AAGD;;AAED,WAAOA,KAAP;AACD;;AAED6D,EAAAA,gBAAgB,CAACT,UAAD,EAAa;AAC3B,QAAIA,UAAU,KAAK,OAAnB,EAA4B;AAC1B,aAAO,KAAP;AACD;;AAED,QAAIA,UAAU,KAAK,MAAnB,EAA2B;AACzB,aAAO,OAAP;AACD;;AAED,WAAOA,UAAP;AACD,GAvTiC;;;AA2TlCZ,EAAAA,4BAA4B,CAACvjB,KAAD,EAAQsX,OAAR,EAAiB;AAC3C,UAAMuN,OAAO,GAAG,KAAKlgB,WAAL,CAAiBG,QAAjC;AACAwS,IAAAA,OAAO,GAAGA,OAAO,IAAIzS,IAAI,CAAClB,GAAL,CAAS3D,KAAK,CAACC,cAAf,EAA+B4kB,OAA/B,CAArB;;AAEA,QAAI,CAACvN,OAAL,EAAc;AACZA,MAAAA,OAAO,GAAG,IAAI,KAAK3S,WAAT,CAAqB3E,KAAK,CAACC,cAA3B,EAA2C,KAAK6kB,kBAAL,EAA3C,CAAV;AACAjgB,MAAAA,IAAI,CAACd,GAAL,CAAS/D,KAAK,CAACC,cAAf,EAA+B4kB,OAA/B,EAAwCvN,OAAxC;AACD;;AAED,WAAOA,OAAP;AACD;;AAEDX,EAAAA,UAAU,GAAG;AACX,UAAM;AAAE1O,MAAAA;AAAF,QAAa,KAAKkE,OAAxB;;AAEA,QAAI,OAAOlE,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,aAAOA,MAAM,CAAC5O,KAAP,CAAa,GAAb,EAAkBud,GAAlB,CAAsBxP,GAAG,IAAIrN,MAAM,CAACoV,QAAP,CAAgB/H,GAAhB,EAAqB,EAArB,CAA7B,CAAP;AACD;;AAED,QAAI,OAAOa,MAAP,KAAkB,UAAtB,EAAkC;AAChC,aAAO4O,UAAU,IAAI5O,MAAM,CAAC4O,UAAD,EAAa,KAAKjS,QAAlB,CAA3B;AACD;;AAED,WAAOqD,MAAP;AACD;;AAED4N,EAAAA,gBAAgB,CAACsO,UAAD,EAAa;AAC3B,UAAMrN,qBAAqB,GAAG;AAC5BC,MAAAA,SAAS,EAAEoN,UADiB;AAE5BpO,MAAAA,SAAS,EAAE,CACT;AACE1Y,QAAAA,IAAI,EAAE,MADR;AAEE2Z,QAAAA,OAAO,EAAE;AACPkK,UAAAA,kBAAkB,EAAE,KAAK/U,OAAL,CAAa+U;AAD1B;AAFX,OADS,EAOT;AACE7jB,QAAAA,IAAI,EAAE,QADR;AAEE2Z,QAAAA,OAAO,EAAE;AACP/O,UAAAA,MAAM,EAAE,KAAK0O,UAAL;AADD;AAFX,OAPS,EAaT;AACEtZ,QAAAA,IAAI,EAAE,iBADR;AAEE2Z,QAAAA,OAAO,EAAE;AACPnC,UAAAA,QAAQ,EAAE,KAAK1I,OAAL,CAAa0I;AADhB;AAFX,OAbS,EAmBT;AACExX,QAAAA,IAAI,EAAE,OADR;AAEE2Z,QAAAA,OAAO,EAAE;AACPzgB,UAAAA,OAAO,EAAG,IAAG,KAAKoO,WAAL,CAAiBrH,IAAK;AAD5B;AAFX,OAnBS,EAyBT;AACED,QAAAA,IAAI,EAAE,UADR;AAEE4Y,QAAAA,OAAO,EAAE,IAFX;AAGE8O,QAAAA,KAAK,EAAE,YAHT;AAIEvnB,QAAAA,EAAE,EAAEkJ,IAAI,IAAI,KAAKse,4BAAL,CAAkCte,IAAlC;AAJd,OAzBS,CAFiB;AAkC5Bue,MAAAA,aAAa,EAAEve,IAAI,IAAI;AACrB,YAAIA,IAAI,CAACsQ,OAAL,CAAaD,SAAb,KAA2BrQ,IAAI,CAACqQ,SAApC,EAA+C;AAC7C,eAAKiO,4BAAL,CAAkCte,IAAlC;AACD;AACF;AAtC2B,KAA9B;AAyCA,WAAO,EACL,GAAGoQ,qBADE;AAEL,UAAI,OAAO,KAAK3K,OAAL,CAAa6I,YAApB,KAAqC,UAArC,GAAkD,KAAK7I,OAAL,CAAa6I,YAAb,CAA0B8B,qBAA1B,CAAlD,GAAqG,KAAK3K,OAAL,CAAa6I,YAAtH;AAFK,KAAP;AAID;;AAEDqP,EAAAA,mBAAmB,CAACF,UAAD,EAAa;AAC9B,SAAKR,aAAL,GAAqB/nB,SAArB,CAA+B4S,GAA/B,CAAoC,GAAEkS,cAAa,IAAG,KAAKkE,gBAAL,CAAsBT,UAAtB,CAAkC,EAAxF;AACD;;AAEDC,EAAAA,cAAc,CAACrN,SAAD,EAAY;AACxB,WAAOsK,aAAa,CAACtK,SAAS,CAACxb,WAAV,EAAD,CAApB;AACD;;AAED4nB,EAAAA,aAAa,GAAG;AACd,UAAM+B,QAAQ,GAAG,KAAK/Y,OAAL,CAAaxJ,OAAb,CAAqBtJ,KAArB,CAA2B,GAA3B,CAAjB;;AAEA6rB,IAAAA,QAAQ,CAACnqB,OAAT,CAAiB4H,OAAO,IAAI;AAC1B,UAAIA,OAAO,KAAK,OAAhB,EAAyB;AACvBxC,QAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+B,KAAKD,WAAL,CAAiBvK,KAAjB,CAAuB4nB,KAAtD,EAA6D,KAAK7V,OAAL,CAAa7V,QAA1E,EAAoF0J,KAAK,IAAI,KAAKgH,MAAL,CAAYhH,KAAZ,CAA7F;AACD,OAFD,MAEO,IAAI2C,OAAO,KAAKigB,cAAhB,EAAgC;AACrC,cAAMuC,OAAO,GAAGxiB,OAAO,KAAK8f,aAAZ,GACd,KAAK9d,WAAL,CAAiBvK,KAAjB,CAAuB+nB,UADT,GAEd,KAAKxd,WAAL,CAAiBvK,KAAjB,CAAuB6nB,OAFzB;AAGA,cAAMmD,QAAQ,GAAGziB,OAAO,KAAK8f,aAAZ,GACf,KAAK9d,WAAL,CAAiBvK,KAAjB,CAAuBgoB,UADR,GAEf,KAAKzd,WAAL,CAAiBvK,KAAjB,CAAuB8nB,QAFzB;AAIA/hB,QAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BugB,OAA/B,EAAwC,KAAKhZ,OAAL,CAAa7V,QAArD,EAA+D0J,KAAK,IAAI,KAAKyjB,MAAL,CAAYzjB,KAAZ,CAAxE;AACAG,QAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BwgB,QAA/B,EAAyC,KAAKjZ,OAAL,CAAa7V,QAAtD,EAAgE0J,KAAK,IAAI,KAAK0jB,MAAL,CAAY1jB,KAAZ,CAAzE;AACD;AACF,KAdD;;AAgBA,SAAK4jB,iBAAL,GAAyB,MAAM;AAC7B,UAAI,KAAKhf,QAAT,EAAmB;AACjB,aAAKmN,IAAL;AACD;AACF,KAJD;;AAMA5R,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAAL,CAAc2B,OAAd,CAAuB,IAAG8b,gBAAiB,EAA3C,CAAhB,EAA+D,eAA/D,EAAgF,KAAKuB,iBAArF;;AAEA,QAAI,KAAKzX,OAAL,CAAa7V,QAAjB,EAA2B;AACzB,WAAK6V,OAAL,GAAe,EACb,GAAG,KAAKA,OADK;AAEbxJ,QAAAA,OAAO,EAAE,QAFI;AAGbrM,QAAAA,QAAQ,EAAE;AAHG,OAAf;AAKD,KAND,MAMO;AACL,WAAK+uB,SAAL;AACD;AACF;;AAEDA,EAAAA,SAAS,GAAG;AACV,UAAMtE,KAAK,GAAG,KAAKnc,QAAL,CAAc3L,YAAd,CAA2B,OAA3B,CAAd;;AACA,UAAMqsB,iBAAiB,GAAG,OAAO,KAAK1gB,QAAL,CAAc3L,YAAd,CAA2B,wBAA3B,CAAjC;;AAEA,QAAI8nB,KAAK,IAAIuE,iBAAiB,KAAK,QAAnC,EAA6C;AAC3C,WAAK1gB,QAAL,CAAcqC,YAAd,CAA2B,wBAA3B,EAAqD8Z,KAAK,IAAI,EAA9D;;AACA,UAAIA,KAAK,IAAI,CAAC,KAAKnc,QAAL,CAAc3L,YAAd,CAA2B,YAA3B,CAAV,IAAsD,CAAC,KAAK2L,QAAL,CAAc+f,WAAzE,EAAsF;AACpF,aAAK/f,QAAL,CAAcqC,YAAd,CAA2B,YAA3B,EAAyC8Z,KAAzC;AACD;;AAED,WAAKnc,QAAL,CAAcqC,YAAd,CAA2B,OAA3B,EAAoC,EAApC;AACD;AACF;;AAEDwc,EAAAA,MAAM,CAACzjB,KAAD,EAAQsX,OAAR,EAAiB;AACrBA,IAAAA,OAAO,GAAG,KAAKiM,4BAAL,CAAkCvjB,KAAlC,EAAyCsX,OAAzC,CAAV;;AAEA,QAAItX,KAAJ,EAAW;AACTsX,MAAAA,OAAO,CAAC2L,cAAR,CACEjjB,KAAK,CAACK,IAAN,KAAe,SAAf,GAA2BqiB,aAA3B,GAA2CD,aAD7C,IAEI,IAFJ;AAGD;;AAED,QAAInL,OAAO,CAACqM,aAAR,GAAwB/nB,SAAxB,CAAkCC,QAAlC,CAA2CkK,iBAA3C,KAA+DuR,OAAO,CAAC0L,WAAR,KAAwBV,gBAA3F,EAA6G;AAC3GhL,MAAAA,OAAO,CAAC0L,WAAR,GAAsBV,gBAAtB;AACA;AACD;;AAEDjU,IAAAA,YAAY,CAACiJ,OAAO,CAACyL,QAAT,CAAZ;AAEAzL,IAAAA,OAAO,CAAC0L,WAAR,GAAsBV,gBAAtB;;AAEA,QAAI,CAAChL,OAAO,CAACnL,OAAR,CAAgB6U,KAAjB,IAA0B,CAAC1J,OAAO,CAACnL,OAAR,CAAgB6U,KAAhB,CAAsBhP,IAArD,EAA2D;AACzDsF,MAAAA,OAAO,CAACtF,IAAR;AACA;AACD;;AAEDsF,IAAAA,OAAO,CAACyL,QAAR,GAAmBzkB,UAAU,CAAC,MAAM;AAClC,UAAIgZ,OAAO,CAAC0L,WAAR,KAAwBV,gBAA5B,EAA8C;AAC5ChL,QAAAA,OAAO,CAACtF,IAAR;AACD;AACF,KAJ4B,EAI1BsF,OAAO,CAACnL,OAAR,CAAgB6U,KAAhB,CAAsBhP,IAJI,CAA7B;AAKD;;AAED0R,EAAAA,MAAM,CAAC1jB,KAAD,EAAQsX,OAAR,EAAiB;AACrBA,IAAAA,OAAO,GAAG,KAAKiM,4BAAL,CAAkCvjB,KAAlC,EAAyCsX,OAAzC,CAAV;;AAEA,QAAItX,KAAJ,EAAW;AACTsX,MAAAA,OAAO,CAAC2L,cAAR,CACEjjB,KAAK,CAACK,IAAN,KAAe,UAAf,GAA4BqiB,aAA5B,GAA4CD,aAD9C,IAEInL,OAAO,CAAC1S,QAAR,CAAiB/I,QAAjB,CAA0BmE,KAAK,CAAC0B,aAAhC,CAFJ;AAGD;;AAED,QAAI4V,OAAO,CAACkM,oBAAR,EAAJ,EAAoC;AAClC;AACD;;AAEDnV,IAAAA,YAAY,CAACiJ,OAAO,CAACyL,QAAT,CAAZ;AAEAzL,IAAAA,OAAO,CAAC0L,WAAR,GAAsBT,eAAtB;;AAEA,QAAI,CAACjL,OAAO,CAACnL,OAAR,CAAgB6U,KAAjB,IAA0B,CAAC1J,OAAO,CAACnL,OAAR,CAAgB6U,KAAhB,CAAsBjP,IAArD,EAA2D;AACzDuF,MAAAA,OAAO,CAACvF,IAAR;AACA;AACD;;AAEDuF,IAAAA,OAAO,CAACyL,QAAR,GAAmBzkB,UAAU,CAAC,MAAM;AAClC,UAAIgZ,OAAO,CAAC0L,WAAR,KAAwBT,eAA5B,EAA6C;AAC3CjL,QAAAA,OAAO,CAACvF,IAAR;AACD;AACF,KAJ4B,EAI1BuF,OAAO,CAACnL,OAAR,CAAgB6U,KAAhB,CAAsBjP,IAJI,CAA7B;AAKD;;AAEDyR,EAAAA,oBAAoB,GAAG;AACrB,SAAK,MAAM7gB,OAAX,IAAsB,KAAKsgB,cAA3B,EAA2C;AACzC,UAAI,KAAKA,cAAL,CAAoBtgB,OAApB,CAAJ,EAAkC;AAChC,eAAO,IAAP;AACD;AACF;;AAED,WAAO,KAAP;AACD;;AAEDyJ,EAAAA,UAAU,CAACzR,MAAD,EAAS;AACjB,UAAM4qB,cAAc,GAAGhe,WAAW,CAACI,iBAAZ,CAA8B,KAAK/C,QAAnC,CAAvB;AAEA/J,IAAAA,MAAM,CAACC,IAAP,CAAYyqB,cAAZ,EAA4BxqB,OAA5B,CAAoCyqB,QAAQ,IAAI;AAC9C,UAAI5E,qBAAqB,CAACrf,GAAtB,CAA0BikB,QAA1B,CAAJ,EAAyC;AACvC,eAAOD,cAAc,CAACC,QAAD,CAArB;AACD;AACF,KAJD;AAMA7qB,IAAAA,MAAM,GAAG,EACP,GAAG,KAAKgK,WAAL,CAAiBoE,OADb;AAEP,SAAGwc,cAFI;AAGP,UAAI,OAAO5qB,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;AAHO,KAAT;AAMAA,IAAAA,MAAM,CAACwX,SAAP,GAAmBxX,MAAM,CAACwX,SAAP,KAAqB,KAArB,GAA6B3b,QAAQ,CAACkG,IAAtC,GAA6CnC,UAAU,CAACI,MAAM,CAACwX,SAAR,CAA1E;;AAEA,QAAI,OAAOxX,MAAM,CAACqmB,KAAd,KAAwB,QAA5B,EAAsC;AACpCrmB,MAAAA,MAAM,CAACqmB,KAAP,GAAe;AACbhP,QAAAA,IAAI,EAAErX,MAAM,CAACqmB,KADA;AAEbjP,QAAAA,IAAI,EAAEpX,MAAM,CAACqmB;AAFA,OAAf;AAID;;AAED,QAAI,OAAOrmB,MAAM,CAAComB,KAAd,KAAwB,QAA5B,EAAsC;AACpCpmB,MAAAA,MAAM,CAAComB,KAAP,GAAepmB,MAAM,CAAComB,KAAP,CAAaxoB,QAAb,EAAf;AACD;;AAED,QAAI,OAAOoC,MAAM,CAAC+pB,OAAd,KAA0B,QAA9B,EAAwC;AACtC/pB,MAAAA,MAAM,CAAC+pB,OAAP,GAAiB/pB,MAAM,CAAC+pB,OAAP,CAAensB,QAAf,EAAjB;AACD;;AAEDkC,IAAAA,eAAe,CAAC6C,MAAD,EAAO3C,MAAP,EAAe,KAAKgK,WAAL,CAAiB2E,WAAhC,CAAf;;AAEA,QAAI3O,MAAM,CAACymB,QAAX,EAAqB;AACnBzmB,MAAAA,MAAM,CAACmmB,QAAP,GAAkBlB,YAAY,CAACjlB,MAAM,CAACmmB,QAAR,EAAkBnmB,MAAM,CAACmlB,SAAzB,EAAoCnlB,MAAM,CAAColB,UAA3C,CAA9B;AACD;;AAED,WAAOplB,MAAP;AACD;;AAEDmqB,EAAAA,kBAAkB,GAAG;AACnB,UAAMnqB,MAAM,GAAG,EAAf;;AAEA,QAAI,KAAKwR,OAAT,EAAkB;AAChB,WAAK,MAAM1I,GAAX,IAAkB,KAAK0I,OAAvB,EAAgC;AAC9B,YAAI,KAAKxH,WAAL,CAAiBoE,OAAjB,CAAyBtF,GAAzB,MAAkC,KAAK0I,OAAL,CAAa1I,GAAb,CAAtC,EAAyD;AACvD9I,UAAAA,MAAM,CAAC8I,GAAD,CAAN,GAAc,KAAK0I,OAAL,CAAa1I,GAAb,CAAd;AACD;AACF;AACF;;AAED,WAAO9I,MAAP;AACD;;AAED4pB,EAAAA,cAAc,GAAG;AACf,UAAMrB,GAAG,GAAG,KAAKS,aAAL,EAAZ;AACA,UAAM8B,QAAQ,GAAGvC,GAAG,CAACjqB,YAAJ,CAAiB,OAAjB,EAA0BT,KAA1B,CAAgCmoB,oBAAhC,CAAjB;;AACA,QAAI8E,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAACjrB,MAAT,GAAkB,CAA3C,EAA8C;AAC5CirB,MAAAA,QAAQ,CAAC7O,GAAT,CAAa8O,KAAK,IAAIA,KAAK,CAACpsB,IAAN,EAAtB,EACGyB,OADH,CACW4qB,MAAM,IAAIzC,GAAG,CAACtnB,SAAJ,CAAc2I,MAAd,CAAqBohB,MAArB,CADrB;AAED;AACF;;AAEDX,EAAAA,4BAA4B,CAACnO,UAAD,EAAa;AACvC,UAAM;AAAE+O,MAAAA;AAAF,QAAY/O,UAAlB;;AAEA,QAAI,CAAC+O,KAAL,EAAY;AACV;AACD;;AAED,SAAK1C,GAAL,GAAW0C,KAAK,CAACvF,QAAN,CAAewF,MAA1B;;AACA,SAAKtB,cAAL;;AACA,SAAKF,mBAAL,CAAyB,KAAKD,cAAL,CAAoBwB,KAAK,CAAC7O,SAA1B,CAAzB;AACD,GAhlBiC;;;AAolBZ,SAAftZ,eAAe,CAAC9C,MAAD,EAAS;AAC7B,WAAO,KAAK8L,IAAL,CAAU,YAAY;AAC3B,YAAMC,IAAI,GAAGmc,OAAO,CAACvd,mBAAR,CAA4B,IAA5B,EAAkC3K,MAAlC,CAAb;;AAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAO+L,IAAI,CAAC/L,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED+L,QAAAA,IAAI,CAAC/L,MAAD,CAAJ;AACD;AACF,KAVM,CAAP;AAWD;;AAhmBiC;AAmmBpC;AACA;AACA;AACA;AACA;AACA;;;AAEAuC,kBAAkB,CAAC2lB,OAAD,CAAlB;;ACxuBA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;;AAEA,MAAMvlB,MAAI,GAAG,SAAb;AACA,MAAMwH,UAAQ,GAAG,YAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAM4b,YAAY,GAAG,YAArB;AACA,MAAMC,kBAAkB,GAAG,IAAIvlB,MAAJ,CAAY,UAASslB,YAAa,MAAlC,EAAyC,GAAzC,CAA3B;AAEA,MAAM3X,SAAO,GAAG,EACd,GAAG8Z,OAAO,CAAC9Z,OADG;AAEdgO,EAAAA,SAAS,EAAE,OAFG;AAGd9O,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAHM;AAIdtF,EAAAA,OAAO,EAAE,OAJK;AAKd+hB,EAAAA,OAAO,EAAE,EALK;AAMd5D,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEE,kCAFF,GAGE,kCAHF,GAIA;AAVI,CAAhB;AAaA,MAAMxX,aAAW,GAAG,EAClB,GAAGuZ,OAAO,CAACvZ,WADO;AAElBob,EAAAA,OAAO,EAAE;AAFS,CAApB;AAKA,MAAMtqB,OAAK,GAAG;AACZunB,EAAAA,IAAI,EAAG,OAAM3c,WAAU,EADX;AAEZ4c,EAAAA,MAAM,EAAG,SAAQ5c,WAAU,EAFf;AAGZ6c,EAAAA,IAAI,EAAG,OAAM7c,WAAU,EAHX;AAIZ8c,EAAAA,KAAK,EAAG,QAAO9c,WAAU,EAJb;AAKZ+c,EAAAA,QAAQ,EAAG,WAAU/c,WAAU,EALnB;AAMZgd,EAAAA,KAAK,EAAG,QAAOhd,WAAU,EANb;AAOZid,EAAAA,OAAO,EAAG,UAASjd,WAAU,EAPjB;AAQZkd,EAAAA,QAAQ,EAAG,WAAUld,WAAU,EARnB;AASZmd,EAAAA,UAAU,EAAG,aAAYnd,WAAU,EATvB;AAUZod,EAAAA,UAAU,EAAG,aAAYpd,WAAU;AAVvB,CAAd;AAaA,MAAMc,iBAAe,GAAG,MAAxB;AACA,MAAMC,iBAAe,GAAG,MAAxB;AAEA,MAAM+f,cAAc,GAAG,iBAAvB;AACA,MAAMC,gBAAgB,GAAG,eAAzB;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,OAAN,SAAsBnD,OAAtB,CAA8B;AAC5B;AAEkB,aAAP9Z,OAAO,GAAG;AACnB,WAAOA,SAAP;AACD;;AAEc,aAAJzL,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD;;AAEe,aAALlD,KAAK,GAAG;AACjB,WAAOA,OAAP;AACD;;AAEqB,aAAXkP,WAAW,GAAG;AACvB,WAAOA,aAAP;AACD,GAjB2B;;;AAqB5Bua,EAAAA,aAAa,GAAG;AACd,WAAO,KAAKW,QAAL,MAAmB,KAAKyB,WAAL,EAA1B;AACD;;AAEDtC,EAAAA,aAAa,GAAG;AACd,QAAI,KAAKT,GAAT,EAAc;AACZ,aAAO,KAAKA,GAAZ;AACD;;AAED,SAAKA,GAAL,GAAW,MAAMS,aAAN,EAAX;;AAEA,QAAI,CAAC,KAAKa,QAAL,EAAL,EAAsB;AACpBpuB,MAAAA,cAAc,CAACW,OAAf,CAAuB+uB,cAAvB,EAAuC,KAAK5C,GAA5C,EAAiD3e,MAAjD;AACD;;AAED,QAAI,CAAC,KAAK0hB,WAAL,EAAL,EAAyB;AACvB7vB,MAAAA,cAAc,CAACW,OAAf,CAAuBgvB,gBAAvB,EAAyC,KAAK7C,GAA9C,EAAmD3e,MAAnD;AACD;;AAED,WAAO,KAAK2e,GAAZ;AACD;;AAEDgB,EAAAA,UAAU,GAAG;AACX,UAAMhB,GAAG,GAAG,KAAKS,aAAL,EAAZ,CADW;;AAIX,SAAKc,iBAAL,CAAuBruB,cAAc,CAACW,OAAf,CAAuB+uB,cAAvB,EAAuC5C,GAAvC,CAAvB,EAAoE,KAAKsB,QAAL,EAApE;;AACA,QAAIE,OAAO,GAAG,KAAKuB,WAAL,EAAd;;AACA,QAAI,OAAOvB,OAAP,KAAmB,UAAvB,EAAmC;AACjCA,MAAAA,OAAO,GAAGA,OAAO,CAAC5tB,IAAR,CAAa,KAAK8N,QAAlB,CAAV;AACD;;AAED,SAAK6f,iBAAL,CAAuBruB,cAAc,CAACW,OAAf,CAAuBgvB,gBAAvB,EAAyC7C,GAAzC,CAAvB,EAAsEwB,OAAtE;AAEAxB,IAAAA,GAAG,CAACtnB,SAAJ,CAAc2I,MAAd,CAAqBuB,iBAArB,EAAsCC,iBAAtC;AACD,GAxD2B;;;AA4D5Bse,EAAAA,mBAAmB,CAACF,UAAD,EAAa;AAC9B,SAAKR,aAAL,GAAqB/nB,SAArB,CAA+B4S,GAA/B,CAAoC,GAAEkS,YAAa,IAAG,KAAKkE,gBAAL,CAAsBT,UAAtB,CAAkC,EAAxF;AACD;;AAED8B,EAAAA,WAAW,GAAG;AACZ,WAAO,KAAKrhB,QAAL,CAAc3L,YAAd,CAA2B,iBAA3B,KAAiD,KAAKkT,OAAL,CAAauY,OAArE;AACD;;AAEDH,EAAAA,cAAc,GAAG;AACf,UAAMrB,GAAG,GAAG,KAAKS,aAAL,EAAZ;AACA,UAAM8B,QAAQ,GAAGvC,GAAG,CAACjqB,YAAJ,CAAiB,OAAjB,EAA0BT,KAA1B,CAAgCmoB,kBAAhC,CAAjB;;AACA,QAAI8E,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAACjrB,MAAT,GAAkB,CAA3C,EAA8C;AAC5CirB,MAAAA,QAAQ,CAAC7O,GAAT,CAAa8O,KAAK,IAAIA,KAAK,CAACpsB,IAAN,EAAtB,EACGyB,OADH,CACW4qB,MAAM,IAAIzC,GAAG,CAACtnB,SAAJ,CAAc2I,MAAd,CAAqBohB,MAArB,CADrB;AAED;AACF,GA3E2B;;;AA+EN,SAAfloB,eAAe,CAAC9C,MAAD,EAAS;AAC7B,WAAO,KAAK8L,IAAL,CAAU,YAAY;AAC3B,YAAMC,IAAI,GAAGsf,OAAO,CAAC1gB,mBAAR,CAA4B,IAA5B,EAAkC3K,MAAlC,CAAb;;AAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAO+L,IAAI,CAAC/L,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED+L,QAAAA,IAAI,CAAC/L,MAAD,CAAJ;AACD;AACF,KAVM,CAAP;AAWD;;AA3F2B;AA8F9B;AACA;AACA;AACA;AACA;AACA;;;AAEAuC,kBAAkB,CAAC8oB,OAAD,CAAlB;;ACvKA;AACA;AACA;AACA;AACA;AACA;AAcA;AACA;AACA;AACA;AACA;;AAEA,MAAM1oB,MAAI,GAAG,WAAb;AACA,MAAMwH,UAAQ,GAAG,cAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMU,cAAY,GAAG,WAArB;AAEA,MAAMuD,SAAO,GAAG;AACdd,EAAAA,MAAM,EAAE,EADM;AAEdie,EAAAA,MAAM,EAAE,MAFM;AAGd9nB,EAAAA,MAAM,EAAE;AAHM,CAAhB;AAMA,MAAMkL,aAAW,GAAG;AAClBrB,EAAAA,MAAM,EAAE,QADU;AAElBie,EAAAA,MAAM,EAAE,QAFU;AAGlB9nB,EAAAA,MAAM,EAAE;AAHU,CAApB;AAMA,MAAM+nB,cAAc,GAAI,WAAUnhB,WAAU,EAA5C;AACA,MAAMohB,YAAY,GAAI,SAAQphB,WAAU,EAAxC;AACA,MAAMuF,mBAAmB,GAAI,OAAMvF,WAAU,GAAEQ,cAAa,EAA5D;AAEA,MAAM6gB,wBAAwB,GAAG,eAAjC;AACA,MAAMxf,mBAAiB,GAAG,QAA1B;AAEA,MAAMyf,iBAAiB,GAAG,wBAA1B;AACA,MAAMC,yBAAuB,GAAG,mBAAhC;AACA,MAAMC,kBAAkB,GAAG,WAA3B;AACA,MAAMC,kBAAkB,GAAG,WAA3B;AACA,MAAMC,mBAAmB,GAAG,kBAA5B;AACA,MAAMC,mBAAiB,GAAG,WAA1B;AACA,MAAMC,0BAAwB,GAAG,kBAAjC;AAEA,MAAMC,aAAa,GAAG,QAAtB;AACA,MAAMC,eAAe,GAAG,UAAxB;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,SAAN,SAAwBriB,aAAxB,CAAsC;AACpCC,EAAAA,WAAW,CAACpO,OAAD,EAAUoE,MAAV,EAAkB;AAC3B,UAAMpE,OAAN;AACA,SAAKywB,cAAL,GAAsB,KAAKpiB,QAAL,CAAc6J,OAAd,KAA0B,MAA1B,GAAmC7U,MAAnC,GAA4C,KAAKgL,QAAvE;AACA,SAAKuH,OAAL,GAAe,KAAKC,UAAL,CAAgBzR,MAAhB,CAAf;AACA,SAAKgX,SAAL,GAAkB,GAAE,KAAKxF,OAAL,CAAa/N,MAAO,IAAGooB,kBAAmB,KAAI,KAAKra,OAAL,CAAa/N,MAAO,IAAGsoB,mBAAoB,KAAI,KAAKva,OAAL,CAAa/N,MAAO,KAAIioB,wBAAyB,EAAlK;AACA,SAAKY,QAAL,GAAgB,EAAhB;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACA,SAAKC,aAAL,GAAqB,IAArB;AACA,SAAKC,aAAL,GAAqB,CAArB;AAEAjnB,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK4kB,cAArB,EAAqCZ,YAArC,EAAmD,MAAM,KAAKiB,QAAL,EAAzD;AAEA,SAAKC,OAAL;;AACA,SAAKD,QAAL;AACD,GAfmC;;;AAmBlB,aAAPte,OAAO,GAAG;AACnB,WAAOA,SAAP;AACD;;AAEc,aAAJzL,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD,GAzBmC;;;AA6BpCgqB,EAAAA,OAAO,GAAG;AACR,UAAMC,UAAU,GAAG,KAAKP,cAAL,KAAwB,KAAKA,cAAL,CAAoBptB,MAA5C,GACjBitB,aADiB,GAEjBC,eAFF;AAIA,UAAMU,YAAY,GAAG,KAAKrb,OAAL,CAAa+Z,MAAb,KAAwB,MAAxB,GACnBqB,UADmB,GAEnB,KAAKpb,OAAL,CAAa+Z,MAFf;AAIA,UAAMuB,UAAU,GAAGD,YAAY,KAAKV,eAAjB,GACjB,KAAKY,aAAL,EADiB,GAEjB,CAFF;AAIA,SAAKT,QAAL,GAAgB,EAAhB;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACA,SAAKE,aAAL,GAAqB,KAAKO,gBAAL,EAArB;AAEA,UAAMC,OAAO,GAAGxxB,cAAc,CAACC,IAAf,CAAoB,KAAKsb,SAAzB,CAAhB;AAEAiW,IAAAA,OAAO,CAAChR,GAAR,CAAYrgB,OAAO,IAAI;AACrB,YAAMsxB,cAAc,GAAGtuB,sBAAsB,CAAChD,OAAD,CAA7C;AACA,YAAM6H,MAAM,GAAGypB,cAAc,GAAGzxB,cAAc,CAACW,OAAf,CAAuB8wB,cAAvB,CAAH,GAA4C,IAAzE;;AAEA,UAAIzpB,MAAJ,EAAY;AACV,cAAM0pB,SAAS,GAAG1pB,MAAM,CAAC+J,qBAAP,EAAlB;;AACA,YAAI2f,SAAS,CAACzP,KAAV,IAAmByP,SAAS,CAACC,MAAjC,EAAyC;AACvC,iBAAO,CACLxgB,WAAW,CAACigB,YAAD,CAAX,CAA0BppB,MAA1B,EAAkCgK,GAAlC,GAAwCqf,UADnC,EAELI,cAFK,CAAP;AAID;AACF;;AAED,aAAO,IAAP;AACD,KAfD,EAgBG3wB,MAhBH,CAgBU8wB,IAAI,IAAIA,IAhBlB,EAiBGC,IAjBH,CAiBQ,CAACjK,CAAD,EAAIE,CAAJ,KAAUF,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAC,CAAC,CAAD,CAjB1B,EAkBGnjB,OAlBH,CAkBWitB,IAAI,IAAI;AACf,WAAKf,QAAL,CAActvB,IAAd,CAAmBqwB,IAAI,CAAC,CAAD,CAAvB;;AACA,WAAKd,QAAL,CAAcvvB,IAAd,CAAmBqwB,IAAI,CAAC,CAAD,CAAvB;AACD,KArBH;AAsBD;;AAEDjjB,EAAAA,OAAO,GAAG;AACR5E,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK4mB,cAAtB,EAAsChiB,WAAtC;AACA,UAAMD,OAAN;AACD,GA3EmC;;;AA+EpCqH,EAAAA,UAAU,CAACzR,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,EACP,GAAGoO,SADI;AAEP,SAAGxB,WAAW,CAACI,iBAAZ,CAA8B,KAAK/C,QAAnC,CAFI;AAGP,UAAI,OAAOjK,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;AAHO,KAAT;;AAMA,QAAI,OAAOA,MAAM,CAACyD,MAAd,KAAyB,QAAzB,IAAqC/D,SAAS,CAACM,MAAM,CAACyD,MAAR,CAAlD,EAAmE;AACjE,UAAI;AAAEkT,QAAAA;AAAF,UAAS3W,MAAM,CAACyD,MAApB;;AACA,UAAI,CAACkT,EAAL,EAAS;AACPA,QAAAA,EAAE,GAAG5Y,MAAM,CAAC4E,MAAD,CAAX;AACA3C,QAAAA,MAAM,CAACyD,MAAP,CAAckT,EAAd,GAAmBA,EAAnB;AACD;;AAED3W,MAAAA,MAAM,CAACyD,MAAP,GAAiB,IAAGkT,EAAG,EAAvB;AACD;;AAED7W,IAAAA,eAAe,CAAC6C,MAAD,EAAO3C,MAAP,EAAe2O,aAAf,CAAf;AAEA,WAAO3O,MAAP;AACD;;AAED+sB,EAAAA,aAAa,GAAG;AACd,WAAO,KAAKV,cAAL,KAAwBptB,MAAxB,GACL,KAAKotB,cAAL,CAAoBkB,WADf,GAEL,KAAKlB,cAAL,CAAoB3e,SAFtB;AAGD;;AAEDsf,EAAAA,gBAAgB,GAAG;AACjB,WAAO,KAAKX,cAAL,CAAoB7K,YAApB,IAAoCvjB,IAAI,CAACmG,GAAL,CACzCvI,QAAQ,CAACkG,IAAT,CAAcyf,YAD2B,EAEzC3lB,QAAQ,CAACC,eAAT,CAAyB0lB,YAFgB,CAA3C;AAID;;AAEDgM,EAAAA,gBAAgB,GAAG;AACjB,WAAO,KAAKnB,cAAL,KAAwBptB,MAAxB,GACLA,MAAM,CAACwuB,WADF,GAEL,KAAKpB,cAAL,CAAoB7e,qBAApB,GAA4C4f,MAF9C;AAGD;;AAEDV,EAAAA,QAAQ,GAAG;AACT,UAAMhf,SAAS,GAAG,KAAKqf,aAAL,KAAuB,KAAKvb,OAAL,CAAalE,MAAtD;;AACA,UAAMkU,YAAY,GAAG,KAAKwL,gBAAL,EAArB;;AACA,UAAMU,SAAS,GAAG,KAAKlc,OAAL,CAAalE,MAAb,GAAsBkU,YAAtB,GAAqC,KAAKgM,gBAAL,EAAvD;;AAEA,QAAI,KAAKf,aAAL,KAAuBjL,YAA3B,EAAyC;AACvC,WAAKmL,OAAL;AACD;;AAED,QAAIjf,SAAS,IAAIggB,SAAjB,EAA4B;AAC1B,YAAMjqB,MAAM,GAAG,KAAK8oB,QAAL,CAAc,KAAKA,QAAL,CAAc1sB,MAAd,GAAuB,CAArC,CAAf;;AAEA,UAAI,KAAK2sB,aAAL,KAAuB/oB,MAA3B,EAAmC;AACjC,aAAKkqB,SAAL,CAAelqB,MAAf;AACD;;AAED;AACD;;AAED,QAAI,KAAK+oB,aAAL,IAAsB9e,SAAS,GAAG,KAAK4e,QAAL,CAAc,CAAd,CAAlC,IAAsD,KAAKA,QAAL,CAAc,CAAd,IAAmB,CAA7E,EAAgF;AAC9E,WAAKE,aAAL,GAAqB,IAArB;;AACA,WAAKoB,MAAL;;AACA;AACD;;AAED,SAAK,IAAI9nB,CAAC,GAAG,KAAKwmB,QAAL,CAAczsB,MAA3B,EAAmCiG,CAAC,EAApC,GAAyC;AACvC,YAAM+nB,cAAc,GAAG,KAAKrB,aAAL,KAAuB,KAAKD,QAAL,CAAczmB,CAAd,CAAvB,IACnB4H,SAAS,IAAI,KAAK4e,QAAL,CAAcxmB,CAAd,CADM,KAElB,OAAO,KAAKwmB,QAAL,CAAcxmB,CAAC,GAAG,CAAlB,CAAP,KAAgC,WAAhC,IAA+C4H,SAAS,GAAG,KAAK4e,QAAL,CAAcxmB,CAAC,GAAG,CAAlB,CAFzC,CAAvB;;AAIA,UAAI+nB,cAAJ,EAAoB;AAClB,aAAKF,SAAL,CAAe,KAAKpB,QAAL,CAAczmB,CAAd,CAAf;AACD;AACF;AACF;;AAED6nB,EAAAA,SAAS,CAAClqB,MAAD,EAAS;AAChB,SAAK+oB,aAAL,GAAqB/oB,MAArB;;AAEA,SAAKmqB,MAAL;;AAEA,UAAME,OAAO,GAAG,KAAK9W,SAAL,CAAetY,KAAf,CAAqB,GAArB,EACbud,GADa,CACTtgB,QAAQ,IAAK,GAAEA,QAAS,oBAAmB8H,MAAO,MAAK9H,QAAS,UAAS8H,MAAO,IADvE,CAAhB;;AAGA,UAAMsqB,IAAI,GAAGtyB,cAAc,CAACW,OAAf,CAAuB0xB,OAAO,CAACE,IAAR,CAAa,GAAb,CAAvB,CAAb;;AAEA,QAAID,IAAI,CAAC9sB,SAAL,CAAeC,QAAf,CAAwBwqB,wBAAxB,CAAJ,EAAuD;AACrDjwB,MAAAA,cAAc,CAACW,OAAf,CAAuB6vB,0BAAvB,EAAiD8B,IAAI,CAACniB,OAAL,CAAaogB,mBAAb,CAAjD,EACG/qB,SADH,CACa4S,GADb,CACiB3H,mBADjB;AAGA6hB,MAAAA,IAAI,CAAC9sB,SAAL,CAAe4S,GAAf,CAAmB3H,mBAAnB;AACD,KALD,MAKO;AACL;AACA6hB,MAAAA,IAAI,CAAC9sB,SAAL,CAAe4S,GAAf,CAAmB3H,mBAAnB;AAEAzQ,MAAAA,cAAc,CAACiB,OAAf,CAAuBqxB,IAAvB,EAA6BnC,yBAA7B,EACGxrB,OADH,CACW6tB,SAAS,IAAI;AACpB;AACA;AACAxyB,QAAAA,cAAc,CAACwB,IAAf,CAAoBgxB,SAApB,EAAgC,GAAEpC,kBAAmB,KAAIE,mBAAoB,EAA7E,EACG3rB,OADH,CACWitB,IAAI,IAAIA,IAAI,CAACpsB,SAAL,CAAe4S,GAAf,CAAmB3H,mBAAnB,CADnB,EAHoB;;AAOpBzQ,QAAAA,cAAc,CAACwB,IAAf,CAAoBgxB,SAApB,EAA+BnC,kBAA/B,EACG1rB,OADH,CACW8tB,OAAO,IAAI;AAClBzyB,UAAAA,cAAc,CAACa,QAAf,CAAwB4xB,OAAxB,EAAiCrC,kBAAjC,EACGzrB,OADH,CACWitB,IAAI,IAAIA,IAAI,CAACpsB,SAAL,CAAe4S,GAAf,CAAmB3H,mBAAnB,CADnB;AAED,SAJH;AAKD,OAbH;AAcD;;AAED1G,IAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKqkB,cAA1B,EAA0Cb,cAA1C,EAA0D;AACxDzkB,MAAAA,aAAa,EAAEtD;AADyC,KAA1D;AAGD;;AAEDmqB,EAAAA,MAAM,GAAG;AACPnyB,IAAAA,cAAc,CAACC,IAAf,CAAoB,KAAKsb,SAAzB,EACGza,MADH,CACU4xB,IAAI,IAAIA,IAAI,CAACltB,SAAL,CAAeC,QAAf,CAAwBgL,mBAAxB,CADlB,EAEG9L,OAFH,CAEW+tB,IAAI,IAAIA,IAAI,CAACltB,SAAL,CAAe2I,MAAf,CAAsBsC,mBAAtB,CAFnB;AAGD,GAxMmC;;;AA4Md,SAAfpJ,eAAe,CAAC9C,MAAD,EAAS;AAC7B,WAAO,KAAK8L,IAAL,CAAU,YAAY;AAC3B,YAAMC,IAAI,GAAGqgB,SAAS,CAACzhB,mBAAV,CAA8B,IAA9B,EAAoC3K,MAApC,CAAb;;AAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B;AACD;;AAED,UAAI,OAAO+L,IAAI,CAAC/L,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED+L,MAAAA,IAAI,CAAC/L,MAAD,CAAJ;AACD,KAZM,CAAP;AAaD;;AA1NmC;AA6NtC;AACA;AACA;AACA;AACA;;;AAEAwF,YAAY,CAACiC,EAAb,CAAgBxI,MAAhB,EAAwB2Q,mBAAxB,EAA6C,MAAM;AACjDnU,EAAAA,cAAc,CAACC,IAAf,CAAoBiwB,iBAApB,EACGvrB,OADH,CACWguB,GAAG,IAAI,IAAIhC,SAAJ,CAAcgC,GAAd,CADlB;AAED,CAHD;AAKA;AACA;AACA;AACA;AACA;AACA;;AAEA7rB,kBAAkB,CAAC6pB,SAAD,CAAlB;;ACjTA;AACA;AACA;AACA;AACA;AACA;AAYA;AACA;AACA;AACA;AACA;;AAEA,MAAMzpB,MAAI,GAAG,KAAb;AACA,MAAMwH,UAAQ,GAAG,QAAjB;AACA,MAAME,WAAS,GAAI,IAAGF,UAAS,EAA/B;AACA,MAAMU,YAAY,GAAG,WAArB;AAEA,MAAMmL,YAAU,GAAI,OAAM3L,WAAU,EAApC;AACA,MAAM4L,cAAY,GAAI,SAAQ5L,WAAU,EAAxC;AACA,MAAMyL,YAAU,GAAI,OAAMzL,WAAU,EAApC;AACA,MAAM0L,aAAW,GAAI,QAAO1L,WAAU,EAAtC;AACA,MAAMY,oBAAoB,GAAI,QAAOZ,WAAU,GAAEQ,YAAa,EAA9D;AAEA,MAAMwjB,wBAAwB,GAAG,eAAjC;AACA,MAAMniB,iBAAiB,GAAG,QAA1B;AACA,MAAMf,iBAAe,GAAG,MAAxB;AACA,MAAMC,iBAAe,GAAG,MAAxB;AAEA,MAAM4gB,iBAAiB,GAAG,WAA1B;AACA,MAAMJ,uBAAuB,GAAG,mBAAhC;AACA,MAAMxb,eAAe,GAAG,SAAxB;AACA,MAAMke,kBAAkB,GAAG,uBAA3B;AACA,MAAMniB,oBAAoB,GAAG,0EAA7B;AACA,MAAM8f,wBAAwB,GAAG,kBAAjC;AACA,MAAMsC,8BAA8B,GAAG,iCAAvC;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,GAAN,SAAkBzkB,aAAlB,CAAgC;AAC9B;AAEe,aAAJpH,IAAI,GAAG;AAChB,WAAOA,MAAP;AACD,GAL6B;;;AAS9B0U,EAAAA,IAAI,GAAG;AACL,QAAK,KAAKpN,QAAL,CAAcrN,UAAd,IACH,KAAKqN,QAAL,CAAcrN,UAAd,CAAyBC,QAAzB,KAAsCC,IAAI,CAACC,YADxC,IAEH,KAAKkN,QAAL,CAAchJ,SAAd,CAAwBC,QAAxB,CAAiCgL,iBAAjC,CAFF,EAEwD;AACtD;AACD;;AAED,QAAIhP,QAAJ;AACA,UAAMuG,MAAM,GAAG5E,sBAAsB,CAAC,KAAKoL,QAAN,CAArC;;AACA,UAAMwkB,WAAW,GAAG,KAAKxkB,QAAL,CAAc2B,OAAd,CAAsBggB,uBAAtB,CAApB;;AAEA,QAAI6C,WAAJ,EAAiB;AACf,YAAMC,YAAY,GAAGD,WAAW,CAACzL,QAAZ,KAAyB,IAAzB,IAAiCyL,WAAW,CAACzL,QAAZ,KAAyB,IAA1D,GAAiEsL,kBAAjE,GAAsFle,eAA3G;AACAlT,MAAAA,QAAQ,GAAGzB,cAAc,CAACC,IAAf,CAAoBgzB,YAApB,EAAkCD,WAAlC,CAAX;AACAvxB,MAAAA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,CAAC2C,MAAT,GAAkB,CAAnB,CAAnB;AACD;;AAED,UAAM+b,SAAS,GAAG1e,QAAQ,GACxBsI,YAAY,CAACwC,OAAb,CAAqB9K,QAArB,EAA+B8Y,YAA/B,EAA2C;AACzCjP,MAAAA,aAAa,EAAE,KAAKkD;AADqB,KAA3C,CADwB,GAIxB,IAJF;AAMA,UAAM8Q,SAAS,GAAGvV,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC6L,YAApC,EAAgD;AAChE/O,MAAAA,aAAa,EAAE7J;AADiD,KAAhD,CAAlB;;AAIA,QAAI6d,SAAS,CAAC1S,gBAAV,IAA+BuT,SAAS,KAAK,IAAd,IAAsBA,SAAS,CAACvT,gBAAnE,EAAsF;AACpF;AACD;;AAED,SAAKslB,SAAL,CAAe,KAAK1jB,QAApB,EAA8BwkB,WAA9B;;AAEA,UAAMxW,QAAQ,GAAG,MAAM;AACrBzS,MAAAA,YAAY,CAACwC,OAAb,CAAqB9K,QAArB,EAA+B+Y,cAA/B,EAA6C;AAC3ClP,QAAAA,aAAa,EAAE,KAAKkD;AADuB,OAA7C;AAGAzE,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC8L,aAApC,EAAiD;AAC/ChP,QAAAA,aAAa,EAAE7J;AADgC,OAAjD;AAGD,KAPD;;AASA,QAAIuG,MAAJ,EAAY;AACV,WAAKkqB,SAAL,CAAelqB,MAAf,EAAuBA,MAAM,CAAC7G,UAA9B,EAA0Cqb,QAA1C;AACD,KAFD,MAEO;AACLA,MAAAA,QAAQ;AACT;AACF,GAxD6B;;;AA4D9B0V,EAAAA,SAAS,CAAC/xB,OAAD,EAAU4b,SAAV,EAAqBtV,QAArB,EAA+B;AACtC,UAAMysB,cAAc,GAAGnX,SAAS,KAAKA,SAAS,CAACwL,QAAV,KAAuB,IAAvB,IAA+BxL,SAAS,CAACwL,QAAV,KAAuB,IAA3D,CAAT,GACrBvnB,cAAc,CAACC,IAAf,CAAoB4yB,kBAApB,EAAwC9W,SAAxC,CADqB,GAErB/b,cAAc,CAACa,QAAf,CAAwBkb,SAAxB,EAAmCpH,eAAnC,CAFF;AAIA,UAAMwe,MAAM,GAAGD,cAAc,CAAC,CAAD,CAA7B;AACA,UAAMtW,eAAe,GAAGnW,QAAQ,IAAK0sB,MAAM,IAAIA,MAAM,CAAC3tB,SAAP,CAAiBC,QAAjB,CAA0BiK,iBAA1B,CAA/C;;AAEA,UAAM8M,QAAQ,GAAG,MAAM,KAAK4W,mBAAL,CAAyBjzB,OAAzB,EAAkCgzB,MAAlC,EAA0C1sB,QAA1C,CAAvB;;AAEA,QAAI0sB,MAAM,IAAIvW,eAAd,EAA+B;AAC7BuW,MAAAA,MAAM,CAAC3tB,SAAP,CAAiB2I,MAAjB,CAAwBwB,iBAAxB;;AACA,WAAKZ,cAAL,CAAoByN,QAApB,EAA8Brc,OAA9B,EAAuC,IAAvC;AACD,KAHD,MAGO;AACLqc,MAAAA,QAAQ;AACT;AACF;;AAED4W,EAAAA,mBAAmB,CAACjzB,OAAD,EAAUgzB,MAAV,EAAkB1sB,QAAlB,EAA4B;AAC7C,QAAI0sB,MAAJ,EAAY;AACVA,MAAAA,MAAM,CAAC3tB,SAAP,CAAiB2I,MAAjB,CAAwBsC,iBAAxB;AAEA,YAAM4iB,aAAa,GAAGrzB,cAAc,CAACW,OAAf,CAAuBmyB,8BAAvB,EAAuDK,MAAM,CAAChyB,UAA9D,CAAtB;;AAEA,UAAIkyB,aAAJ,EAAmB;AACjBA,QAAAA,aAAa,CAAC7tB,SAAd,CAAwB2I,MAAxB,CAA+BsC,iBAA/B;AACD;;AAED,UAAI0iB,MAAM,CAACtwB,YAAP,CAAoB,MAApB,MAAgC,KAApC,EAA2C;AACzCswB,QAAAA,MAAM,CAACtiB,YAAP,CAAoB,eAApB,EAAqC,KAArC;AACD;AACF;;AAED1Q,IAAAA,OAAO,CAACqF,SAAR,CAAkB4S,GAAlB,CAAsB3H,iBAAtB;;AACA,QAAItQ,OAAO,CAAC0C,YAAR,CAAqB,MAArB,MAAiC,KAArC,EAA4C;AAC1C1C,MAAAA,OAAO,CAAC0Q,YAAR,CAAqB,eAArB,EAAsC,IAAtC;AACD;;AAED3K,IAAAA,MAAM,CAAC/F,OAAD,CAAN;;AAEA,QAAIA,OAAO,CAACqF,SAAR,CAAkBC,QAAlB,CAA2BiK,iBAA3B,CAAJ,EAAiD;AAC/CvP,MAAAA,OAAO,CAACqF,SAAR,CAAkB4S,GAAlB,CAAsBzI,iBAAtB;AACD;;AAED,QAAIyK,MAAM,GAAGja,OAAO,CAACgB,UAArB;;AACA,QAAIiZ,MAAM,IAAIA,MAAM,CAACmN,QAAP,KAAoB,IAAlC,EAAwC;AACtCnN,MAAAA,MAAM,GAAGA,MAAM,CAACjZ,UAAhB;AACD;;AAED,QAAIiZ,MAAM,IAAIA,MAAM,CAAC5U,SAAP,CAAiBC,QAAjB,CAA0BmtB,wBAA1B,CAAd,EAAmE;AACjE,YAAMU,eAAe,GAAGnzB,OAAO,CAACgQ,OAAR,CAAgBogB,iBAAhB,CAAxB;;AAEA,UAAI+C,eAAJ,EAAqB;AACnBtzB,QAAAA,cAAc,CAACC,IAAf,CAAoBuwB,wBAApB,EAA8C8C,eAA9C,EACG3uB,OADH,CACW4uB,QAAQ,IAAIA,QAAQ,CAAC/tB,SAAT,CAAmB4S,GAAnB,CAAuB3H,iBAAvB,CADvB;AAED;;AAEDtQ,MAAAA,OAAO,CAAC0Q,YAAR,CAAqB,eAArB,EAAsC,IAAtC;AACD;;AAED,QAAIpK,QAAJ,EAAc;AACZA,MAAAA,QAAQ;AACT;AACF,GA3H6B;;;AA+HR,SAAfY,eAAe,CAAC9C,MAAD,EAAS;AAC7B,WAAO,KAAK8L,IAAL,CAAU,YAAY;AAC3B,YAAMC,IAAI,GAAGyiB,GAAG,CAAC7jB,mBAAJ,CAAwB,IAAxB,CAAb;;AAEA,UAAI,OAAO3K,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAO+L,IAAI,CAAC/L,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED+L,QAAAA,IAAI,CAAC/L,MAAD,CAAJ;AACD;AACF,KAVM,CAAP;AAWD;;AA3I6B;AA8IhC;AACA;AACA;AACA;AACA;;;AAEAwF,YAAY,CAACiC,EAAb,CAAgB5L,QAAhB,EAA0BoP,oBAA1B,EAAgDkB,oBAAhD,EAAsE,UAAU9G,KAAV,EAAiB;AACrF,MAAI,CAAC,GAAD,EAAM,MAAN,EAAc7G,QAAd,CAAuB,KAAKsV,OAA5B,CAAJ,EAA0C;AACxCzO,IAAAA,KAAK,CAAC4D,cAAN;AACD;;AAED,MAAIjI,UAAU,CAAC,IAAD,CAAd,EAAsB;AACpB;AACD;;AAED,QAAM+K,IAAI,GAAGyiB,GAAG,CAAC7jB,mBAAJ,CAAwB,IAAxB,CAAb;AACAoB,EAAAA,IAAI,CAACsL,IAAL;AACD,CAXD;AAaA;AACA;AACA;AACA;AACA;AACA;;AAEA9U,kBAAkB,CAACisB,GAAD,CAAlB;;AC7NA;AACA;AACA;AACA;AACA;AACA;AAWA;AACA;AACA;AACA;AACA;;AAEA,MAAM7rB,IAAI,GAAG,OAAb;AACA,MAAMwH,QAAQ,GAAG,UAAjB;AACA,MAAME,SAAS,GAAI,IAAGF,QAAS,EAA/B;AAEA,MAAMsV,mBAAmB,GAAI,gBAAepV,SAAU,EAAtD;AACA,MAAM4kB,eAAe,GAAI,YAAW5kB,SAAU,EAA9C;AACA,MAAM6kB,cAAc,GAAI,WAAU7kB,SAAU,EAA5C;AACA,MAAMkV,aAAa,GAAI,UAASlV,SAAU,EAA1C;AACA,MAAM8kB,cAAc,GAAI,WAAU9kB,SAAU,EAA5C;AACA,MAAM2L,UAAU,GAAI,OAAM3L,SAAU,EAApC;AACA,MAAM4L,YAAY,GAAI,SAAQ5L,SAAU,EAAxC;AACA,MAAMyL,UAAU,GAAI,OAAMzL,SAAU,EAApC;AACA,MAAM0L,WAAW,GAAI,QAAO1L,SAAU,EAAtC;AAEA,MAAMc,eAAe,GAAG,MAAxB;AACA,MAAMikB,eAAe,GAAG,MAAxB;AACA,MAAMhkB,eAAe,GAAG,MAAxB;AACA,MAAMikB,kBAAkB,GAAG,SAA3B;AAEA,MAAM1gB,WAAW,GAAG;AAClBuX,EAAAA,SAAS,EAAE,SADO;AAElBoJ,EAAAA,QAAQ,EAAE,SAFQ;AAGlBjJ,EAAAA,KAAK,EAAE;AAHW,CAApB;AAMA,MAAMjY,OAAO,GAAG;AACd8X,EAAAA,SAAS,EAAE,IADG;AAEdoJ,EAAAA,QAAQ,EAAE,IAFI;AAGdjJ,EAAAA,KAAK,EAAE;AAHO,CAAhB;AAMA,MAAMpG,qBAAqB,GAAG,2BAA9B;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMsP,KAAN,SAAoBxlB,aAApB,CAAkC;AAChCC,EAAAA,WAAW,CAACpO,OAAD,EAAUoE,MAAV,EAAkB;AAC3B,UAAMpE,OAAN;AAEA,SAAK4V,OAAL,GAAe,KAAKC,UAAL,CAAgBzR,MAAhB,CAAf;AACA,SAAKooB,QAAL,GAAgB,IAAhB;AACA,SAAKoH,oBAAL,GAA4B,KAA5B;AACA,SAAKC,uBAAL,GAA+B,KAA/B;;AACA,SAAKjH,aAAL;AACD,GAT+B;;;AAaV,aAAX7Z,WAAW,GAAG;AACvB,WAAOA,WAAP;AACD;;AAEiB,aAAPP,OAAO,GAAG;AACnB,WAAOA,OAAP;AACD;;AAEc,aAAJzL,IAAI,GAAG;AAChB,WAAOA,IAAP;AACD,GAvB+B;;;AA2BhC0U,EAAAA,IAAI,GAAG;AACL,UAAM0D,SAAS,GAAGvV,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC6L,UAApC,CAAlB;;AAEA,QAAIiF,SAAS,CAAC1S,gBAAd,EAAgC;AAC9B;AACD;;AAED,SAAKqnB,aAAL;;AAEA,QAAI,KAAKle,OAAL,CAAa0U,SAAjB,EAA4B;AAC1B,WAAKjc,QAAL,CAAchJ,SAAd,CAAwB4S,GAAxB,CAA4B1I,eAA5B;AACD;;AAED,UAAM8M,QAAQ,GAAG,MAAM;AACrB,WAAKhO,QAAL,CAAchJ,SAAd,CAAwB2I,MAAxB,CAA+BylB,kBAA/B;;AACA,WAAKplB,QAAL,CAAchJ,SAAd,CAAwB4S,GAAxB,CAA4BzI,eAA5B;;AAEA5F,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC8L,WAApC;;AAEA,WAAK4Z,kBAAL;AACD,KAPD;;AASA,SAAK1lB,QAAL,CAAchJ,SAAd,CAAwB2I,MAAxB,CAA+BwlB,eAA/B;;AACAztB,IAAAA,MAAM,CAAC,KAAKsI,QAAN,CAAN;;AACA,SAAKA,QAAL,CAAchJ,SAAd,CAAwB4S,GAAxB,CAA4Bwb,kBAA5B;;AAEA,SAAK7kB,cAAL,CAAoByN,QAApB,EAA8B,KAAKhO,QAAnC,EAA6C,KAAKuH,OAAL,CAAa0U,SAA1D;AACD;;AAED9O,EAAAA,IAAI,GAAG;AACL,QAAI,CAAC,KAAKnN,QAAL,CAAchJ,SAAd,CAAwBC,QAAxB,CAAiCkK,eAAjC,CAAL,EAAwD;AACtD;AACD;;AAED,UAAMwQ,SAAS,GAAGpW,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoC+L,UAApC,CAAlB;;AAEA,QAAI4F,SAAS,CAACvT,gBAAd,EAAgC;AAC9B;AACD;;AAED,UAAM4P,QAAQ,GAAG,MAAM;AACrB,WAAKhO,QAAL,CAAchJ,SAAd,CAAwB4S,GAAxB,CAA4Bub,eAA5B;;AACA5pB,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKiC,QAA1B,EAAoCgM,YAApC;AACD,KAHD;;AAKA,SAAKhM,QAAL,CAAchJ,SAAd,CAAwB2I,MAAxB,CAA+BwB,eAA/B;;AACA,SAAKZ,cAAL,CAAoByN,QAApB,EAA8B,KAAKhO,QAAnC,EAA6C,KAAKuH,OAAL,CAAa0U,SAA1D;AACD;;AAED9b,EAAAA,OAAO,GAAG;AACR,SAAKslB,aAAL;;AAEA,QAAI,KAAKzlB,QAAL,CAAchJ,SAAd,CAAwBC,QAAxB,CAAiCkK,eAAjC,CAAJ,EAAuD;AACrD,WAAKnB,QAAL,CAAchJ,SAAd,CAAwB2I,MAAxB,CAA+BwB,eAA/B;AACD;;AAED,UAAMhB,OAAN;AACD,GApF+B;;;AAwFhCqH,EAAAA,UAAU,CAACzR,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,EACP,GAAGoO,OADI;AAEP,SAAGxB,WAAW,CAACI,iBAAZ,CAA8B,KAAK/C,QAAnC,CAFI;AAGP,UAAI,OAAOjK,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;AAHO,KAAT;AAMAF,IAAAA,eAAe,CAAC6C,IAAD,EAAO3C,MAAP,EAAe,KAAKgK,WAAL,CAAiB2E,WAAhC,CAAf;AAEA,WAAO3O,MAAP;AACD;;AAED2vB,EAAAA,kBAAkB,GAAG;AACnB,QAAI,CAAC,KAAKne,OAAL,CAAa8d,QAAlB,EAA4B;AAC1B;AACD;;AAED,QAAI,KAAKE,oBAAL,IAA6B,KAAKC,uBAAtC,EAA+D;AAC7D;AACD;;AAED,SAAKrH,QAAL,GAAgBzkB,UAAU,CAAC,MAAM;AAC/B,WAAKyT,IAAL;AACD,KAFyB,EAEvB,KAAK5F,OAAL,CAAa6U,KAFU,CAA1B;AAGD;;AAEDuJ,EAAAA,cAAc,CAACvqB,KAAD,EAAQwqB,aAAR,EAAuB;AACnC,YAAQxqB,KAAK,CAACK,IAAd;AACE,WAAK,WAAL;AACA,WAAK,UAAL;AACE,aAAK8pB,oBAAL,GAA4BK,aAA5B;AACA;;AACF,WAAK,SAAL;AACA,WAAK,UAAL;AACE,aAAKJ,uBAAL,GAA+BI,aAA/B;AACA;AARJ;;AAaA,QAAIA,aAAJ,EAAmB;AACjB,WAAKH,aAAL;;AACA;AACD;;AAED,UAAM5a,WAAW,GAAGzP,KAAK,CAAC0B,aAA1B;;AACA,QAAI,KAAKkD,QAAL,KAAkB6K,WAAlB,IAAiC,KAAK7K,QAAL,CAAc/I,QAAd,CAAuB4T,WAAvB,CAArC,EAA0E;AACxE;AACD;;AAED,SAAK6a,kBAAL;AACD;;AAEDnH,EAAAA,aAAa,GAAG;AACdhjB,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BwV,mBAA/B,EAAoDQ,qBAApD,EAA2E,MAAM,KAAK7I,IAAL,EAAjF;AACA5R,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BglB,eAA/B,EAAgD5pB,KAAK,IAAI,KAAKuqB,cAAL,CAAoBvqB,KAApB,EAA2B,IAA3B,CAAzD;AACAG,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BilB,cAA/B,EAA+C7pB,KAAK,IAAI,KAAKuqB,cAAL,CAAoBvqB,KAApB,EAA2B,KAA3B,CAAxD;AACAG,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BsV,aAA/B,EAA8Cla,KAAK,IAAI,KAAKuqB,cAAL,CAAoBvqB,KAApB,EAA2B,IAA3B,CAAvD;AACAG,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKwC,QAArB,EAA+BklB,cAA/B,EAA+C9pB,KAAK,IAAI,KAAKuqB,cAAL,CAAoBvqB,KAApB,EAA2B,KAA3B,CAAxD;AACD;;AAEDqqB,EAAAA,aAAa,GAAG;AACdhc,IAAAA,YAAY,CAAC,KAAK0U,QAAN,CAAZ;AACA,SAAKA,QAAL,GAAgB,IAAhB;AACD,GAxJ+B;;;AA4JV,SAAftlB,eAAe,CAAC9C,MAAD,EAAS;AAC7B,WAAO,KAAK8L,IAAL,CAAU,YAAY;AAC3B,YAAMC,IAAI,GAAGwjB,KAAK,CAAC5kB,mBAAN,CAA0B,IAA1B,EAAgC3K,MAAhC,CAAb;;AAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAO+L,IAAI,CAAC/L,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;AACD;;AAED+L,QAAAA,IAAI,CAAC/L,MAAD,CAAJ,CAAa,IAAb;AACD;AACF,KAVM,CAAP;AAWD;;AAxK+B;AA2KlC;AACA;AACA;AACA;AACA;AACA;;;AAEAuC,kBAAkB,CAACgtB,KAAD,CAAlB;;;;"}