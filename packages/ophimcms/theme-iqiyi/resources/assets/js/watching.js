$(document).ready(function () {
    $('.play-time .more-infor').on('click', function () {
        var show = $(this).data('show');
        $(this).removeClass('show');
        $('.infor-watching .show-more-info').toggle(800);
        if (show == 1) {
            $('.play-time .more-infor_2').addClass('show');
        } else {
            $('.play-time .more-infor_1').addClass('show');
        }
    });

    const splideByCate = new Splide(".firm-propose .splide", {
        // Optional parameters
        start: 0,
        perPage: 5,
        perMove: 1,
        gap: 14,
        type: "loop",
        drag: "free",
        snap: true,
        arrows: true,
        lazyLoad: true,
        pagination: false,

        // Responsive breakpoint
        breakpoints: {
            1679: {
                perPage: 6,

            },
            1480: {
                perPage: 5,

            },
            1200: {
                perPage: 4,

            },
            768: {
                perPage: 3,
            }
        }
    });

    splideByCate.mount();

    $('.list-top-firm .firm-item-link').hover(function () {
        $('.list-top-firm .firm-item-link.active').removeClass('active');
        $(this).addClass('active');
    })

    function swapEpisode() {
        let windowWidth = $(window).width();
        if (windowWidth <= 1024) {
            let episode = $('.watcher .episodes').html();
            $('.episodes-response').html(episode);
        } else {
            let episode = $('.episodes-response').html();
            if (episode.trim().length !== 0) {
                $('.watcher .episodes').html(episode);
            }
        }
    }

    swapEpisode();
    $(window).on("resize", function () {
        swapEpisode();
    })

    var hiddenElement = $(".BtnLight.AAIco-lightbulb_outline");
    function hideElementF() {
        hiddenElement.hide();
    }

    function showElementF() {
        hiddenElement.show();
    }

    $(document).ready(function () {
        setInterval(hideElementF, 5000); // Hide element every 5 seconds

        $(document).on('click', function () {
            showElementF();
            clearTimeout(autoHideTimeout);
            autoHideTimeout = setTimeout(hideElementF, 5000);
        });

        var autoHideTimeout = setTimeout(hideElementF, 5000);
    });
})