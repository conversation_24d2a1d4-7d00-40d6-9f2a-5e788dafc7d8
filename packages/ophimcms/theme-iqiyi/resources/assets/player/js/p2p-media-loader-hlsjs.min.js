require=function t(e,r,n){function i(a,o){if(!r[a]){if(!e[a]){var u="function"==typeof require&&require;if(!o&&u)return u(a,!0);if(s)return s(a,!0);var f=new Error("Cannot find module '"+a+"'");throw f.code="MODULE_NOT_FOUND",f}var l=r[a]={exports:{}};e[a][0].call(l.exports,(function(t){return i(e[a][1][t]||t)}),l,l.exports,t,e,r,n)}return r[a].exports}for(var s="function"==typeof require&&require,a=0;a<n.length;a++)i(n[a]);return i}({1:[function(t,e,r){window.p2pml||(window.p2pml={}),window.p2pml.hlsjs=t("p2p-media-loader-hlsjs")},{"p2p-media-loader-hlsjs":"p2p-media-loader-hlsjs"}],2:[function(t,e,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.Engine=void 0;const n=t("events"),i=t("p2p-media-loader-core"),s=t("./segment-manager"),a=t("./hlsjs-loader"),o=t("./hlsjs-loader-class");class u extends n.EventEmitter{constructor(t={}){super(),this.loader=new i.HybridLoader(t.loader),this.segmentManager=new s.SegmentManager(this.loader,t.segments),Object.keys(i.Events).map((t=>i.Events[t])).forEach((t=>this.loader.on(t,((...e)=>this.emit(t,...e)))))}static isSupported(){return i.HybridLoader.isSupported()}createLoaderClass(){return o.createHlsJsLoaderClass(a.HlsJsLoader,this)}async destroy(){await this.segmentManager.destroy()}getSettings(){return{segments:this.segmentManager.getSettings(),loader:this.loader.getSettings()}}getDetails(){return{loader:this.loader.getDetails()}}setPlayingSegment(t,e,r,n){this.segmentManager.setPlayingSegment(t,e,r,n)}setPlayingSegmentByCurrentTime(t){this.segmentManager.setPlayingSegmentByCurrentTime(t)}}r.Engine=u},{"./hlsjs-loader":4,"./hlsjs-loader-class":3,"./segment-manager":5,events:"events","p2p-media-loader-core":"p2p-media-loader-core"}],3:[function(t,e,r){e.exports.createHlsJsLoaderClass=function(t,e){function r(){this.impl=new t(e.segmentManager),this.stats=this.impl.stats}return r.prototype.load=function(t,e,r){this.context=t,this.impl.load(t,e,r)},r.prototype.abort=function(){this.impl.abort(this.context)},r.prototype.destroy=function(){this.context&&this.impl.abort(this.context)},r.getEngine=function(){return e},r}},{}],4:[function(t,e,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.HlsJsLoader=void 0;r.HlsJsLoader=class{constructor(t){this.stats={},this.segmentManager=t}async load(t,e,r){if(t.type)try{const e=await this.segmentManager.loadPlaylist(t.url);this.successPlaylist(e,t,r)}catch(e){this.error(e,t,r)}else if(t.frag)try{const e=await this.segmentManager.loadSegment(t.url,null==t.rangeStart||null==t.rangeEnd?void 0:{offset:t.rangeStart,length:t.rangeEnd-t.rangeStart});void 0!==e.content&&setTimeout((()=>this.successSegment(e.content,e.downloadBandwidth,t,r)),0)}catch(e){setTimeout((()=>this.error(e,t,r)),0)}else console.warn("Unknown load request",t)}abort(t){this.segmentManager.abortSegment(t.url,null==t.rangeStart||null==t.rangeEnd?void 0:{offset:t.rangeStart,length:t.rangeEnd-t.rangeStart})}successPlaylist(t,e,r){const n=performance.now();this.stats.trequest=n-300,this.stats.tfirst=n-200,this.stats.tload=n,this.stats.loaded=t.response.length,r.onSuccess({url:t.responseURL,data:t.response},this.stats,e)}successSegment(t,e,r,n){const i=performance.now(),s=t.byteLength/(void 0===e||e<=0?12500:e);this.stats.trequest=i-1-s,this.stats.tfirst=i-s,this.stats.tload=i,this.stats.loaded=t.byteLength,n.onSuccess({url:r.url,data:t},this.stats,r)}error(t,e,r){r.onError(t,e)}}},{}],5:[function(t,e,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.SegmentManager=void 0;const n=t("p2p-media-loader-core"),i=t("m3u8-parser"),s={forwardSegmentCount:20,swarmId:void 0,assetsStorage:void 0};r.SegmentManager=class{constructor(t,e={}){this.masterPlaylist=null,this.variantPlaylists=new Map,this.segmentRequest=null,this.playQueue=[],this.onSegmentLoaded=t=>{this.segmentRequest&&this.segmentRequest.segmentUrl===t.url&&f(this.segmentRequest.segmentByterange)===t.range&&(this.segmentRequest.onSuccess(t.data.slice(0),t.downloadBandwidth),this.segmentRequest=null)},this.onSegmentError=(t,e)=>{this.segmentRequest&&this.segmentRequest.segmentUrl===t.url&&f(this.segmentRequest.segmentByterange)===t.range&&(this.segmentRequest.onError(e),this.segmentRequest=null)},this.onSegmentAbort=t=>{this.segmentRequest&&this.segmentRequest.segmentUrl===t.url&&f(this.segmentRequest.segmentByterange)===t.range&&(this.segmentRequest.onError("Loading aborted: internal abort"),this.segmentRequest=null)},this.settings=Object.assign(Object.assign({},s),e),this.loader=t,this.loader.on(n.Events.SegmentLoaded,this.onSegmentLoaded),this.loader.on(n.Events.SegmentError,this.onSegmentError),this.loader.on(n.Events.SegmentAbort,this.onSegmentAbort)}getSettings(){return this.settings}processPlaylist(t,e,r){const n=new i.Parser;n.push(e),n.end();const s=new a(t,r,n.manifest);if(s.manifest.playlists){this.masterPlaylist=s;for(const[t,e]of this.variantPlaylists){const{streamSwarmId:r,found:n,index:i}=this.getStreamSwarmId(e.requestUrl);n?(e.streamSwarmId=r,e.streamId="V"+i.toString()):this.variantPlaylists.delete(t)}}else{const{streamSwarmId:e,found:r,index:n}=this.getStreamSwarmId(t);(r||null===this.masterPlaylist)&&(s.streamSwarmId=e,s.streamId=null===this.masterPlaylist?void 0:"V"+n.toString(),this.variantPlaylists.set(t,s),this.updateSegments())}}async loadPlaylist(t){const e=this.settings.assetsStorage;let r;if(void 0!==e){let n;n=this.getMasterSwarmId(),void 0===n&&(n=t.split("?")[0]);const i=await e.getAsset(t,void 0,n);void 0!==i?r={responseURL:i.responseUri,response:i.data}:(r=await this.loadContent(t,"text"),e.storeAsset({masterManifestUri:null!==this.masterPlaylist?this.masterPlaylist.requestUrl:t,masterSwarmId:n,requestUri:t,responseUri:r.responseURL,data:r.response}))}else r=await this.loadContent(t,"text");return this.processPlaylist(t,r.response,r.responseURL),r}async loadSegment(t,e){const r=this.getSegmentLocation(t,e),n=f(e);if(!r){let e;const r=this.settings.assetsStorage;if(void 0!==r){let i,s=null!==this.masterPlaylist?this.masterPlaylist.requestUrl:void 0;if(i=this.getMasterSwarmId(),void 0===i&&1===this.variantPlaylists.size&&(i=this.variantPlaylists.values().next().value.requestUrl.split("?")[0]),void 0===s&&1===this.variantPlaylists.size&&(s=this.variantPlaylists.values().next().value.requestUrl),void 0!==i&&void 0!==s){const a=await r.getAsset(t,n,i);if(void 0!==a)e=a.data;else{const a=await this.loadContent(t,"arraybuffer",n);e=a.response,r.storeAsset({masterManifestUri:s,masterSwarmId:i,requestUri:t,requestRange:n,responseUri:a.responseURL,data:e})}}}if(void 0===e){e=(await this.loadContent(t,"arraybuffer",n)).response}return{content:e,downloadBandwidth:0}}const i=(r.playlist.manifest.mediaSequence?r.playlist.manifest.mediaSequence:0)+r.segmentIndex;if(this.playQueue.length>0){this.playQueue[this.playQueue.length-1].segmentSequence!==i-1&&(this.playQueue=[])}this.segmentRequest&&this.segmentRequest.onError("Cancel segment request: simultaneous segment requests are not supported");const s=new Promise(((n,s)=>{this.segmentRequest=new o(t,e,i,r.playlist.requestUrl,((t,e)=>n({content:t,downloadBandwidth:e})),(t=>s(t)))}));return this.playQueue.push({segmentUrl:t,segmentByterange:e,segmentSequence:i}),this.loadSegments(r.playlist,r.segmentIndex,!0),s}setPlayingSegment(t,e,r,n){const i=this.playQueue.findIndex((r=>r.segmentUrl==t&&u(r.segmentByterange,e)));i>=0&&(this.playQueue=this.playQueue.slice(i),this.playQueue[0].playPosition={start:r,duration:n},this.updateSegments())}setPlayingSegmentByCurrentTime(t){if(0===this.playQueue.length||!this.playQueue[0].playPosition)return;const e=this.playQueue[0].playPosition;e.start+e.duration-t<.2&&(this.playQueue=this.playQueue.slice(1),this.updateSegments())}abortSegment(t,e){this.segmentRequest&&this.segmentRequest.segmentUrl===t&&u(this.segmentRequest.segmentByterange,e)&&(this.segmentRequest.onSuccess(void 0,0),this.segmentRequest=null)}async destroy(){this.segmentRequest&&(this.segmentRequest.onError("Loading aborted: object destroyed"),this.segmentRequest=null),this.masterPlaylist=null,this.variantPlaylists.clear(),this.playQueue=[],void 0!==this.settings.assetsStorage&&await this.settings.assetsStorage.destroy(),await this.loader.destroy()}updateSegments(){if(!this.segmentRequest)return;const t=this.getSegmentLocation(this.segmentRequest.segmentUrl,this.segmentRequest.segmentByterange);t&&this.loadSegments(t.playlist,t.segmentIndex,!1)}getSegmentLocation(t,e){for(const r of this.variantPlaylists.values()){const n=r.getSegmentIndex(t,e);if(n>=0)return{playlist:r,segmentIndex:n}}}async loadSegments(t,e,r){const n=[],i=t.manifest.segments,s=t.manifest.mediaSequence?t.manifest.mediaSequence:0;let a=null,o=Math.max(0,this.playQueue.length-1);const u=this.getMasterSwarmId();for(let l=e;l<i.length&&n.length<this.settings.forwardSegmentCount;++l){const e=t.manifest.segments[l],i=t.getSegmentAbsoluteUrl(e.uri),h=e.byterange,g=this.getSegmentId(t,s+l);n.push({id:g,url:i,masterSwarmId:void 0!==u?u:t.streamSwarmId,masterManifestUri:null!==this.masterPlaylist?this.masterPlaylist.requestUrl:t.requestUrl,streamId:t.streamId,sequence:(s+l).toString(),range:f(h),priority:o++}),r&&!a&&(a=g)}if(this.loader.load(n,t.streamSwarmId),a){const t=await this.loader.getSegment(a);t&&this.onSegmentLoaded(t)}}getSegmentId(t,e){return`${t.streamSwarmId}+${e}`}getMasterSwarmId(){const t=this.settings.swarmId&&0!==this.settings.swarmId.length?this.settings.swarmId:void 0;return void 0!==t?t:null!==this.masterPlaylist?this.masterPlaylist.requestUrl.split("?")[0]:void 0}getStreamSwarmId(t){const e=this.getMasterSwarmId();if(null!==this.masterPlaylist)for(let r=0;r<this.masterPlaylist.manifest.playlists.length;++r){if(new URL(this.masterPlaylist.manifest.playlists[r].uri,this.masterPlaylist.responseUrl).toString()===t)return{streamSwarmId:`${e}+V${r}`,found:!0,index:r}}return{streamSwarmId:void 0!==e?e:t.split("?")[0],found:!1,index:-1}}async loadContent(t,e,r){return new Promise(((n,i)=>{const s=new XMLHttpRequest;s.open("GET",t,!0),s.responseType=e,r&&s.setRequestHeader("Range",r),s.addEventListener("readystatechange",(()=>{4===s.readyState&&(s.status>=200&&s.status<300?n(s):i(s.statusText))}));const a=this.loader.getSettings().xhrSetup;a&&a(s,t),s.send()}))}};class a{constructor(t,e,r){this.requestUrl=t,this.responseUrl=e,this.manifest=r,this.streamSwarmId=""}getSegmentIndex(t,e){for(let r=0;r<this.manifest.segments.length;++r){const n=this.manifest.segments[r];if(t===this.getSegmentAbsoluteUrl(n.uri)&&u(n.byterange,e))return r}return-1}getSegmentAbsoluteUrl(t){return new URL(t,this.responseUrl).toString()}}class o{constructor(t,e,r,n,i,s){this.segmentUrl=t,this.segmentByterange=e,this.segmentSequence=r,this.playlistRequestUrl=n,this.onSuccess=i,this.onError=s}}function u(t,e){return void 0===t?void 0===e:void 0!==e&&t.length===e.length&&t.offset===e.offset}function f(t){if(void 0===t)return;const e=t.offset+t.length-1;return`bytes=${t.offset}-${e}`}},{"m3u8-parser":16,"p2p-media-loader-core":"p2p-media-loader-core"}],6:[function(t,e,r){e.exports=function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}},{}],7:[function(t,e,r){function n(){return e.exports=n=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},n.apply(this,arguments)}e.exports=n},{}],8:[function(t,e,r){e.exports=function(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}},{}],9:[function(t,e,r){e.exports=function(t){return t&&t.__esModule?t:{default:t}}},{}],10:[function(t,e,r){(function(n){(function(){"use strict";var i=t("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(t){for(var e=(a=t,s.default.atob?s.default.atob(a):n.from(a,"base64").toString("binary")),r=new Uint8Array(e.length),i=0;i<e.length;i++)r[i]=e.charCodeAt(i);var a;return r};var s=i(t("global/window"));e.exports=r.default}).call(this)}).call(this,t("buffer").Buffer)},{"@babel/runtime/helpers/interopRequireDefault":9,buffer:13,"global/window":14}],11:[function(t,e,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(){function t(){this.listeners={}}var e=t.prototype;return e.on=function(t,e){this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push(e)},e.off=function(t,e){if(!this.listeners[t])return!1;var r=this.listeners[t].indexOf(e);return this.listeners[t]=this.listeners[t].slice(0),this.listeners[t].splice(r,1),r>-1},e.trigger=function(t){var e=this.listeners[t];if(e)if(2===arguments.length)for(var r=e.length,n=0;n<r;++n)e[n].call(this,arguments[1]);else for(var i=Array.prototype.slice.call(arguments,1),s=e.length,a=0;a<s;++a)e[a].apply(this,i)},e.dispose=function(){this.listeners={}},e.pipe=function(t){this.on("data",(function(e){t.push(e)}))},t}();r.default=n,e.exports=r.default},{}],12:[function(t,e,r){"use strict";r.byteLength=function(t){var e=f(t),r=e[0],n=e[1];return 3*(r+n)/4-n},r.toByteArray=function(t){var e,r,n=f(t),a=n[0],o=n[1],u=new s(function(t,e,r){return 3*(e+r)/4-r}(0,a,o)),l=0,h=o>0?a-4:a;for(r=0;r<h;r+=4)e=i[t.charCodeAt(r)]<<18|i[t.charCodeAt(r+1)]<<12|i[t.charCodeAt(r+2)]<<6|i[t.charCodeAt(r+3)],u[l++]=e>>16&255,u[l++]=e>>8&255,u[l++]=255&e;2===o&&(e=i[t.charCodeAt(r)]<<2|i[t.charCodeAt(r+1)]>>4,u[l++]=255&e);1===o&&(e=i[t.charCodeAt(r)]<<10|i[t.charCodeAt(r+1)]<<4|i[t.charCodeAt(r+2)]>>2,u[l++]=e>>8&255,u[l++]=255&e);return u},r.fromByteArray=function(t){for(var e,r=t.length,i=r%3,s=[],a=16383,o=0,u=r-i;o<u;o+=a)s.push(l(t,o,o+a>u?u:o+a));1===i?(e=t[r-1],s.push(n[e>>2]+n[e<<4&63]+"==")):2===i&&(e=(t[r-2]<<8)+t[r-1],s.push(n[e>>10]+n[e>>4&63]+n[e<<2&63]+"="));return s.join("")};for(var n=[],i=[],s="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,u=a.length;o<u;++o)n[o]=a[o],i[a.charCodeAt(o)]=o;function f(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");return-1===r&&(r=e),[r,r===e?0:4-r%4]}function l(t,e,r){for(var i,s,a=[],o=e;o<r;o+=3)i=(t[o]<<16&16711680)+(t[o+1]<<8&65280)+(255&t[o+2]),a.push(n[(s=i)>>18&63]+n[s>>12&63]+n[s>>6&63]+n[63&s]);return a.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},{}],13:[function(t,e,r){(function(e){(function(){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
"use strict";var e=t("base64-js"),n=t("ieee754");r.Buffer=a,r.SlowBuffer=function(t){+t!=t&&(t=0);return a.alloc(+t)},r.INSPECT_MAX_BYTES=50;var i=**********;function s(t){if(t>i)throw new RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return e.__proto__=a.prototype,e}function a(t,e,r){if("number"==typeof t){if("string"==typeof e)throw new TypeError('The "string" argument must be of type string. Received type number');return f(t)}return o(t,e,r)}function o(t,e,r){if("string"==typeof t)return function(t,e){"string"==typeof e&&""!==e||(e="utf8");if(!a.isEncoding(e))throw new TypeError("Unknown encoding: "+e);var r=0|g(t,e),n=s(r),i=n.write(t,e);i!==r&&(n=n.slice(0,i));return n}(t,e);if(ArrayBuffer.isView(t))return l(t);if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(X(t,ArrayBuffer)||t&&X(t.buffer,ArrayBuffer))return function(t,e,r){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw new RangeError('"length" is outside of buffer bounds');var n;n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r);return n.__proto__=a.prototype,n}(t,e,r);if("number"==typeof t)throw new TypeError('The "value" argument must not be of type number. Received type number');var n=t.valueOf&&t.valueOf();if(null!=n&&n!==t)return a.from(n,e,r);var i=function(t){if(a.isBuffer(t)){var e=0|h(t.length),r=s(e);return 0===r.length||t.copy(r,0,0,e),r}if(void 0!==t.length)return"number"!=typeof t.length||k(t.length)?s(0):l(t);if("Buffer"===t.type&&Array.isArray(t.data))return l(t.data)}(t);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return a.from(t[Symbol.toPrimitive]("string"),e,r);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function u(t){if("number"!=typeof t)throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('The value "'+t+'" is invalid for option "size"')}function f(t){return u(t),s(t<0?0:0|h(t))}function l(t){for(var e=t.length<0?0:0|h(t.length),r=s(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}function h(t){if(t>=i)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i.toString(16)+" bytes");return 0|t}function g(t,e){if(a.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||X(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return j(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return D(t).length;default:if(i)return n?-1:j(t).length;e=(""+e).toLowerCase(),i=!0}}function c(t,e,r){var n=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return P(this,e,r);case"utf8":case"utf-8":return T(this,e,r);case"ascii":return R(this,e,r);case"latin1":case"binary":return U(this,e,r);case"base64":return I(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return C(this,e,r);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function d(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function p(t,e,r,n,i){if(0===t.length)return-1;if("string"==typeof r?(n=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),k(r=+r)&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(i)return-1;r=t.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof e&&(e=a.from(e,n)),a.isBuffer(e))return 0===e.length?-1:y(t,e,r,n,i);if("number"==typeof e)return e&=255,"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):y(t,[e],r,n,i);throw new TypeError("val must be string, number or Buffer")}function y(t,e,r,n,i){var s,a=1,o=t.length,u=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return-1;a=2,o/=2,u/=2,r/=2}function f(t,e){return 1===a?t[e]:t.readUInt16BE(e*a)}if(i){var l=-1;for(s=r;s<o;s++)if(f(t,s)===f(e,-1===l?0:s-l)){if(-1===l&&(l=s),s-l+1===u)return l*a}else-1!==l&&(s-=s-l),l=-1}else for(r+u>o&&(r=o-u),s=r;s>=0;s--){for(var h=!0,g=0;g<u;g++)if(f(t,s+g)!==f(e,g)){h=!1;break}if(h)return s}return-1}function m(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var s=e.length;n>s/2&&(n=s/2);for(var a=0;a<n;++a){var o=parseInt(e.substr(2*a,2),16);if(k(o))return a;t[r+a]=o}return a}function b(t,e,r,n){return N(j(e,t.length-r),t,r,n)}function v(t,e,r,n){return N(function(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(e),t,r,n)}function w(t,e,r,n){return v(t,e,r,n)}function E(t,e,r,n){return N(D(e),t,r,n)}function S(t,e,r,n){return N(function(t,e){for(var r,n,i,s=[],a=0;a<t.length&&!((e-=2)<0);++a)n=(r=t.charCodeAt(a))>>8,i=r%256,s.push(i),s.push(n);return s}(e,t.length-r),t,r,n)}function I(t,r,n){return 0===r&&n===t.length?e.fromByteArray(t):e.fromByteArray(t.slice(r,n))}function T(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var s,a,o,u,f=t[i],l=null,h=f>239?4:f>223?3:f>191?2:1;if(i+h<=r)switch(h){case 1:f<128&&(l=f);break;case 2:128==(192&(s=t[i+1]))&&(u=(31&f)<<6|63&s)>127&&(l=u);break;case 3:s=t[i+1],a=t[i+2],128==(192&s)&&128==(192&a)&&(u=(15&f)<<12|(63&s)<<6|63&a)>2047&&(u<55296||u>57343)&&(l=u);break;case 4:s=t[i+1],a=t[i+2],o=t[i+3],128==(192&s)&&128==(192&a)&&128==(192&o)&&(u=(15&f)<<18|(63&s)<<12|(63&a)<<6|63&o)>65535&&u<1114112&&(l=u)}null===l?(l=65533,h=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),i+=h}return function(t){var e=t.length;if(e<=A)return String.fromCharCode.apply(String,t);var r="",n=0;for(;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=A));return r}(n)}r.kMaxLength=i,a.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()}catch(t){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),"undefined"!=typeof Symbol&&null!=Symbol.species&&a[Symbol.species]===a&&Object.defineProperty(a,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1}),a.poolSize=8192,a.from=function(t,e,r){return o(t,e,r)},a.prototype.__proto__=Uint8Array.prototype,a.__proto__=Uint8Array,a.alloc=function(t,e,r){return function(t,e,r){return u(t),t<=0?s(t):void 0!==e?"string"==typeof r?s(t).fill(e,r):s(t).fill(e):s(t)}(t,e,r)},a.allocUnsafe=function(t){return f(t)},a.allocUnsafeSlow=function(t){return f(t)},a.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==a.prototype},a.compare=function(t,e){if(X(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),X(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(t)||!a.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,i=0,s=Math.min(r,n);i<s;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);var r;if(void 0===e)for(e=0,r=0;r<t.length;++r)e+=t[r].length;var n=a.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var s=t[r];if(X(s,Uint8Array)&&(s=a.from(s)),!a.isBuffer(s))throw new TypeError('"list" argument must be an Array of Buffers');s.copy(n,i),i+=s.length}return n},a.byteLength=g,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)d(this,e,e+1);return this},a.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)d(this,e,e+3),d(this,e+1,e+2);return this},a.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)d(this,e,e+7),d(this,e+1,e+6),d(this,e+2,e+5),d(this,e+3,e+4);return this},a.prototype.toString=function(){var t=this.length;return 0===t?"":0===arguments.length?T(this,0,t):c.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(t){if(!a.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){var t="",e=r.INSPECT_MAX_BYTES;return t=this.toString("hex",0,e).replace(/(.{2})/g,"$1 ").trim(),this.length>e&&(t+=" ... "),"<Buffer "+t+">"},a.prototype.compare=function(t,e,r,n,i){if(X(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return-1;if(e>=r)return 1;if(this===t)return 0;for(var s=(i>>>=0)-(n>>>=0),o=(r>>>=0)-(e>>>=0),u=Math.min(s,o),f=this.slice(n,i),l=t.slice(e,r),h=0;h<u;++h)if(f[h]!==l[h]){s=f[h],o=l[h];break}return s<o?-1:o<s?1:0},a.prototype.includes=function(t,e,r){return-1!==this.indexOf(t,e,r)},a.prototype.indexOf=function(t,e,r){return p(this,t,e,r,!0)},a.prototype.lastIndexOf=function(t,e,r){return p(this,t,e,r,!1)},a.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-e;if((void 0===r||r>i)&&(r=i),t.length>0&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var s=!1;;)switch(n){case"hex":return m(this,t,e,r);case"utf8":case"utf-8":return b(this,t,e,r);case"ascii":return v(this,t,e,r);case"latin1":case"binary":return w(this,t,e,r);case"base64":return E(this,t,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,t,e,r);default:if(s)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),s=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var A=4096;function R(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}function U(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}function P(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",s=e;s<r;++s)i+=_(t[s]);return i}function C(t,e,r){for(var n=t.slice(e,r),i="",s=0;s<n.length;s+=2)i+=String.fromCharCode(n[s]+256*n[s+1]);return i}function O(t,e,r){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>r)throw new RangeError("Trying to access beyond buffer length")}function M(t,e,r,n,i,s){if(!a.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<s)throw new RangeError('"value" argument is out of bounds');if(r+n>t.length)throw new RangeError("Index out of range")}function x(t,e,r,n,i,s){if(r+n>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function B(t,e,r,i,s){return e=+e,r>>>=0,s||x(t,0,r,4),n.write(t,e,r,i,23,4),r+4}function L(t,e,r,i,s){return e=+e,r>>>=0,s||x(t,0,r,8),n.write(t,e,r,i,52,8),r+8}a.prototype.slice=function(t,e){var r=this.length;(t=~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),(e=void 0===e?r:~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return n.__proto__=a.prototype,n},a.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||O(t,e,this.length);for(var n=this[t],i=1,s=0;++s<e&&(i*=256);)n+=this[t+s]*i;return n},a.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||O(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},a.prototype.readUInt8=function(t,e){return t>>>=0,e||O(t,1,this.length),this[t]},a.prototype.readUInt16LE=function(t,e){return t>>>=0,e||O(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUInt16BE=function(t,e){return t>>>=0,e||O(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUInt32LE=function(t,e){return t>>>=0,e||O(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},a.prototype.readUInt32BE=function(t,e){return t>>>=0,e||O(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||O(t,e,this.length);for(var n=this[t],i=1,s=0;++s<e&&(i*=256);)n+=this[t+s]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},a.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||O(t,e,this.length);for(var n=e,i=1,s=this[t+--n];n>0&&(i*=256);)s+=this[t+--n]*i;return s>=(i*=128)&&(s-=Math.pow(2,8*e)),s},a.prototype.readInt8=function(t,e){return t>>>=0,e||O(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},a.prototype.readInt16LE=function(t,e){t>>>=0,e||O(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},a.prototype.readInt16BE=function(t,e){t>>>=0,e||O(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},a.prototype.readInt32LE=function(t,e){return t>>>=0,e||O(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,e){return t>>>=0,e||O(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readFloatLE=function(t,e){return t>>>=0,e||O(t,4,this.length),n.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,e){return t>>>=0,e||O(t,4,this.length),n.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,e){return t>>>=0,e||O(t,8,this.length),n.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,e){return t>>>=0,e||O(t,8,this.length),n.read(this,t,!1,52,8)},a.prototype.writeUIntLE=function(t,e,r,n){(t=+t,e>>>=0,r>>>=0,n)||M(this,t,e,r,Math.pow(2,8*r)-1,0);var i=1,s=0;for(this[e]=255&t;++s<r&&(i*=256);)this[e+s]=t/i&255;return e+r},a.prototype.writeUIntBE=function(t,e,r,n){(t=+t,e>>>=0,r>>>=0,n)||M(this,t,e,r,Math.pow(2,8*r)-1,0);var i=r-1,s=1;for(this[e+i]=255&t;--i>=0&&(s*=256);)this[e+i]=t/s&255;return e+r},a.prototype.writeUInt8=function(t,e,r){return t=+t,e>>>=0,r||M(this,t,e,1,255,0),this[e]=255&t,e+1},a.prototype.writeUInt16LE=function(t,e,r){return t=+t,e>>>=0,r||M(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeUInt16BE=function(t,e,r){return t=+t,e>>>=0,r||M(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeUInt32LE=function(t,e,r){return t=+t,e>>>=0,r||M(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},a.prototype.writeUInt32BE=function(t,e,r){return t=+t,e>>>=0,r||M(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var i=Math.pow(2,8*r-1);M(this,t,e,r,i-1,-i)}var s=0,a=1,o=0;for(this[e]=255&t;++s<r&&(a*=256);)t<0&&0===o&&0!==this[e+s-1]&&(o=1),this[e+s]=(t/a>>0)-o&255;return e+r},a.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var i=Math.pow(2,8*r-1);M(this,t,e,r,i-1,-i)}var s=r-1,a=1,o=0;for(this[e+s]=255&t;--s>=0&&(a*=256);)t<0&&0===o&&0!==this[e+s+1]&&(o=1),this[e+s]=(t/a>>0)-o&255;return e+r},a.prototype.writeInt8=function(t,e,r){return t=+t,e>>>=0,r||M(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},a.prototype.writeInt16LE=function(t,e,r){return t=+t,e>>>=0,r||M(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeInt16BE=function(t,e,r){return t=+t,e>>>=0,r||M(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeInt32LE=function(t,e,r){return t=+t,e>>>=0,r||M(this,t,e,4,**********,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},a.prototype.writeInt32BE=function(t,e,r){return t=+t,e>>>=0,r||M(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeFloatLE=function(t,e,r){return B(this,t,e,!0,r)},a.prototype.writeFloatBE=function(t,e,r){return B(this,t,e,!1,r)},a.prototype.writeDoubleLE=function(t,e,r){return L(this,t,e,!0,r)},a.prototype.writeDoubleBE=function(t,e,r){return L(this,t,e,!1,r)},a.prototype.copy=function(t,e,r,n){if(!a.isBuffer(t))throw new TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i=n-r;if(this===t&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(e,r,n);else if(this===t&&r<e&&e<n)for(var s=i-1;s>=0;--s)t[s+e]=this[s+r];else Uint8Array.prototype.set.call(t,this.subarray(r,n),e);return i},a.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw new TypeError("Unknown encoding: "+n);if(1===t.length){var i=t.charCodeAt(0);("utf8"===n&&i<128||"latin1"===n)&&(t=i)}}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;var s;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(s=e;s<r;++s)this[s]=t;else{var o=a.isBuffer(t)?t:a.from(t,n),u=o.length;if(0===u)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(s=0;s<r-e;++s)this[s+e]=o[s%u]}return this};var q=/[^+/0-9A-Za-z-_]/g;function _(t){return t<16?"0"+t.toString(16):t.toString(16)}function j(t,e){var r;e=e||1/0;for(var n=t.length,i=null,s=[],a=0;a<n;++a){if((r=t.charCodeAt(a))>55295&&r<57344){if(!i){if(r>56319){(e-=3)>-1&&s.push(239,191,189);continue}if(a+1===n){(e-=3)>-1&&s.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&s.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(e-=3)>-1&&s.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;s.push(r)}else if(r<2048){if((e-=2)<0)break;s.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;s.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;s.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return s}function D(t){return e.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(q,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function N(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length||i>=t.length);++i)e[i+r]=t[i];return i}function X(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}function k(t){return t!=t}}).call(this)}).call(this,t("buffer").Buffer)},{"base64-js":12,buffer:13,ieee754:15}],14:[function(t,e,r){(function(t){(function(){var r;r="undefined"!=typeof window?window:void 0!==t?t:"undefined"!=typeof self?self:{},e.exports=r}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],15:[function(t,e,r){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
r.read=function(t,e,r,n,i){var s,a,o=8*i-n-1,u=(1<<o)-1,f=u>>1,l=-7,h=r?i-1:0,g=r?-1:1,c=t[e+h];for(h+=g,s=c&(1<<-l)-1,c>>=-l,l+=o;l>0;s=256*s+t[e+h],h+=g,l-=8);for(a=s&(1<<-l)-1,s>>=-l,l+=n;l>0;a=256*a+t[e+h],h+=g,l-=8);if(0===s)s=1-f;else{if(s===u)return a?NaN:1/0*(c?-1:1);a+=Math.pow(2,n),s-=f}return(c?-1:1)*a*Math.pow(2,s-n)},r.write=function(t,e,r,n,i,s){var a,o,u,f=8*s-i-1,l=(1<<f)-1,h=l>>1,g=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,c=n?0:s-1,d=n?1:-1,p=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(o=isNaN(e)?1:0,a=l):(a=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-a))<1&&(a--,u*=2),(e+=a+h>=1?g/u:g*Math.pow(2,1-h))*u>=2&&(a++,u/=2),a+h>=l?(o=0,a=l):a+h>=1?(o=(e*u-1)*Math.pow(2,i),a+=h):(o=e*Math.pow(2,h-1)*Math.pow(2,i),a=0));i>=8;t[r+c]=255&o,c+=d,o/=256,i-=8);for(a=a<<i|o,f+=i;f>0;t[r+c]=255&a,c+=d,a/=256,f-=8);t[r+c-d]|=128*p}},{}],16:[function(t,e,r){
/*! @name m3u8-parser @version 4.5.2 @license Apache-2.0 */
"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=t("@babel/runtime/helpers/inheritsLoose"),i=t("@videojs/vhs-utils/cjs/stream.js"),s=t("@babel/runtime/helpers/extends"),a=t("@babel/runtime/helpers/assertThisInitialized"),o=t("@videojs/vhs-utils/cjs/decode-b64-to-uint8-array.js");function u(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var f=u(n),l=u(i),h=u(s),g=u(a),c=u(o),d=function(t){function e(){var e;return(e=t.call(this)||this).buffer="",e}return f.default(e,t),e.prototype.push=function(t){var e;for(this.buffer+=t,e=this.buffer.indexOf("\n");e>-1;e=this.buffer.indexOf("\n"))this.trigger("data",this.buffer.substring(0,e)),this.buffer=this.buffer.substring(e+1)},e}(l.default),p=function(t){for(var e,r=t.split(new RegExp('(?:^|,)((?:[^=]*)=(?:"[^"]*"|[^,]*))')),n={},i=r.length;i--;)""!==r[i]&&((e=/([^=]*)=(.*)/.exec(r[i]).slice(1))[0]=e[0].replace(/^\s+|\s+$/g,""),e[1]=e[1].replace(/^\s+|\s+$/g,""),e[1]=e[1].replace(/^['"](.*)['"]$/g,"$1"),n[e[0]]=e[1]);return n},y=function(t){function e(){var e;return(e=t.call(this)||this).customParsers=[],e.tagMappers=[],e}f.default(e,t);var r=e.prototype;return r.push=function(t){var e,r,n=this;0!==(t=t.trim()).length&&("#"===t[0]?this.tagMappers.reduce((function(e,r){var n=r(t);return n===t?e:e.concat([n])}),[t]).forEach((function(t){for(var i=0;i<n.customParsers.length;i++)if(n.customParsers[i].call(n,t))return;if(0===t.indexOf("#EXT"))if(t=t.replace("\r",""),e=/^#EXTM3U/.exec(t))n.trigger("data",{type:"tag",tagType:"m3u"});else{if(e=/^#EXTINF:?([0-9\.]*)?,?(.*)?$/.exec(t))return r={type:"tag",tagType:"inf"},e[1]&&(r.duration=parseFloat(e[1])),e[2]&&(r.title=e[2]),void n.trigger("data",r);if(e=/^#EXT-X-TARGETDURATION:?([0-9.]*)?/.exec(t))return r={type:"tag",tagType:"targetduration"},e[1]&&(r.duration=parseInt(e[1],10)),void n.trigger("data",r);if(e=/^#ZEN-TOTAL-DURATION:?([0-9.]*)?/.exec(t))return r={type:"tag",tagType:"totalduration"},e[1]&&(r.duration=parseInt(e[1],10)),void n.trigger("data",r);if(e=/^#EXT-X-VERSION:?([0-9.]*)?/.exec(t))return r={type:"tag",tagType:"version"},e[1]&&(r.version=parseInt(e[1],10)),void n.trigger("data",r);if(e=/^#EXT-X-MEDIA-SEQUENCE:?(\-?[0-9.]*)?/.exec(t))return r={type:"tag",tagType:"media-sequence"},e[1]&&(r.number=parseInt(e[1],10)),void n.trigger("data",r);if(e=/^#EXT-X-DISCONTINUITY-SEQUENCE:?(\-?[0-9.]*)?/.exec(t))return r={type:"tag",tagType:"discontinuity-sequence"},e[1]&&(r.number=parseInt(e[1],10)),void n.trigger("data",r);if(e=/^#EXT-X-PLAYLIST-TYPE:?(.*)?$/.exec(t))return r={type:"tag",tagType:"playlist-type"},e[1]&&(r.playlistType=e[1]),void n.trigger("data",r);if(e=/^#EXT-X-BYTERANGE:?([0-9.]*)?@?([0-9.]*)?/.exec(t))return r={type:"tag",tagType:"byterange"},e[1]&&(r.length=parseInt(e[1],10)),e[2]&&(r.offset=parseInt(e[2],10)),void n.trigger("data",r);if(e=/^#EXT-X-ALLOW-CACHE:?(YES|NO)?/.exec(t))return r={type:"tag",tagType:"allow-cache"},e[1]&&(r.allowed=!/NO/.test(e[1])),void n.trigger("data",r);if(e=/^#EXT-X-MAP:?(.*)$/.exec(t)){if(r={type:"tag",tagType:"map"},e[1]){var s=p(e[1]);if(s.URI&&(r.uri=s.URI),s.BYTERANGE){var a=s.BYTERANGE.split("@"),o=a[0],u=a[1];r.byterange={},o&&(r.byterange.length=parseInt(o,10)),u&&(r.byterange.offset=parseInt(u,10))}}n.trigger("data",r)}else if(e=/^#EXT-X-STREAM-INF:?(.*)$/.exec(t)){if(r={type:"tag",tagType:"stream-inf"},e[1]){if(r.attributes=p(e[1]),r.attributes.RESOLUTION){var f=r.attributes.RESOLUTION.split("x"),l={};f[0]&&(l.width=parseInt(f[0],10)),f[1]&&(l.height=parseInt(f[1],10)),r.attributes.RESOLUTION=l}r.attributes.BANDWIDTH&&(r.attributes.BANDWIDTH=parseInt(r.attributes.BANDWIDTH,10)),r.attributes["PROGRAM-ID"]&&(r.attributes["PROGRAM-ID"]=parseInt(r.attributes["PROGRAM-ID"],10))}n.trigger("data",r)}else{if(e=/^#EXT-X-MEDIA:?(.*)$/.exec(t))return r={type:"tag",tagType:"media"},e[1]&&(r.attributes=p(e[1])),void n.trigger("data",r);if(e=/^#EXT-X-ENDLIST/.exec(t))n.trigger("data",{type:"tag",tagType:"endlist"});else if(e=/^#EXT-X-DISCONTINUITY/.exec(t))n.trigger("data",{type:"tag",tagType:"discontinuity"});else{if(e=/^#EXT-X-PROGRAM-DATE-TIME:?(.*)$/.exec(t))return r={type:"tag",tagType:"program-date-time"},e[1]&&(r.dateTimeString=e[1],r.dateTimeObject=new Date(e[1])),void n.trigger("data",r);if(e=/^#EXT-X-KEY:?(.*)$/.exec(t))return r={type:"tag",tagType:"key"},e[1]&&(r.attributes=p(e[1]),r.attributes.IV&&("0x"===r.attributes.IV.substring(0,2).toLowerCase()&&(r.attributes.IV=r.attributes.IV.substring(2)),r.attributes.IV=r.attributes.IV.match(/.{8}/g),r.attributes.IV[0]=parseInt(r.attributes.IV[0],16),r.attributes.IV[1]=parseInt(r.attributes.IV[1],16),r.attributes.IV[2]=parseInt(r.attributes.IV[2],16),r.attributes.IV[3]=parseInt(r.attributes.IV[3],16),r.attributes.IV=new Uint32Array(r.attributes.IV))),void n.trigger("data",r);if(e=/^#EXT-X-START:?(.*)$/.exec(t))return r={type:"tag",tagType:"start"},e[1]&&(r.attributes=p(e[1]),r.attributes["TIME-OFFSET"]=parseFloat(r.attributes["TIME-OFFSET"]),r.attributes.PRECISE=/YES/.test(r.attributes.PRECISE)),void n.trigger("data",r);if(e=/^#EXT-X-CUE-OUT-CONT:?(.*)?$/.exec(t))return r={type:"tag",tagType:"cue-out-cont"},e[1]?r.data=e[1]:r.data="",void n.trigger("data",r);if(e=/^#EXT-X-CUE-OUT:?(.*)?$/.exec(t))return r={type:"tag",tagType:"cue-out"},e[1]?r.data=e[1]:r.data="",void n.trigger("data",r);if(e=/^#EXT-X-CUE-IN:?(.*)?$/.exec(t))return r={type:"tag",tagType:"cue-in"},e[1]?r.data=e[1]:r.data="",void n.trigger("data",r);n.trigger("data",{type:"tag",data:t.slice(4)})}}}else n.trigger("data",{type:"comment",text:t.slice(1)})})):this.trigger("data",{type:"uri",uri:t}))},r.addParser=function(t){var e=this,r=t.expression,n=t.customType,i=t.dataParser,s=t.segment;"function"!=typeof i&&(i=function(t){return t}),this.customParsers.push((function(t){if(r.exec(t))return e.trigger("data",{type:"custom",data:i(t),customType:n,segment:s}),!0}))},r.addTagMapper=function(t){var e=t.expression,r=t.map;this.tagMappers.push((function(t){return e.test(t)?r(t):t}))},e}(l.default),m=function(t){function e(){var e;(e=t.call(this)||this).lineStream=new d,e.parseStream=new y,e.lineStream.pipe(e.parseStream);var r,n,i=g.default(e),s=[],a={},o=function(){},u={AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},f=0;e.manifest={allowCache:!0,discontinuityStarts:[],segments:[]};var l=0;return e.parseStream.on("data",(function(t){var e,g;({tag:function(){({"allow-cache":function(){this.manifest.allowCache=t.allowed,"allowed"in t||(this.trigger("info",{message:"defaulting allowCache to YES"}),this.manifest.allowCache=!0)},byterange:function(){var e={};"length"in t&&(a.byterange=e,e.length=t.length,"offset"in t||(t.offset=l)),"offset"in t&&(a.byterange=e,e.offset=t.offset),l=e.offset+e.length},endlist:function(){this.manifest.endList=!0},inf:function(){"mediaSequence"in this.manifest||(this.manifest.mediaSequence=0,this.trigger("info",{message:"defaulting media sequence to zero"})),"discontinuitySequence"in this.manifest||(this.manifest.discontinuitySequence=0,this.trigger("info",{message:"defaulting discontinuity sequence to zero"})),t.duration>0&&(a.duration=t.duration),0===t.duration&&(a.duration=.01,this.trigger("info",{message:"updating zero segment duration to a small value"})),this.manifest.segments=s},key:function(){if(t.attributes)if("NONE"!==t.attributes.METHOD)if(t.attributes.URI){if("urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed"===t.attributes.KEYFORMAT){return-1===["SAMPLE-AES","SAMPLE-AES-CTR","SAMPLE-AES-CENC"].indexOf(t.attributes.METHOD)?void this.trigger("warn",{message:"invalid key method provided for Widevine"}):("SAMPLE-AES-CENC"===t.attributes.METHOD&&this.trigger("warn",{message:"SAMPLE-AES-CENC is deprecated, please use SAMPLE-AES-CTR instead"}),"data:text/plain;base64,"!==t.attributes.URI.substring(0,23)?void this.trigger("warn",{message:"invalid key URI provided for Widevine"}):t.attributes.KEYID&&"0x"===t.attributes.KEYID.substring(0,2)?void(this.manifest.contentProtection={"com.widevine.alpha":{attributes:{schemeIdUri:t.attributes.KEYFORMAT,keyId:t.attributes.KEYID.substring(2)},pssh:c.default(t.attributes.URI.split(",")[1])}}):void this.trigger("warn",{message:"invalid key ID provided for Widevine"}))}t.attributes.METHOD||this.trigger("warn",{message:"defaulting key method to AES-128"}),n={method:t.attributes.METHOD||"AES-128",uri:t.attributes.URI},void 0!==t.attributes.IV&&(n.iv=t.attributes.IV)}else this.trigger("warn",{message:"ignoring key declaration without URI"});else n=null;else this.trigger("warn",{message:"ignoring key declaration without attribute list"})},"media-sequence":function(){isFinite(t.number)?this.manifest.mediaSequence=t.number:this.trigger("warn",{message:"ignoring invalid media sequence: "+t.number})},"discontinuity-sequence":function(){isFinite(t.number)?(this.manifest.discontinuitySequence=t.number,f=t.number):this.trigger("warn",{message:"ignoring invalid discontinuity sequence: "+t.number})},"playlist-type":function(){/VOD|EVENT/.test(t.playlistType)?this.manifest.playlistType=t.playlistType:this.trigger("warn",{message:"ignoring unknown playlist type: "+t.playlist})},map:function(){r={},t.uri&&(r.uri=t.uri),t.byterange&&(r.byterange=t.byterange)},"stream-inf":function(){this.manifest.playlists=s,this.manifest.mediaGroups=this.manifest.mediaGroups||u,t.attributes?(a.attributes||(a.attributes={}),h.default(a.attributes,t.attributes)):this.trigger("warn",{message:"ignoring empty stream-inf attributes"})},media:function(){if(this.manifest.mediaGroups=this.manifest.mediaGroups||u,t.attributes&&t.attributes.TYPE&&t.attributes["GROUP-ID"]&&t.attributes.NAME){var r=this.manifest.mediaGroups[t.attributes.TYPE];r[t.attributes["GROUP-ID"]]=r[t.attributes["GROUP-ID"]]||{},e=r[t.attributes["GROUP-ID"]],(g={default:/yes/i.test(t.attributes.DEFAULT)}).default?g.autoselect=!0:g.autoselect=/yes/i.test(t.attributes.AUTOSELECT),t.attributes.LANGUAGE&&(g.language=t.attributes.LANGUAGE),t.attributes.URI&&(g.uri=t.attributes.URI),t.attributes["INSTREAM-ID"]&&(g.instreamId=t.attributes["INSTREAM-ID"]),t.attributes.CHARACTERISTICS&&(g.characteristics=t.attributes.CHARACTERISTICS),t.attributes.FORCED&&(g.forced=/yes/i.test(t.attributes.FORCED)),e[t.attributes.NAME]=g}else this.trigger("warn",{message:"ignoring incomplete or missing media group"})},discontinuity:function(){f+=1,a.discontinuity=!0,this.manifest.discontinuityStarts.push(s.length)},"program-date-time":function(){void 0===this.manifest.dateTimeString&&(this.manifest.dateTimeString=t.dateTimeString,this.manifest.dateTimeObject=t.dateTimeObject),a.dateTimeString=t.dateTimeString,a.dateTimeObject=t.dateTimeObject},targetduration:function(){!isFinite(t.duration)||t.duration<0?this.trigger("warn",{message:"ignoring invalid target duration: "+t.duration}):this.manifest.targetDuration=t.duration},totalduration:function(){!isFinite(t.duration)||t.duration<0?this.trigger("warn",{message:"ignoring invalid total duration: "+t.duration}):this.manifest.totalDuration=t.duration},start:function(){t.attributes&&!isNaN(t.attributes["TIME-OFFSET"])?this.manifest.start={timeOffset:t.attributes["TIME-OFFSET"],precise:t.attributes.PRECISE}:this.trigger("warn",{message:"ignoring start declaration without appropriate attribute list"})},"cue-out":function(){a.cueOut=t.data},"cue-out-cont":function(){a.cueOutCont=t.data},"cue-in":function(){a.cueIn=t.data}}[t.tagType]||o).call(i)},uri:function(){a.uri=t.uri,s.push(a),this.manifest.targetDuration&&!("duration"in a)&&(this.trigger("warn",{message:"defaulting segment duration to the target duration"}),a.duration=this.manifest.targetDuration),n&&(a.key=n),a.timeline=f,r&&(a.map=r),a={}},comment:function(){},custom:function(){t.segment?(a.custom=a.custom||{},a.custom[t.customType]=t.data):(this.manifest.custom=this.manifest.custom||{},this.manifest.custom[t.customType]=t.data)}})[t.type].call(i)})),e}f.default(e,t);var r=e.prototype;return r.push=function(t){this.lineStream.push(t)},r.end=function(){this.lineStream.push("\n")},r.addParser=function(t){this.parseStream.addParser(t)},r.addTagMapper=function(t){this.parseStream.addTagMapper(t)},e}(l.default);r.LineStream=d,r.ParseStream=y,r.Parser=m},{"@babel/runtime/helpers/assertThisInitialized":6,"@babel/runtime/helpers/extends":7,"@babel/runtime/helpers/inheritsLoose":8,"@videojs/vhs-utils/cjs/decode-b64-to-uint8-array.js":10,"@videojs/vhs-utils/cjs/stream.js":11}],"p2p-media-loader-hlsjs":[function(t,e,r){"use strict";
/**
 * @license Apache-2.0
 * Copyright 2018 Novage LLC.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),i=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||n(e,t,r)};function s(t){t&&t.config&&t.config.loader&&"function"==typeof t.config.loader.getEngine&&a(t,t.config.loader.getEngine())}function a(t,e){t.on("hlsFragChanged",((t,r)=>{const n=r.frag,i=2!==n.byteRange.length?void 0:{offset:n.byteRange[0],length:n.byteRange[1]-n.byteRange[0]};e.setPlayingSegment(n.url,i,n.start,n.duration)})),t.on("hlsDestroying",(async()=>{await e.destroy()})),t.on("hlsError",((r,n)=>{if("bufferStalledError"===n.details){const r=void 0===t.media?t.el_:t.media;if(void 0===r)return;e.setPlayingSegmentByCurrentTime(r.currentTime)}}))}Object.defineProperty(r,"__esModule",{value:!0}),r.initJwPlayer=r.initMediaElementJsPlayer=r.initVideoJsHlsJsPlugin=r.initVideoJsContribHlsJsPlayer=r.initFlowplayerHlsJsPlayer=r.initClapprPlayer=r.initHlsJsPlayer=r.version=void 0,r.version="0.6.2",i(t("./engine"),r),i(t("./segment-manager"),r),r.initHlsJsPlayer=s,r.initClapprPlayer=function(t){t.on("play",(()=>{const e=t.core.getCurrentPlayback();e._hls&&!e._hls._p2pm_linitialized&&(e._hls._p2pm_linitialized=!0,s(t.core.getCurrentPlayback()._hls))}))},r.initFlowplayerHlsJsPlayer=function(t){t.on("ready",(()=>s(t.engine.hlsjs?t.engine.hlsjs:t.engine.hls)))},r.initVideoJsContribHlsJsPlayer=function(t){t.ready((()=>{const e=t.tech_.options_;e&&e.hlsjsConfig&&e.hlsjsConfig.loader&&"function"==typeof e.hlsjsConfig.loader.getEngine&&a(t.tech_,e.hlsjsConfig.loader.getEngine())}))},r.initVideoJsHlsJsPlugin=function(){null!=videojs&&null!=videojs.Html5Hlsjs&&videojs.Html5Hlsjs.addHook("beforeinitialize",((t,e)=>{e.config&&e.config.loader&&"function"==typeof e.config.loader.getEngine&&a(e,e.config.loader.getEngine())}))},r.initMediaElementJsPlayer=function(t){t.addEventListener("hlsFragChanged",(e=>{const r=t.hlsPlayer;if(r&&r.config&&r.config.loader&&"function"==typeof r.config.loader.getEngine){const t=r.config.loader.getEngine();if(e.data&&e.data.length>1){const r=e.data[1].frag,n=2!==r.byteRange.length?void 0:{offset:r.byteRange[0],length:r.byteRange[1]-r.byteRange[0]};t.setPlayingSegment(r.url,n,r.start,r.duration)}}})),t.addEventListener("hlsDestroying",(async()=>{const e=t.hlsPlayer;if(e&&e.config&&e.config.loader&&"function"==typeof e.config.loader.getEngine){const t=e.config.loader.getEngine();await t.destroy()}})),t.addEventListener("hlsError",(e=>{const r=t.hlsPlayer;if(r&&r.config&&r.config.loader&&"function"==typeof r.config.loader.getEngine&&void 0!==e.data&&"bufferStalledError"===e.data.details){r.config.loader.getEngine().setPlayingSegmentByCurrentTime(r.media.currentTime)}}))},r.initJwPlayer=function(t,e){const r=setInterval((()=>{t.hls&&t.hls.config&&(clearInterval(r),Object.assign(t.hls.config,e),s(t.hls))}),200)}},{"./engine":2,"./segment-manager":5}]},{},[1]);
