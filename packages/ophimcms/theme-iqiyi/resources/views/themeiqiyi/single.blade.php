@extends('themes::themeiqiyi.layout')

@php
    use Illuminate\Support\Str;
        $watch_url = '';
        if (!$currentMovie->is_copyright && count($currentMovie->episodes) && $currentMovie->episodes[0]['link'] != '') {
            $watch_url = $currentMovie->episodes
                ->sortBy([['server', 'asc']])
                ->groupBy('server')
                ->first()
                ->sortByDesc('name', SORT_NATURAL)
                ->groupBy('name')
                ->last()
                ->sortByDesc('type')
                ->first()
                ->getUrl();
        }
@endphp

@section('single_top')
    @include('themes::themeiqiyi.details.index')
@endsection

@section('content')
    <section class="content">
        <div class="container">
            @if ($currentMovie->showtimes && $currentMovie->showtimes != '')
                <div class="row mb-3">
                    <div class="focus-promotion">
                        <div class="detail-notes">
                            <a class="button" role="button" tabindex="0">
                                <img alt=""
                                     src="{{ asset('themes/iqiyi/img/icons/icon-calendar.svg') }}">
                                <span>Lịch chiếu: </span>
                                <span>{!! $currentMovie->showtimes !!}</span>
                            </a>
                        </div>
                    </div>
                </div>
            @endif
            @if ($currentMovie->showtimes && $currentMovie->showtimes != '')
                <div class="row">
                    <div class="focus-promotion">
                        <div class="detail-notes">
                            <a class="button" role="button" tabindex="0">
                                <img alt=""
                                     src="{{ asset('themes/iqiyi/img/icons/icon-notification.svg') }}">
                                <span>Thông báo: </span>
                                <span>{!! $currentMovie->notify !!}</span>
                            </a>
                        </div>
                    </div>
                </div>
            @endif
            <div class="row">
                <div class="col">
                    <ul class="nav nav-pills mb-3 tab-content-ul" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="pills-propose-tab" data-bs-toggle="pill"
                                    data-bs-target="#pills-propose" type="button" role="tab"
                                    aria-controls="pills-propose" aria-selected="false">Đề xuất cho bạn
                            </button>
                        </li>
                        @php
                            $keyTab = 1;
                        @endphp
                        @foreach ($currentMovie->episodes->sortBy([['server', 'asc']])->groupBy('server') as $server => $data)
                            <li class="nav-item" role="presentation">
                                <button class="nav-link"
                                        id="pills-firm-tab-{{$keyTab}}"
                                        data-bs-toggle="pill"
                                        data-bs-target="#pills-firm-{{$keyTab}}"
                                        type="button"
                                        role="tab"
                                        aria-controls="pills-firm-{{$keyTab}}"
                                        aria-selected="{{$keyTab == 1 ? 'true' : 'false'}}"
                                >
                                    Danh sách tập <span>{{ $server }}</span>
                                </button>
                            </li>
                            @php
                                $keyTab++;
                            @endphp
                        @endforeach
                    </ul>
                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-propose" role="tabpanel"
                             aria-labelledby="pills-propose-tab">
                            @include('themes::themeiqiyi.details.propose')
                        </div>
                        @php
                            $keyTabPane = 1;
                        @endphp
                        @foreach ($currentMovie->episodes->sortBy([['server', 'asc']])->groupBy('server') as $server => $data)
                            <div class="tab-pane fade"
                                 id="pills-firm-{{$keyTabPane}}" role="tabpanel"
                                 aria-labelledby="pills-firm-tab-{{$keyTabPane}}">
                                <div class="video-list-wrapper">
                                    @foreach ($data->sortByDesc('name', SORT_NATURAL)->groupBy('name') as $name => $item)
                                        <a href="{{ $item->sortByDesc('type')->first()->getUrl() }}" class="video-item"
                                           title="{{ $currentMovie->name . ' - Tập'. $name }}">
                                            <div class="video-item-img">
                                                <img src="{{$currentMovie->getPosterUrl()}}"
                                                     alt="" class="desc-img">
                                                <div class="video-item-img-layer"></div>
                                                <div class="wrap " role="button" aria-label="play-button" tabindex="0"
                                                     rseat="0"
                                                     data-pb="block=album_information&amp;r=3513185601796900&amp;a=play&amp;rpage=album">
                                                    <svg width="60px" height="60px" viewBox="0 0 60 60" version="1.1"
                                                         xmlns="http://www.w3.org/2000/svg"
                                                         xmlns:xlink="http://www.w3.org/1999/xlink" class="play-button">
                                                        <g id="Btn/Play/Normal" stroke="none" stroke-width="1"
                                                           fill="none" fill-rule="evenodd">
                                                            <circle id="bg" fill="#1CC749" cx="30" cy="30"
                                                                    r="30"></circle>
                                                            <path
                                                                d="M35.7461509,22.4942263 L45.1405996,36.5858994 C46.059657,37.9644855 45.6871354,39.8270935 44.3085493,40.7461509 C43.8157468,41.0746859 43.2367237,41.25 42.6444487,41.25 L23.8555513,41.25 C22.198697,41.25 20.8555513,39.9068542 20.8555513,38.25 C20.8555513,37.657725 21.0308654,37.078702 21.3594004,36.5858994 L30.7538491,22.4942263 C31.6729065,21.1156403 33.5355145,20.7431187 34.9141006,21.662176 C35.2436575,21.8818806 35.5264463,22.1646695 35.7461509,22.4942263 Z"
                                                                id="Triangle" fill="#FFFFFF"
                                                                transform="translate(33.250000, 30.000000) rotate(-270.000000) translate(-33.250000, -30.000000) ">
                                                            </path>
                                                        </g>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="video-item-name">
                                                {{ $currentMovie->name . ' - Tập '. $name }}
                                            </div>
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                            @php
                                $keyTabPane++;
                            @endphp
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('css_page')
    <link rel='stylesheet' href='{{asset('themes/iqiyi/css/details/index.css')}}?ver=1.0.1' type='text/css'/>
@stop

@section('js_page')
    <script src="{{asset('themes/iqiyi/js/details.js')}}?ver=1.0.1"></script>
    @if ($currentMovie->trailer_url && strpos($currentMovie->trailer_url, 'youtube'))
        @php
            parse_str( parse_url( $currentMovie->trailer_url, PHP_URL_QUERY ), $my_array_of_vars );
            $video_id = $my_array_of_vars['v'];
        @endphp
        <script>
            toroflixPublic.trailer = "<iframe width=\"560\" height=\"315\" src=\"https:\/\/www.youtube.com\/embed\/{{$video_id}}\" frameborder=\"0\" allow=\"autoplay\" allow=\"encrypted-media\" allowfullscreen><\/iframe>"
        </script>
        <div class="Modal-Box Ttrailer">
            <div class="Modal-Content">
                <span class="Modal-Close Button"><i class="fa-solid fa-xmark"></i></span>
            </div>
            <i class="AAOverlay"></i>
        </div>
    @endif

    <script src="{{ asset('themes/iqiyi/plugins/jquery-raty/jquery.raty.js') }}"></script>
    <link href="{{ asset('themes/iqiyi/plugins/jquery-raty/jquery.raty.css') }}" rel="stylesheet" type="text/css"/>

    <script>
        var rated = false;
        $('#movies-rating-star').raty({
            score: {{ $currentMovie->getRatingStar() }},
            number: 10,
            numberMax: 10,
            hints: ['quá tệ', 'tệ', 'không hay', 'không hay lắm', 'bình thường', 'xem được', 'có vẻ hay', 'hay',
                'rất hay', 'siêu phẩm'
            ],
            starOff: '/themes/iqiyi/plugins/jquery-raty/images/star-off.png',
            starOn: '/themes/iqiyi/plugins/jquery-raty/images/star-on.png',
            starHalf: '/themes/iqiyi/plugins/jquery-raty/images/star-half.png',
            click: function (score, evt) {
                if (rated) return
                fetch("{{ route('movie.rating', ['movie' => $currentMovie->slug]) }}", {
                    method: 'POST',
                    headers: {
                        "Content-Type": "application/json",
                        'X-CSRF-TOKEN': document.querySelector(
                            'meta[name="csrf-token"]')
                            .getAttribute(
                                'content')
                    },
                    body: JSON.stringify({
                        rating: score
                    })
                });
                rated = true;
                $('#movies-rating-star').data('raty').readOnly(true);
                $('#movies-rating-msg').html(`Bạn đã đánh giá ${score} sao cho phim này!`);
            }
        });
    </script>

    {!! setting('site_scripts_facebook_sdk') !!}
@stop
