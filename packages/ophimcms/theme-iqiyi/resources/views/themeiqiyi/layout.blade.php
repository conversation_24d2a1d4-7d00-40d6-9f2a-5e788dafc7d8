@extends('themes::themeiqiyi.master')

@php
    $menu = \Ophim\Core\Models\Menu::getTree();
    $tops = Cache::remember('site.movies.tops', setting('site_cache_ttl', 5 * 60), function () {
        $lists = preg_split('/[\n\r]+/', get_theme_option('hotest'));
        $data = [];
        foreach ($lists as $list) {
            if (trim($list)) {
                $list = explode('|', $list);
                [$label, $relation, $field, $val, $sortKey, $alg, $limit, $template] = array_merge($list, ['Phim hot', '', 'type', 'series', 'view_total', 'desc', 4, 'top_thumb']);
                try {
                    $data[] = [
                        'label' => $label,
                        'template' => $template,
                        'data' => \Ophim\Core\Models\Movie::when($relation, function ($query) use ($relation, $field, $val) {
                            $query->whereHas($relation, function ($rel) use ($field, $val) {
                                $rel->where($field, $val);
                            });
                        })
                            ->when(!$relation, function ($query) use ($field, $val) {
                                $query->where($field, $val);
                            })
                            ->orderBy($sortKey, $alg)
                            ->limit($limit)
                            ->get(),
                    ];
                } catch (\Exception $e) {
                    # code
                }
            }
        }
        return $data;
    });
@endphp

@push('header')
    <!-- font Roboto -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- bootstrap -->
    <link href="{{asset('themes/iqiyi/plugins/bootstrap/css/bootstrap.min.css')}}?ver=1.0.1" rel="stylesheet">
    <!-- fontawesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- splidejs -->
    <link href="{{asset('themes/iqiyi/css/splide.min.css')}}?ver=1.0.1" rel="stylesheet">
    <!-- css base -->
    <link rel='stylesheet' href='{{asset('themes/iqiyi/css/global-styles.css')}}?ver=1.0.1' type='text/css'/>
    <!-- Page style -->
    @yield('css_page')
@endpush

@section('body')
    <div class="app">
        @include("themes::themeiqiyi.inc.header")
        <div class="main">
            @if (get_theme_option('ads_header'))
                <div class="Main Container">
                    {!! get_theme_option('ads_header') !!}
                </div>
            @endif
            @yield('home_page_slider_poster')
            @yield('single_top')
            <div class="Main Container">
                @yield('home_page_slider_thumb')
                <div class="TpRwCont ">
                    <main class="{{ \Request::route()->getName() }}">
                        @yield('content')
                    </main>
                </div>
            </div>
        </div>
    </div>
@endsection


@section('footer')
    {!! get_theme_option('footer') !!}

    @if (get_theme_option('ads_catfish'))
        {!! get_theme_option('ads_catfish') !!}
    @endif

    @include('themes::themeiqiyi.inc.footer')
@endsection

@push('scripts')
    <script src='{{asset('themes/iqiyi/js/jquery.js')}}?ver=3.0.0'></script>
    <script src='{{asset('themes/iqiyi/js/splide.min.js')}}?ver=1.0.1'></script>
    <script src='{{asset('themes/iqiyi/plugins/bootstrap/js/bootstrap.bundle.min.js')}}?ver=1.0.1'></script>
    <script src='{{asset('themes/iqiyi/js/functions.js')}}?ver=1.0.1'></script>
    <script src='{{asset('themes/iqiyi/js/main.js') }}?ver=1.0.1'></script>
    <script type="text/javascript" id='funciones_public_sol-js-extra'>
        var toroflixPublic = {"url":"/","nonce":"7a0fde296e","trailer":"","noItemsAvailable":"No entries found","selectAll":"Select all","selectNone":"Select none","searchplaceholder":"Click here to search","loadingData":"Still loading data...","viewmore":"View more","id":"","type":"","report_text_reportForm":"Report Form","report_text_message":"Message","report_text_send":"SEND","report_text_has_send":"the report has been sent","playerAutomaticSlider":"1"};
    </script>
    <!-- Page script -->
    @yield('js_page')
    {!! setting('site_scripts_google_analytics') !!}
@endpush
