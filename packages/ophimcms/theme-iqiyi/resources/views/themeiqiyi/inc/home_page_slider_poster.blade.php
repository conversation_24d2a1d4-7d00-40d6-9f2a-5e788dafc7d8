<section id="slider">
    <div class="container">
        <div class="slider__column splide">
            <div class="splide__track">
                {{-- start list --}}
                <ul class="splide__list">
                    @foreach($home_page_slider_poster['data'] as $movie)
                        <li class="splide__slide">
                            <a href="{{$movie->getUrl()}}">
                                <img src="{{$movie->getPosterUrl()}}" alt="Slider">
                                <div class="crs-content">
                                    <div class="crs-content__title">
                                        <h2>{{$movie->name}}</h2>
                                    </div>
                                    <span class="crs-content__top">
                                    <div class="top">
                                        @switch($movie->status)
                                            @case("ongoing")
                                                Đang chiếu
                                                @break
                                            @case("completed")
                                                Trọn bộ
                                                @break
                                            @default
                                                Trailer
                                        @endswitch
                                    </div>
                                    {{$movie->origin_name}}
                                </span>
                                    <div class="crs-content__infor">
                                        <div class="rate">
                                            <i class="fas fa-star"></i> {{$movie->getRatingStar()}}
                                        </div>
                                        <div class="year after-item">
                                            {{$movie->publish_year}}
                                        </div>
                                        <div class="week after-item">
                                            {{$movie->episode_time}}
                                        </div>
                                        <div class="episode_number after-item">
                                            @if($movie->type == 'single')
                                                Phim lẻ
                                            @else
                                                Phim bộ
                                            @endif
                                        </div>
                                    </div>
                                    <div class="crs-content__category">Đạo diễn:
                                        @if(count($movie->directors))
                                            {!! $movie->directors->map(function ($director) {
                                               return '<a href="' .
                                                   $director->getUrl() .
                                                   '" tite="Đạo diễn ' .
                                                   $director->name .
                                                   '">' .
                                                   $director->name .
                                                   '</a>';
                                           })->implode(', ') !!}
                                        @endif
                                    </div>
                                    <div class="crs-content__category">Thể loại:
                                        @if(count($movie->categories))
                                            {!! $movie->categories->map(function ($category) {
                                               return '<a href="' .
                                                   $category->getUrl() .
                                                   '" tite="Thể loại ' .
                                                   $category->name .
                                                   '">' .
                                                   $category->name .
                                                   '</a>';
                                           })->implode(', ') !!}
                                        @endif
                                    </div>
                                    <div
                                        class="crs-content__desc">{!! mb_substr(strip_tags($movie->content),0,160, "utf-8") !!}
                                        ...
                                    </div>
                                    <div class="sc-a4176019-0 gHWgi option-button">
                                        <div class="sc-88e580be-0 gUElsb">
                                            <div class="wrap" href="{{$movie->getUrl()}}">
                                                <i class="icon-play"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </li>
                    @endforeach
                </ul>
                {{-- end list --}}
            </div>
        </div>
    </div>
</section>
