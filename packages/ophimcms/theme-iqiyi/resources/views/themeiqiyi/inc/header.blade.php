@php
    $logo = setting('site_logo', '');
    $brand = setting('site_brand', '');
    $title = $title ?? setting('site_homepage_title', '');
@endphp
<div class="header">
    <div class="header-container">
        <div class="row wrap-header">
            <div class="header__left">
                <ul class="navbar__list header-respon-sidebar mr-3">
                    <li class="navbar__list--item">
                        <i class="fa-solid fa-bars"></i>
                    </li>
                </ul>
                <div class="header__logo">
                    <a href="/" title="{{ $title }}" rel="home">
                        @if ($logo)
                            {!! $logo !!}
                        @elseif($brand)
                            {!! $brand !!}
                        @else
                            <img src="{{ asset('themes/iqiyi/img/logo.svg') }}" alt="">
                        @endif
                    </a>
                </div>
                <ul class="navbar__list">
                    @foreach ($menu as $item)
                        @if (count($item['children']))
                            <li id="menu-item-{{ $item['id'] }}" class="navbar__list--item">
                                <a href="{{ $item['link'] }}" class="navbar__list--item--link active">
                                    {{ $item['name'] }}
                                </a>
                                <ul class="dropdown-nav">
                                    @foreach ($item['children'] as $children)
                                        <li id="menu-item-{{ $children['id'] }}" class="dropdown-nav__item">
                                            <a href="{{ $children['link'] }}">{{ $children['name'] }}</a>
                                        </li>
                                    @endforeach
                                </ul>
                            </li>
                        @else
                            <li id="menu-item-{{ $item['id'] }}" class="navbar__list--item">
                                <a href="{{ $item['link'] }}" class="navbar__list--item--link active">
                                    {{ $item['name'] }}
                                </a>
                            </li>
                        @endif
                    @endforeach
                </ul>
                <ul class="navbar__list respon-navbar-left">
                    <li class="navbar__list--item">
                        <a href="#" class="navbar__list--item--link">
                            Lướt xem <i class="fa-solid fa-sort-down icon-down"></i>
                        </a>
                        <ul class="dropdown-nav">
                            @foreach ($menu as $item)
                                <li class="dropdown-nav__item">
                                    <a href="{{ $item['link'] }}">{{ $item['name'] }}</a>
                                </li>
                            @endforeach
                        </ul>
                    </li>
                </ul>
            </div>
            <div class="header__right">
                <div class="header-search">
                    <form id="search-form" action="/" method="get">
                        <input type="text" placeholder="Tìm kiếm phim" name="search" id="key-search"
                               autocomplete="off" value="{{ request('search') }}">
                        <button class="btn-search" type="submit">
                            <div class="search-btn" role="button" tabindex="0" aria-label="search button">
                                <i class="fa-solid fa-magnifying-glass"></i>
                            </div>
                        </button>
                        <span class="line"></span>
                    </form>
                </div>
            </div>
        </div>
        <div class="row">
            <ul class="respon-navbar-second-row">
                @foreach ($menu as $key => $item)
                    <li class="">
                        <a class="{{ $key == 0 ? "link-active" : "" }}" href="{{ $item['link'] }}">{{ $item['name'] }}</a>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>
</div>
<div class="menu-box">
    <div class="menu-background"></div>
    <div class="menu-content">
        <div class="help-center-content">
            <div class="menu-user">
                <div class="wrap-user-infor">
                    <a href="#" class="user-content">
                        <img src="{{asset('themes/iqiyi/img/avatar_default.png')}}" alt="" class="user-avatar">
                        <div class="user-name line-clamp-1">Đăng nhập/ Đăng ký</div>
                    </a>
                </div>
            </div>
            <div class="menu-channel">
                <h3 class="channel-item">
                    <a href="#" class="channel-item--link">Lịch sử xem</a>
                </h3>
                <h3 class="channel-item line">
                    <a href="#" class="channel-item--link ">Sưu tập của tôi</a>
                </h3>
                <h3 class="channel-item">
                    <a href="#" class="channel-item--link">Giới thiệu đi</a>
                </h3>
            </div>
        </div>
    </div>
</div>
