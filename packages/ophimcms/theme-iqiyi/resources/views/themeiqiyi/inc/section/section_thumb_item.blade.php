@php
    $rating_star = $movie->getRatingStar() ?? '0.0';
    $type = $movie->type == 'single' ? 'Phim lẻ' : 'Phim bộ';
    $publish_year = $movie->publish_year ?? date('Y');
    $content = trim(preg_replace('/\s\s+/', ' ', $movie->content)) ?? '';
    $content = str_replace('"', "'", $content);
    $thumb_url = $movie->getPosterUrl() ?? '';
    $name = $movie->name ?? '';
    $url = $movie->getUrl() ?? '#';
    $categories = '';
    if(count($movie->categories)) {
        $categories = $movie->categories->map(function ($category) {
           return $category->name;
       })->implode(', ');
    }
@endphp

<li class="splide__slide">
    <a href="{{$movie->getUrl()}}">
        <div class="splide__item"
             data-wrap_data='{"rate": "{{ $rating_star }}", "type" : "{{ $type }}", "year" : "{{ $publish_year }}", "desc":"{{ $content }}", "img_url" : "{{ $thumb_url }}", "title": "{{ $name }}" , "linkF" : "{{ $url }}", "firm_cate" : "{{ $categories }}"}'>
            <div class="splide__img-wrap">
                <img src="{{$movie->getThumbUrl()}}" alt="Slider" class="splide__img">
                <div class="episodes">{{$movie->episode_current}}</div>
            </div>
            <div class="splide__item-title">
                {{$movie->name}}
            </div>
        </div>
    </a>
</li>
