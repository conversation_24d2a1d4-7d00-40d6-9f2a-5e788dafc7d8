<section class="container">
    <div class="firm-by-category">
        <h2 class="title-category">{{$item['label']}}</h2>
        <div class="list-item">
            @foreach($item['data'] as $movie)
                @php
                    $rating_star = $movie->getRatingStar() ?? '0.0';
                    $type = $movie->type == 'single' ? 'Phim lẻ' : 'Phim bộ';
                    $publish_year = $movie->publish_year ?? date('Y');
                    $content = trim(preg_replace('/\s\s+/', ' ', $movie->content)) ?? '';
                    $content = str_replace('"', "'", $content);
                    $thumb_url = $movie->getThumbUrl() ?? '';
                    $name = $movie->name ?? '';
                    $url = $movie->getUrl() ?? '#';
                    $categories = '';
                    if(count($movie->categories)) {
                        $categories = $movie->categories->map(function ($category) {
                           return $category->name;
                       })->implode(', ');
                    }
                @endphp
                <div class="item-wrap">
                    <a href="{{$movie->getUrl()}}" class="item-link"
                       data-wrap_data='{"rate": "{{ $rating_star }}", "type" : "{{ $type }}", "year" : "{{ $publish_year }}", "desc":"{{ $content }}", "img_url" : "{{ $thumb_url }}", "title": "{{ $name }}" , "linkF" : "{{ $url }}", "firm_cate" : "{{ $categories }}"}'
                    >
                        <div class="item-img">
                            <img src="{{$movie->getThumbUrl()}}"
                                 alt="{{$movie->name}}" class="desc-img">
                            <div class="item-img-layer">
                                <div class="update-info-mask">{{$movie->episode_current}}</div>
                            </div>
                        </div>
                        <div class="text-box">
                            <div class="item-title">
                                {{$movie->name}}
                            </div>
                        </div>
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</section>
