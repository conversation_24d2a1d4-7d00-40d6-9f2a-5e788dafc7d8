<section class="banner">
    <div class="wrap-banner">
        <div class="row">
            <div class="col__left">
                <div class="banner-content">
                    <div class="banner-content__title">
                        <h1>{{ $currentMovie->name }}</h1>
                    </div>
                    <span class="banner-content__top">
                            <div class="top">
                                @switch($currentMovie->status)
                                    @case("ongoing")
                                        <PERSON><PERSON> chi<PERSON>u
                                        @break
                                    @case("completed")
                                        Trọn bộ
                                        @break
                                    @default
                                        Trailer
                                @endswitch
                            </div>
                            {{$currentMovie->origin_name}}
                        </span>
                    <div class="banner-content__infor">
                        <div class="rate">
                            <i class="fas fa-star"></i> {{$currentMovie->getRatingStar()}}
                        </div>
                        <div class="year after-item">
                            {{$currentMovie->publish_year}}
                        </div>
                        <div class="week after-item">
                            {{$currentMovie->episode_time}}
                        </div>
                        <div class="episode_number after-item">
                            @if($currentMovie->type == 'single')
                                Phim lẻ
                            @else
                                Phim bộ
                            @endif
                        </div>
                    </div>
                    <div class="banner-info-tag">
                        <span class="key">Đạo diễn:</span>
                        @if(count($currentMovie->directors))
                            {!! $currentMovie->directors->map(function ($director) {
                               return '<span><a href="' .
                                   $director->getUrl() .
                                   '" tite="Đạo diễn ' .
                                   $director->name .
                                   '">' .
                                   $director->name .
                                   '</a></span>';
                           })->implode('<i>,</i> ') !!}
                        @endif
                    </div>
                    <div class="banner-info-tag">
                        <span class="key">Thể loại:</span>
                        @if(count($currentMovie->categories))
                            {!! $currentMovie->categories->map(function ($category) {
                               return '<a href="' .
                                   $category->getUrl() .
                                   '" tite="Thể loại ' .
                                   $category->name .
                                   '">' .
                                   $category->name .
                                   '</a>';
                           })->implode(', ') !!}
                        @endif
                    </div>
                    <div class="banner-info-tag">
                        <span class="key">Diễn viên:</span>
                        @if(count($currentMovie->actors))
                            {!! $currentMovie->actors->map(function ($actor) {
                               return '<a href="' .
                                   $actor->getUrl() .
                                   '" tite="Diễn viên ' .
                                   $actor->name .
                                   '">' .
                                   $actor->name .
                                   '</a>';
                           })->implode(', ') !!}
                        @endif
                    </div>
                    <div class="banner-content__desc line-clamp-3">
                        <span class="key"></span>
                        {!! strip_tags($currentMovie->content) !!}
                        <div class="more-info">
                            <span class="text">Hiển thị thêm</span>
                            <i class="fa-solid fa-chevron-down"></i>
                        </div>
                    </div>
                    <div class="group-btn">
                        @if($watch_url)
                            <a href="{{$watch_url}}" class="btn-item btn-play" title="Xem Phim">
                                <i class="fa-solid fa-play"></i>
                                Xem Phim
                            </a>
                        @endif
                        @if ($currentMovie->trailer_url && strpos($currentMovie->trailer_url, 'youtube'))
                            <a href="javascript:void(0)" id="watch-trailer" title="Xem Trailer" class="btn-item btn-trailer">
                                <i class="fa-brands fa-youtube"></i>
                                Xem Trailer
                            </a>
                        @endif
                            <a href="javascript:void(0)"
                               title="Chia sẻ lên facebook"
                               onclick="window.open('http://www.facebook.com/sharer.php?u={{$currentMovie->getUrl()}}', 'Facebook', 'toolbar=0, status=0, width=650, height=450');"
                               class="btn-item btn-facebook">
                                <i class="fa-brands fa-facebook-f"></i>
                            </a>
                            <a href="javascript:void(0)"
                               title="Chia sẻ lên twitter"
                               onclick="window.open('https://twitter.com/intent/tweet?original_referer={{$currentMovie->getUrl()}}&amp;text={{$currentMovie->name}}&amp;tw_p=tweetbutton&amp;url={{$currentMovie->getUrl()}}', 'Twitter', 'toolbar=0, status=0, width=650, height=450');"
                               class="btn-item btn-twitter">
                                <i class="fa-brands fa-twitter"></i>
                            </a>
                    </div>
                </div>
            </div>
            <div class="col__right">
                <div class="wrap-banner-img">
                    <img src="{{$currentMovie->getPosterUrl()}}" class="banner-img" alt="">
                    <div class="left-layer"></div>
                    <div class="bottom-layer"></div>
                </div>
            </div>
        </div>
    </div>
</section>
