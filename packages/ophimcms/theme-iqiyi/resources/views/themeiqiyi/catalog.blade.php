@extends('themes::themeiqiyi.layout')
@section('content')
    <section class="breadcrumb">
        <div class="wrap-breadcrumb"
             style="background: url({{ asset('themes/iqiyi/img/collection-bg-nomal.png') }}) left center / cover no-repeat;">
            <h2 class="title-category">{{$section_name}}</h2>
        </div>
    </section>
    @if(!count($data))
        <div class="focus-promotion" style="text-align: center;">
            <div class="detail-notes">
                <a class="button" role="button" tabindex="0">
                    <img alt=""
                         src="{{ asset('themes/iqiyi/img/icons/icon-notification.svg') }}">
                    <span>Thông báo: </span>
                    <span>Không có nội dung cho mục này. </span>
                </a>
            </div>
        </div>
    @else
        <section class="list-item">
            @foreach($data as $movie)
                @include("themes::themeiqiyi.inc.section.section_list_item")
            @endforeach
        </section>
        <div class="list-item pagination">
            {{ $data->appends(request()->all())->links('themes::themeiqiyi.inc.pagination') }}
        </div>
    @endif
@endsection

@section('css_page')
    <link rel='stylesheet' href='{{asset('themes/iqiyi/css/list.css')}}?ver=1.0.1' type='text/css'/>
@stop

@section('js_page')
    <script src="{{ asset('themes/iqiyi/js/config-splide.js') }}?ver=1.0.1"></script>
@stop
