{"name": "ophimcms/theme-iqiyi", "description": "<PERSON><PERSON>'s iqiyi theme", "type": "library", "authors": [{"name": "ophimcms"}], "require": {"laravel/framework": "^6|^7|^8", "ckfinder/ckfinder-laravel-package": "v3.5.2.1", "hacoidev/ophim-core": "^1.0.0"}, "license": "MIT", "autoload": {"psr-4": {"Ophim\\ThemeIqiyi\\": "src/", "Ophim\\ThemeIqiyi\\Database\\Factories\\": "database/factories/", "Ophim\\ThemeIqiyi\\Database\\Seeders\\": "database/seeders/"}}, "extra": {"laravel": {"providers": ["Ophim\\ThemeIqiyi\\ThemeIqiyiServiceProvider"]}}, "minimum-stability": "stable"}