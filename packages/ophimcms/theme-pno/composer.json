{"name": "ophimcms/theme-pno", "description": "<PERSON><PERSON>'s pno theme", "type": "library", "authors": [{"name": "ophimcms", "email": "<EMAIL>"}], "require": {"laravel/framework": "^6|^7|^8", "ckfinder/ckfinder-laravel-package": "v3.5.2.1", "hacoidev/ophim-core": "^1.1.0"}, "license": "MIT", "autoload": {"psr-4": {"Ophim\\ThemePno\\": "src/", "Ophim\\ThemePno\\Database\\Factories\\": "database/factories/", "Ophim\\ThemePno\\Database\\Seeders\\": "database/seeders/"}}, "extra": {"laravel": {"providers": ["Ophim\\ThemePno\\ThemePnoServiceProvider"]}}, "minimum-stability": "stable"}