:root {
    --swiper-theme-color: #0088cc;
    --primary-color: #0088cc;
    --secenday-color: #0088c0;
}

.owl-carousel .owl-next,
.owl-carousel .owl-prev {
    background-color: var(--primary-color);
}

a {
    color: var(--primary-color);
}

a:hover {
    color: var(--secenday-color);
}

.vjs-chromecast-button .vjs-icon-placeholder {
    width: 18px;
    height: 18px;
}

.ribbon {
    width: 110px;
    height: 80px;
    overflow: hidden;
    position: absolute;
    background: url(/themes/pno/assets/theme/default/images/lock.png);
    background-repeat: no-repeat;
    overflow: hidden;
}

.ribbon-top-right {
    bottom: 0px;
    left: 0px;
}

.tv-ribbon {
    top: 10px;
    left: 5px;
    position: absolute;
    z-index: 6;
    padding: 2px 11px;
    background-color: #ffe22e;
    color: #383737;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    font-size: 14px;
    font-weight: bold;
}

.modal-header {
    background: var(--primary-color);
    border-bottom: transparent;
    color: #fff;
}

.bg_img {
    background-image: url(/themes/pno/uploads/bg/bg.jpg);
}

#myFooter {
    background-color: #232323;
}

#myFooter .footer-copyright {
    background-color: #151414;
}

.slider-content {
    height: 420px;
}

#slider {
    border-radius: 10px;
}

.button_filter {
    color: #fff;
    background-color: #08c;
    border-color: #9e9e9e;
    cursor: pointer;
    display: inline-block;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1.5rem;
    line-height: 1.9;
    border-radius: 0.55rem;
    transition: all 144ms ease-in-out;
}
.button_filter:hover {
    opacity: 0.8;
}
.filter-box select {
    margin: 2px;
    color: #fff;
    background-color: rgba(51, 51, 51, 0.285);
    border-color: #9e9e9e;
    cursor: pointer;
    display: inline-block;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1.5rem;
    line-height: 1.9;
    border-radius: 0.55rem;
    transition: all 144ms ease-in-out;
}
.box-rating {
    padding: 5px 0;
    color: #fff;
}
.block_watch {
    padding: 5px 0;
    text-align: center;
}
.btn_watch:hover {
    opacity: 0.8;
}
