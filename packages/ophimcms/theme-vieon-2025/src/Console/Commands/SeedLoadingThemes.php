<?php

namespace Ophim\ThemeVieon2025\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use Ophim\ThemeVieon2025\Database\Seeders\LoadingThemesSeeder;

class SeedLoadingThemes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'theme-vieon-2025:seed-loading-themes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed loading themes data for VieON 2025 theme';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $this->info('Seeding loading themes...');

        try {
            $seeder = new LoadingThemesSeeder();
            $seeder->run();

            $this->info('Loading themes seeded successfully!');
            return 0;
        } catch (Exception $e) {
            $this->error('Error seeding loading themes: ' . $e->getMessage());
            return 1;
        }
    }
}
