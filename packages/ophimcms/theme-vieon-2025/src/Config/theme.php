<?php

return [
    'name' => 'Vieon 2025',
    'description' => 'Theme Vieon 2025 for OphimCMS',
    'version' => '1.0.0',
    'author' => 'OphimCMS',
    'author_url' => 'https://ophimcms.com',
    'theme_url' => 'https://ophimcms.com/themes/vieon-2025',
    'theme_demo' => 'https://demo.ophimcms.com/vieon-2025',
    'theme_documentation' => 'https://docs.ophimcms.com/themes/vieon-2025',
    'theme_support' => 'https://ophimcms.com/support',
    'theme_license' => 'MIT',
    'theme_license_url' => 'https://opensource.org/licenses/MIT',
    'theme_requires' => [
        'laravel/framework' => '^6|^7|^8',
        'hacoidev/ophim-core' => '^1.0.0',
    ],
    'theme_assets' => [
        'css' => [
            'themes/vieon-2025/css/style.css',
        ],
        'js' => [
            'themes/vieon-2025/js/app.js',
        ],
    ],
    'theme_options' => [
        'home_page_slider_poster' => [
            'type' => 'text',
            'label' => 'Homepage Slider Poster',
            'description' => 'Format: Label|Relation|Field|Value|SortKey|SortOrder|Limit',
            'default' => 'Phim đề cử||is_recommended|1|updated_at|desc|10',
            'placeholder' => 'Phim đề cử||is_recommended|1|updated_at|desc|10'
        ],
        'trending' => [
            'type' => 'text',
            'label' => 'Trending Movies',
            'description' => 'Format: Label|Relation|Field|Value|SortKey|SortOrder|Limit',
            'default' => 'Thịnh hành||view_week|>|0|view_week|desc|12',
            'placeholder' => 'Thịnh hành||view_week|>|0|view_week|desc|12'
        ],
        'latest' => [
            'type' => 'text',
            'label' => 'Latest Movies',
            'description' => 'Format: Label|Relation|Field|Value|SortKey|SortOrder|Limit',
            'default' => 'Mới nhất||updated_at|>|0|updated_at|desc|12',
            'placeholder' => 'Mới nhất||updated_at|>|0|updated_at|desc|12'
        ],
        'sections' => [
            'type' => 'textarea',
            'label' => 'Homepage Sections',
            'description' => 'Format: Label|Relation|Field|Value|SortKey|SortOrder|Limit|Link|Template (one per line)',
            'default' => "Phim bộ||type|series|view_total|desc|12|/phim-bo|movie-slider\nPhim chiếu rạp||type|single|view_total|desc|12|/phim-chieu-rap|movie-slider\nAnime||categories|name|Anime|view_total|desc|12|/anime|movie-slider\nTop 10||rating_star|>|0|rating_star|desc|10|/top-10|top-movies",
            'placeholder' => "Phim bộ||type|series|view_total|desc|12|/phim-bo|movie-slider\nPhim chiếu rạp||type|single|view_total|desc|12|/phim-chieu-rap|movie-slider"
        ],
        'per_page_limit' => [
            'type' => 'number',
            'label' => 'Movies per page',
            'description' => 'Number of movies to show per page',
            'default' => 24,
            'min' => 1,
            'max' => 100
        ],
        'movie_related_limit' => [
            'type' => 'number',
            'label' => 'Related movies limit',
            'description' => 'Number of related movies to show',
            'default' => 12,
            'min' => 1,
            'max' => 50
        ],
        'footer_style' => [
            'type' => 'select',
            'label' => 'Footer Style',
            'description' => 'Choose footer layout style',
            'default' => 'compact',
            'options' => [
                'compact' => 'Compact (Centered, minimal)',
                'full' => 'Full (4 columns, detailed)'
            ]
        ],
        'menu_tabs' => [
            'type' => 'textarea',
            'label' => 'Menu Tabs Configuration',
            'description' => 'Configure menu tabs. Format: tab_id|label|icon|type|limit (one per line)',
            'default' => "categories|Thể Loại|fas fa-th-large|categories|16\nhot|Phim Hot|fas fa-fire|hot|10\nregions|Quốc Gia|fas fa-globe|regions|16\ntypes|Định Dạng|fas fa-film|types|4",
            'placeholder' => "categories|Thể Loại|fas fa-th-large|categories|16\nhot|Phim Hot|fas fa-fire|hot|10"
        ]
    ]
]; 