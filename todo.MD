1. T<PERSON>o/cập nhật migration mở rộng bảng users (avatar, <PERSON><PERSON><PERSON>, gi<PERSON><PERSON> t<PERSON>)
2. Tạo migration bảng password_resets (nếu chưa có)
3. Tạo migration bảng settings (user_id, language, subtitle, ...)
4. Tạo migration bảng comments (user_id, movie_id, content, parent_id, ...)
5. Tạo migration bảng favorites (user_id, movie_id, ...)
6. Tạo migration bảng histories (user_id, movie_id, episode, position, ...)
7. Tạo migration bảng ratings (user_id, movie_id, score, review, ...)
8. Tạo/cập nhật các model: User, Setting, Comment, Favorite, History, Rating
9. Tạo/cập nhật controller: AuthController, PasswordController, EmailVerificationController, TwoFactorController, ProfileController, SettingController, <PERSON>mment<PERSON>ontroller, FavoriteController, HistoryController, RatingController
10. Tạo/cập nhật blade UI/UX cho: đ<PERSON><PERSON> ký, đ<PERSON><PERSON> nhập, quê<PERSON> mật khẩu, x<PERSON><PERSON> thực email, x<PERSON><PERSON> thực 2 lớp, đ<PERSON>i mật khẩu, xóa tài khoản, profile, settings, comment, favorite, history, rating
11. Tạo/cập nhật route cho toàn bộ chức năng user (web.php)
12. Tích hợp middleware bảo vệ route, xác thực email, 2FA, rate limit
13. Custom toàn bộ UI/UX theo style VieON (Tailwind, AlpineJS, hiệu ứng động, responsive)
14. Tạo toast notification cho các thao tác user
15. Test toàn bộ flow user, sửa bug, tối ưu, viết tài liệu hướng dẫn
