<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNewfieldLinksIndexTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('links_index', function (Blueprint $table) {
            $table->json('json_response')->nullable();
            $table->string('messenger')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('links_index', function (Blueprint $table) {
            $table->dropColumn('json_response')->nullable();
            $table->dropColumn('messenger')->nullable();
        });
    }
}
