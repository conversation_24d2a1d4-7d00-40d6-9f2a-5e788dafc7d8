<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLinksIndexTable extends Migration
{
    public function up()
    {
        Schema::create('links_index', function (Blueprint $table) {
            $table->id();
            $table->longText("slug")->nullable();
            $table->integer("status")->default(0)->comment("0 wating 1 runok 2 faild");
            $table->timestamp("updated_at")->nullable();
            $table->timestamp("created_at")->nullable();
        });
    }

    public function down()
    {
        Schema::dropIfExists('links_index');
    }
}
