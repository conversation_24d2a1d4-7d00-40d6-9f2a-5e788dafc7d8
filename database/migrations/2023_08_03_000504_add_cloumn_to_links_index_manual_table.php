<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('links_index_manual', function (Blueprint $table) {
            $table->json('json_response')->nullable();
            $table->string('messenger')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('links_index_manual', function (Blueprint $table) {
            $table->dropColumn('json_response')->nullable();
            $table->dropColumn('messenger')->nullable();
        });
    }
};
