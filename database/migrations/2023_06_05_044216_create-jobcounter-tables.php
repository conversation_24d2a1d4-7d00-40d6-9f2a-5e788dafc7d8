<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateJobcounterTables extends Migration
{
    public function up()
    {
        Schema::create('job_counters', function (Blueprint $table) {
            $table->id();
            $table->date('date');
            $table->unsignedInteger('count')->default(0);
            $table->unsignedInteger('queued_jobs')->default(0);
        });
    }

    public function down()
    {
        Schema::dropIfExists('job_counters');
    }
}
