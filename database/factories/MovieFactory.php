<?php

namespace Database\Factories;

use App\Models\Movie;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class MovieFactory extends Factory
{
    protected $model = Movie::class;

    public function definition()
    {
        $name = $this->faker->sentence(3);
        $origin_name = $this->faker->sentence(2);
        return [
            'name' => $name,
            'origin_name' => $origin_name,
            'slug' => Str::slug($name) . '-' . $this->faker->unique()->numberBetween(1, 9999),
            'content' => $this->faker->paragraph(),
            'type' => 'single',
            'status' => $this->faker->randomElement(['trailer', 'ongoing', 'completed']),
            'thumb_url' => $this->faker->imageUrl(300, 450, 'movies'),
            'poster_url' => $this->faker->imageUrl(300, 450, 'posters'),
            'publish_year' => $this->faker->year(),
            'user_id' => null,
            'user_name' => null,
        ];
    }
} 